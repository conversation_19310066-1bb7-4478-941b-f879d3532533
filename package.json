{"name": "formiqo-ui", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.4.0", "@react-oauth/google": "^0.12.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.18.1", "antd-mask-input": "^2.0.7", "antd-style": "^3.7.1", "axios": "^1.7.2", "chart.js": "^4.4.6", "daisyui": "^4.12.2", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "http-proxy-middleware": "^3.0.0", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.2.0", "react-country-flag": "^3.1.0", "react-day-picker": "^9.3.2", "react-dom": "^18.3.1", "react-ga4": "^2.1.0", "react-google-recaptcha": "^3.1.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.51.5", "react-icons": "^5.2.1", "react-quill": "^2.0.0", "react-responsive": "^10.0.0", "react-router-dom": "^6.23.1", "react-scripts": "^5.0.1", "react-share": "^5.1.0", "react-slideshow-image": "^4.3.2", "react-spinners": "^0.13.8", "react-to-print": "^3.0.2", "styled-components": "^6.1.12", "tailwindcss": "^3.4.4", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}