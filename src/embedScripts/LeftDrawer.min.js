!function(){var n,t=document.createElement("style");t.textContent="body {\n                    display: flex;\n                    justify-content: flex-start;\n                    align-items: center;\n                    height: 100vh;\n                    margin: 0;\n                }\n\n                button {\n                    display: none;\n                }\n\n                .button-container {\n                    position: fixed;\n                    top: 53%;\n                    left: 40px;\n                    transform: translateY(-50%) rotate(-90deg);\n                    transform-origin: left bottom;\n                    z-index: 1001;\n                    white-space: nowrap;\n                    padding: 15px 20px 10px 20px;\n                    background-color: transparent;\n                    border: none;\n                    cursor: pointer;\n                    font-family: 'Exo 2', sans-serif;\n                    font-size: 20px;\n                    font-weight: 500;\n                    letter-spacing: 1px;\n                    line-height: 1;\n                    border-bottom-left-radius: 10px;\n                    border-bottom-right-radius: 10px;\n                    box-shadow: 0px 4px 8px -3px rgba(0,0,0,0.4);\n                }\n\n                .button-container:hover {\n                    opacity: .75;\n                    transition: all .3s ease-out;\n                }\n\n                .drawer-container {\n                    position: fixed;\n                    top: 0;\n                    /*\n                    left: -40%;\n                    width: 40%;\n                    */\n                    height: 100%;\n                    background-color: #fff;\n                    box-shadow: 1px 0 10px rgba(0, 0, 0, 0.07);\n                    transition: left 0.3s ease;\n                    z-index: 1000;\n                }\n\n                .iframe-container {\n                    width: 100%;\n                    height: 100%;\n                    border: none;\n                }\n\n                .close-button-container {\n                    display: none;\n                    justify-content: center;\n                    align-items: center;\n                    position: absolute;\n                    top: 20px;\n                    left: 40%;\n                    background: #f0f0f0;\n                    width: 32px;\n                    height: 32px;\n                    z-index: 1001;\n                    cursor: pointer;\n                    border-top-right-radius: 10px;\n                    border-bottom-right-radius: 10px;\n                    border-top: 1px solid #d1d1d1;\n                    border-right: 1px solid #d1d1d1;\n                    border-bottom: 1px solid #d1d1d1;\n                }\n\n                .close-button-container-mobile {\n                    display: none;\n                    justify-content: center;\n                    align-items: center;\n                    position: absolute;\n                    top: 10px;\n                    right: 20px;\n                    // background: #f0f0f0;\n                    width: 24px;\n                    height: 24px;\n                    z-index: 1001;\n                    cursor: pointer;\n                    border-radius: 5px;\n                    /*\n                    border-top: 1px solid #d1d1d1;\n                    border-right: 1px solid #d1d1d1;\n                    border-bottom: 1px solid #d1d1d1;\n                    */\n                }\n\n                .close-button-container:hover {\n                    opacity: .75;\n                }\n\n                .close-button-icon {\n                    margin-top: -4px;\n                    margin-left: -4px;\n                }\n            ",document.head.appendChild(t);var e=navigator.userAgent,o=window.innerWidth;n=/Mobi|Android/i.test(e)||o<768?"mobile":/iPad|Tablet/i.test(e)||o>=768&&o<1024?"Tablet":o>=1024?"desktop":"unknown";var i,r=document.getElementById("form-container");if(r){var a=r.getAttribute("form");if(a){var d=r.getAttribute("btn");if(d){var l=d.split(" ")[0],s=d.split(" ")[1];(i=document.createElement("div")).className="button-container",i.style.background=l,i.style.color=s;var p=r.getAttribute("btnTxt");i.innerText=p,document.body.appendChild(i)}}var c=document.createElement("div");c.className="drawer-container",c.style.width="mobile"===n?"100%":"40%",c.style.left="mobile"===n?"-100%":"-40%",document.body.appendChild(c);var b=document.createElement("div");b.className="mobile"===n?"close-button-container-mobile":"close-button-container";var m=document.createElement("img");m.className="close-button-icon";var x="data:image/svg+xml;base64,".concat(btoa('\n                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" id="close">\n                                                <path fill="#000" d="M7.05 7.05a1 1 0 0 0 0 1.414L10.586 12 7.05 15.536a1 1 0 1 0 1.414 1.414L12 13.414l3.536 3.536a1 1 0 0 0 1.414-1.414L13.414 12l3.536-3.536a1 1 0 0 0-1.414-1.414L12 10.586 8.464 7.05a1 1 0 0 0-1.414 0Z"></path>\n                                            </svg>\n                                        '));m.src=x,b.appendChild(m),document.body.appendChild(b),i.addEventListener("click",(()=>{var t;"0px"===c.style.left?(b.style.display="none",i.style.display="flex"):(c.style.left="0px",(t=document.createElement("iframe")).src=a,t.className="iframe-container",t.style.width="mobile"===n?"100%":"40%",c.appendChild(t),setTimeout((()=>{b.style.display="flex",i.style.display="none"}),300))})),b.addEventListener("click",(()=>{c.style.left="mobile"===n?"-100%":"-40%",b.style.display="none",i.style.display="flex"})),c.style.left="mobile"===n?"-100%":"-40%"}}();