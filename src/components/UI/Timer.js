import React, { useState, useEffect } from "react";
import { IoTimerOutline } from "react-icons/io5";

const Timer = ({ startTime, setStopped, restart, setRestart }) => {
  const [timeLeft, setTimeLeft] = useState(startTime);

  useEffect(() => {
    if (timeLeft <= 0) {
      setStopped(true);
      if (restart) {
        setTimeLeft(startTime);
        setStopped(false);
        setRestart(false);
        return;
      }
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft((prevTime) => prevTime - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft, restart, setStopped, setRestart, startTime]);

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  };

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        textAlign: "center",
        fontFamily: "Exo 2",
        fontSize: "16px",
        fontWeight: "500",
      }}
    >
      <IoTimerOutline
        style={{ marginRight: "5px", color: "var(--black)", fontSize: "20px" }}
      />
      <div style={{ marginTop: "1px" }}>
        {timeLeft > 0 ? formatTime(timeLeft) : "00:00"}
      </div>
    </div>
  );
};

export default Timer;
