.blackButtonBordered-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--white);
    border: 1px solid var(--black);
    padding: 8px;
    border-radius: 30px;
    cursor: pointer;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 1px 4px;
    position: relative;
    border-bottom: 5px solid var(--black);
}

.blackButtonBordered-container:hover {
    transition: 0.3s all ease-in-out;
    border: .5px solid var(--black);
    margin-top: 5px;
}

.blackButtonBordered-title {
    font-weight: 500;
    font-size: 15px;
    color: var(--black);
    font-family: 'Exo 2', sans-serif;
}
