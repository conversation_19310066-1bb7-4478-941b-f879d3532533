import "./ModalOkButton.css";

const ModalOkButton = ({ title, passive, action, loading, loadingTitle }) => {
  return (
    <div
      className={`modalOkButton-container ${
        passive || loading ? "passive" : ""
      }`}
      onClick={!passive ? action : () => {}}
    >
      <div className="modalOkButton-container-title">
        {loading ? loadingTitle : title}
      </div>
    </div>
  );
};

export default ModalOkButton;
