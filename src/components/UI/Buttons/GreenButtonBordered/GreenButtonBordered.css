.greenButtonBordered-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--green300);
    
    padding: 8px;
    border-radius: 30px;
    cursor: pointer;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 1px 4px;
    position: relative;
    border: 1px solid var(--black);
    border-bottom: 5px solid var(--black);
}

.greenButtonBordered-container:hover {
    transition: 0.15s all ease-in-out;
    border:1px solid var(--black);
    margin-top: 5px;
}

.greenButtonBordered-title {
    font-weight: 600;
    font-size: 15px;
    color: var(--black);
    font-family: 'Exo 2', sans-serif;
}
