import { FaLocationArrow } from "react-icons/fa";
import "./PublishButton.css";

const PublishButton = ({ action, loading }) => {
  return (
    <div
      className={
        loading ? "publishButton-container-disabled" : "publishButton-container"
      }
      onClick={action}
    >
      <div className="publishButton-icon">
        <FaLocationArrow />
      </div>
      <div className="publishButton-title">Publish</div>
    </div>
  );
};

export default PublishButton;
