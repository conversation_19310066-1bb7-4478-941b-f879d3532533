.socialButton-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  border: 1px solid var(--grey200);
  border-radius: 3px;
  cursor: pointer;
  margin-bottom: 15px;
  background-color: #f1f1f1;
  color: #000;
}

.socialButton-container:hover {
  background-color: #fff;
  opacity: 1;
  color: #fff;
  transition: 0.3s all ease-in-out;
}

.socialButton-container:hover .socialButton-title {
  font-weight: 500;
  transition: 0.3s all ease-in-out;
}

.socialButton-google {
  background-color: #dd4b39;
}

.socialButton-google:hover {
  opacity: 0.8;
  transition: 0.3s all ease-in-out;
}

.socialButton-facebook {
  background-color: #3b5897;
}

.socialButton-facebook:hover {
  opacity: 0.8;
  transition: 0.3s all ease-in-out;
}

.socialButton-windows {
  background-color: #2672ec;
}

.socialButton-windows:hover {
  opacity: 0.8;
  transition: 0.3s all ease-in-out;
}

.socialButton-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.socialButton-title {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 15px;
  color: var(--text-dark);
  font-weight: 500;
  font-family: "Exo 2", sans-serif;
}
