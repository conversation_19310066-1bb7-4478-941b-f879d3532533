import "./SocialButton.css";
import google from "../../../../assets/images/icons/google.png";
import facebook from "../../../../assets/images/icons/facebook.png";
import windows from "../../../../assets/images/icons/windows.png";

const SocialButton = ({ social, title, login }) => {
  const socialIcon = () => {
    switch (social) {
      case "google":
        return google;
      case "facebook":
        return facebook;
      case "microsoft":
        return windows;
      default:
        return;
    }
  };

  const socialTitle = () => {
    return title;
  };

  return (
    <div className={`socialButton-container`} onClick={login}>
      <div className="socialButton-icon">
        <img src={socialIcon()} width={28} height={28} alt="socialIcon" />
      </div>
      <div className="socialButton-title">{socialTitle()}</div>
      <div />
    </div>
  );
};

export default SocialButton;
