import { useNavigate } from "react-router-dom";
import { useMainContext } from "../../../context/MainContext";
import "./FormTopMenu.css";
import { pagesWithoutRef } from "../../../pages/form/FormUtil";

const FormTopMenu = ({ selectedItem, form }) => {
  const mainContext = useMainContext();
  const navigate = useNavigate();

  const handleTopMenuAction = (actionItem) => {
    const workspaceUniqueId = mainContext.selectedWorkspace?.uniqueId;
    if (actionItem === "build") {
      navigate(`/workspace/${workspaceUniqueId}/form/${form.id}`, {
        state: {
          form: form,
        },
      });
    }

    if (actionItem === "share") {
      const tempPages = pagesWithoutRef(form.pages);
      form.pages = tempPages;
      navigate(`/workspace/${workspaceUniqueId}/form/${form.id}/share`, {
        state: {
          formId: form.id,
          form: form,
        },
      });
    }

    if (actionItem === "results") {
      const tempPages = pagesWithoutRef(form.pages);
      form.pages = tempPages;
      navigate(`/workspace/${workspaceUniqueId}/form/${form.id}/results`, {
        state: {
          formId: form.id,
          form: form,
        },
      });
    }
  };
  return (
    <div className="form-top-menu-container">
      <div
        onClick={() => handleTopMenuAction("build")}
        className={
          selectedItem === "build"
            ? "form-top-menu-item-selected"
            : "form-top-menu-item"
        }
        style={{ marginRight: "30px" }}
      >
        BUILD
      </div>
      <div
        onClick={() => handleTopMenuAction("share")}
        className={
          selectedItem === "share"
            ? "form-top-menu-item-selected"
            : "form-top-menu-item"
        }
        style={{ marginRight: "30px" }}
      >
        SHARE
      </div>
      {form?.published ? (
        <div
          onClick={() => handleTopMenuAction("results")}
          className={
            selectedItem === "results"
              ? "form-top-menu-item-selected"
              : "form-top-menu-item"
          }
        >
          RESULTS
        </div>
      ) : (
        <div onClick={() => {}} className={"form-top-menu-item-passive"}>
          RESULTS
        </div>
      )}
    </div>
  );
};

export default FormTopMenu;
