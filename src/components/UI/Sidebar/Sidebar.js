import { AiOutlineAppstoreAdd, AiOutlinePlus } from "react-icons/ai";
import "./Sidebar.css";
import { useEffect, useState } from "react";
import CerateWorkspaceModal from "../Modals/CreateWorkspaceModal";
import { useNavigate } from "react-router-dom";
import { useMainContext } from "../../../context/MainContext";
import { useAuth } from "../../../context/AuthContext";
import BlackButton from "../Buttons/BlackButton/BlackButton";

const Sidebar = ({ workspaces, createWorkspace }) => {
  const [buttonLoading, setButtonLoading] = useState(false);
  const [openCreateModal, setOpenCreateModal] = useState(false);
  const [createModalError, setCreateModalError] = useState();
  const [reorderedWorkspaces, setReorderedWorkspaces] = useState([]);

  const mainContext = useMainContext();
  const authContext = useAuth();

  const navigate = useNavigate();

  useEffect(() => {
    const orderedWorkspaces = reorderWorkspaceList(workspaces);
    setReorderedWorkspaces(orderedWorkspaces);
  }, [workspaces, setReorderedWorkspaces]);

  function reorderWorkspaceList(list) {
    let defaultItem = list.find((item) => item.default === true);
    if (defaultItem) {
      let index = list.indexOf(defaultItem);
      list.splice(index, 1);
      list.push(defaultItem);
    }
    return list;
  }

  const onChangeWorkspace = (workspaceId) => {
    const selected = workspaces.filter(
      (workspace) => workspace.id === workspaceId
    )[0];
    mainContext.saveWorkspace(selected);
    navigate(`/workspace/${selected.uniqueId}`);
  };

  const gotoSelectForm = () => {
    navigate(
      `/workspace/${mainContext.selectedWorkspace.uniqueId}/form/select`
    );
  };

  return (
    <div className="sideBar-container">
      <div className="sideBar-create-form">
        <BlackButton title="Create a new form" action={gotoSelectForm} />
      </div>

      <div className="sideBar-menu">
        <div className="sideBar-workspace-title-container">
          {authContext?.authUser?.role !== "USER" && (
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                width: "85%",
                margin: "10px 0",
              }}
            >
              <div style={{ display: "flex" }}>
                <div className="sideBar-workspace-title-icon">
                  <AiOutlineAppstoreAdd />
                </div>
                <div className="sideBar-workspace-title">Workspaces</div>
              </div>

              <div
                className="sideBar-workspace-add-button"
                onClick={() => setOpenCreateModal(true)}
              >
                <div className="sideBar-workspace-add-button-icon">
                  <AiOutlinePlus />
                </div>
              </div>
            </div>
          )}
        </div>
        <ul
          className="sideBar-workspace-item-list"
          style={{ overflow: "auto", height: "calc(100vh - 370px)" }}
        >
          {reorderedWorkspaces.map((workspace, index) => (
            <li
              key={index}
              className={
                mainContext.selectedWorkspace.id === workspace.id
                  ? "sideBar-workspace-item sideBar-workspace-selected-item"
                  : "sideBar-workspace-item"
              }
              onClick={() => onChangeWorkspace(workspace.id)}
            >
              <div className="sideBar-workspace-item-name">
                {workspace.name}
              </div>
              <div className="sideBar-workspace-item-formCount">
                {workspace.formCount}
              </div>
            </li>
          ))}
        </ul>
        {
          authContext.authUser.role === "ADMIN" && <div></div>

          /*
          <div className="sideBar-subscription-container">
            <div>
              <div className="sideBar-subscription-title">
                Responses collected
              </div>
              <div className="sideBar-subscription-progressBar">
                <Progress percent={70} strokeColor="#000" />
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <strong>7</strong>{" "}
                  <div
                    style={{
                      marginLeft: "5px",
                      color: "#717171",
                      fontSize: "12px",
                    }}
                  >
                    {" "}
                    / 10
                  </div>
                </div>
              </div>

              <div className="sideBar-subscription-button">
                Increase response limit
              </div>
            </div>
          </div>
          */
        }
      </div>
      <CerateWorkspaceModal
        open={openCreateModal}
        handleOk={createWorkspace}
        handleClose={() => {
          setOpenCreateModal(false);
          setButtonLoading(false);
        }}
        title="Create a new workspace"
        loading={buttonLoading}
        error={createModalError}
        setError={setCreateModalError}
      />
    </div>
  );
};

export default Sidebar;
