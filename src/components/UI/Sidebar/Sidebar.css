.sideBar-container {
  height: calc(100% - 60px);
  width: 280px;
  position: absolute;
  top: 60px;
  background-color: var(--white);
  /*box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;*/
  z-index: 1;
  border-right: 1px solid var(--grey400);
}

.sideBar-logo-container {
  padding: 15px 24px;
}

.sideBar-menu {
  padding-left: 36px;
  margin-top: 100px;
}

.sideBar-workspace-title-container {
  display: flex;
}

.sideBar-workspace-title-icon {
  font-size: 20px;
  margin-right: 12px;
  color: var(--text-dark);
}

.sideBar-workspace-title {
  color: var(--text-dark);
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 10px;
}

.sideBar-workspace-item {
  display: flex;
  justify-content: space-between;
  padding: 6px 12px;
  font-family: "Exo 2", sans-serif;
  font-size: 15px;
  font-weight: 400;
  color: var(--text-dark);
  cursor: pointer;
  width: 210px;
  margin-bottom: 5px;
}

.sideBar-workspace-selected-item {
  background-color: var(--grey400);
  border-radius: 5px;
  font-weight: 500;
}

.sideBar-workspace-item:hover {
  background-color: var(--bg-light);
  border-radius: 5px;
}

.sideBar-workspace-item-formCount {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  opacity: 0.8;
}

.sideBar-create-form {
  width: 100%;
  padding: 24px;
  position: absolute;
}

.sideBar-workspace-add-button {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  border-radius: 3px;
  margin-top: -5px;
  border: 1px solid var(--black);
  background-color: var(--green600);
}

.sideBar-workspace-add-button-icon {
  font-size: 18px;
  color: var(--text-dark);
}

.sideBar-workspace-add-button:hover {
  transition: 0.15s all;
  border: 1px solid var(--black);
  background-color: var(--green400);
  /*margin-top: -3px;*/
}

.sideBar-workspace-add-button:hover .sideBar-workspace-add-button-icon {
  opacity: 1;
  color: var(--white);
  transition: 0.15s all;
  /* color: var(--text-dark);*/
}

.sideBar-subscription-container {
  width: 280px;
  height: 150px;
  left: 0;
  position: fixed;
  bottom: 0;
  border-top: 1px solid var(--grey300);
  padding: 15px 30px;
}

.sideBar-subscription-title {
  font-size: 14px;
  font-weight: 400;
  font-family: "Exo 2", sans-serif;
}

.sideBar-subscription-progressBar {
  font-size: 14px !important;
}

.sideBar-subscription-button {
  border: 1px solid var(--black);
  background-color: var(--black);
  color: var(--white);
  border-radius: 5px;
  padding: 5px;
  text-align: center;
  margin-top: 20px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 300;
  font-family: "Exo 2", sans-serif;
}

.sideBar-subscription-button:hover {
  border: 1px solid var(--grey200);
  opacity: 0.8;
}

.ant-progress .ant-progress-text.ant-progress-text-outer {
  font-size: 12px !important;
}
