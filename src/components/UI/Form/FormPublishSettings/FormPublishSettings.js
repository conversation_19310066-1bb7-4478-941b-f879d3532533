import "./FormPublishSettings.css";
import { IoPlayOutline } from "react-icons/io5";
import { useEffect, useRef, useState } from "react";
import { getAllWorkspaces } from "../../../../services/http";
import { useMainContext } from "../../../../context/MainContext";
import { FcGoogle } from "react-icons/fc";
import bitcoinImage from "../../../../assets/images/icons/bitcoin.png";
import PublishButton from "../../Buttons/PublishButton/PublishButton";
import { LuChevronRight } from "react-icons/lu";

const FormPublishSettings = ({
  standartForm,
  loading,
  saveChanges,
  setStandartForm,
  usedGoogleAuthenticate,
  setUsedGoogleAuthenticate,
  usedWeb3,
  setUsedWeb3,
  setOpenedPreviewModal,
  formName,
  setOpenedRenameModal,
  publishForm,
  isActivePublishButton,
}) => {
  const [isOpenWorkspaceOptions, setOpenWorkspaceOptions] = useState(false);
  const mainContext = useMainContext();

  const workspaceOptionsRef = useRef();

  useEffect(() => {
    async function getWorkspaceList() {
      await getWorkspaces();
    }
    getWorkspaceList();
  }, []);

  useEffect(() => {
    document.addEventListener("mousedown", handleOutsideClicks);
    return () => {
      document.removeEventListener("mousedown", handleOutsideClicks);
    };
  }, [isOpenWorkspaceOptions]);

  const handleOutsideClicks = (event) => {
    if (
      event.target.className !== "formPublishSettings-workspace-icon" &&
      event.target.className.baseVal !==
        "workspace-top-title-action-icon-svg" &&
      event.target.className.baseVal !== "" &&
      workspaceOptionsRef &&
      workspaceOptionsRef.current &&
      !workspaceOptionsRef.current.contains(event.target)
    ) {
      setOpenWorkspaceOptions(false);
    }
  };

  const getWorkspaces = async () => {
    try {
      const response = await getAllWorkspaces();
      if (response.status === 200) {
      }
    } catch (err) {
      console.log("getting all workspaces error : ", err);
    } finally {
    }
  };

  const removeGoogleAuthenticate = async () => {
    const tempForm = standartForm;
    tempForm.googleAuthenticate = false;
    try {
      const response = await saveChanges(tempForm);
      if (response.status === 200) {
        setUsedGoogleAuthenticate(false);
        setStandartForm(tempForm);
      }
    } catch (err) {
      console.log("Removing google authenticate error : ", err);
    }
  };

  const removeWeb3Authenticate = async () => {
    const tempForm = standartForm;
    tempForm.web3 = false;
    try {
      const response = await saveChanges(tempForm);
      if (response.status === 200) {
        setUsedWeb3(false);
        setStandartForm(tempForm);
      }
    } catch (err) {
      console.log("removing web3 authenticate error : ", err);
    }
  };

  return (
    <div className="formPublishSettings-container">
      <div className="formPublishSettings-workspace-container">
        <div style={{ display: "flex", flexDirection: "column" }}>
          {/*
          <div
            className="formPublishSettings-workspace-icon"
            onClick={() => setOpenWorkspaceOptions(!isOpenWorkspaceOptions)}
          >
            <FaExchangeAlt />
          </div>

         
          {isOpenWorkspaceOptions && (
            <div
              ref={workspaceOptionsRef}
              className="formPublishSettings-workspace-options"
            >
              <ul>
                {workspaceList.map((workspace) => (
                  <li
                    key={workspace.id}
                    onClick={() => mainContext.saveWorkspace(workspace)}
                  >
                    {workspace.name}
                  </li>
                ))}
              </ul>
            </div>
          )}
            */}
        </div>
        <div className="formPublishSettings-workspace-title">
          <div className="formPublishSettings-workspace">
            {mainContext.selectedWorkspace.name}
          </div>
          <div className="formPublishSettings-divider-icon">
            <LuChevronRight />
          </div>
          <div
            className="formPublishSettings-formName"
            onClick={() => setOpenedRenameModal(true)}
          >
            {formName}
          </div>
        </div>
      </div>
      <div className="formPublishSettings-options">
        {usedGoogleAuthenticate && (
          <div
            className="formPublishSettings-option googleAuthenticateContainer"
            onClick={removeGoogleAuthenticate}
          >
            <div className="formPublishSettings-option googleAuthenticate">
              <div>
                <FcGoogle />
              </div>
            </div>
            <div className="formPublishSettings-option googleRemove">
              remove
            </div>
          </div>
        )}
        {usedWeb3 && (
          <div
            className="formPublishSettings-option googleAuthenticateContainer"
            onClick={removeWeb3Authenticate}
          >
            <div className="formPublishSettings-option googleAuthenticate">
              <div>
                <img
                  src={bitcoinImage}
                  width={20}
                  height={20}
                  alt="bitcoinImage"
                />
              </div>
            </div>
            <div className="formPublishSettings-option googleRemove">
              remove
            </div>
          </div>
        )}

        <div
          className="formPublishSettings-option preview"
          onClick={() => setOpenedPreviewModal(true)}
        >
          <IoPlayOutline className="formPublishSettings-preview-icon" />
          Preview
        </div>
        {isActivePublishButton() && (
          <PublishButton action={publishForm} loading={loading} />
        )}
      </div>
    </div>
  );
};

export default FormPublishSettings;
