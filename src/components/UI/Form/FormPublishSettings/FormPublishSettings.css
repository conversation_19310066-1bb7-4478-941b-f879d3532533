.formPublishSettings-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid var(--grey200);
  background-color: var(--grey400);
  /*box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;*/
  padding: 10px 15px;
  margin-bottom: 15px;
  width: 100%;
  height: 55px;
}

.formPublishSettings-workspace-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.formPublishSettings-workspace-icon {
  margin-right: 10px;
  background-color: #fff;
  color: #000;
  border-radius: 100%;
  padding: 3px;
  border: 1px solid #000;
  font-size: 14px;
  background-color: var(--grey400);
  cursor: pointer;
}

.formPublishSettings-workspace-options {
  background-color: var(--white);
  padding: 10px 8px;
  margin-top: 30px;
  z-index: 999;
  border: 1px solid var(--black);
  position: fixed;
  border-radius: 15px;
  border-bottom: 5px solid var(--black);
}

.formPublishSettings-workspace-options ul {
}

.formPublishSettings-workspace-options ul li {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  padding: 6px 12px 6px 12px;
}

.formPublishSettings-workspace-options ul li:hover {
  background-color: var(--grey400);
  cursor: pointer;
  border-radius: 30px;
}

.formPublishSettings-workspace-title {
  display: flex;
  align-items: center;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  color: var(--black);
}

.formPublishSettings-workspace {
  font-size: 15px;
}

.formPublishSettings-divider-icon {
  margin: 2px 10px 0 10px;
}

.formPublishSettings-formName {
  font-size: 15px;
  cursor: pointer;
}

.formPublishSettings-formName:hover {
  margin-top: 2px;
  border-bottom: 2px solid var(--black);
}

.formPublishSettings-workspace {
  font-weight: 500;
  font-family: "Exo 2", sans-serif;
  color: var(--black);
}

.formPublishSettings-options {
  display: flex;
  justify-content: center;
  align-items: center;
}

.formPublishSettings-option {
  margin-right: 15px;
}

.formPublishSettings-divider {
  border-right: 1px solid var(--black);
  padding: 0 10px;
}

.googleAuthenticateContainer {
  display: flex;
  justify-content: center;
  align-content: center;
  background-color: var(--white);
  border-radius: 30px;
  border: 1px solid var(--grey200);
  cursor: pointer;
  margin-right: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.googleAuthenticateContainer:hover {
  background-color: var(--red100);
}

.googleAuthenticateContainer:hover .googleRemove {
  color: var(--white);
}

.googleAuthenticate {
  display: flex;
  background-color: var(--white);
  padding: 5px;
  border-radius: 50px;
  font-weight: 500;
  font-size: 20px;
  cursor: default;
  font-family: "Exo 2", sans-serif;
  color: var(--black);
  box-shadow: rgba(0, 0, 0, 0.24) 0px 1px 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.googleRemove {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 5px;
  color: white;
  margin-top: 7px;
  margin-left: -15px;
  padding: 0px 9px;
  color: var(--black);
  border-radius: 50px;
  cursor: pointer;
  height: 15px;
}

.draft {
  background-color: var(--black);
  padding: 3px 15px;
  border-radius: 30px;
  font-weight: 500;
  font-size: 14px;
  cursor: default;
  font-family: "Exo 2", sans-serif;
  color: var(--white);
  margin-left: 30px;
}

.setting {
  font-size: 20px;
  color: var(--black);
  cursor: pointer;
}

.publish {
  background-color: var(--black);
  color: var(--white);
  padding: 3px 10px 4px 10px;
  border-radius: 5px;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
}

.formPublishSettings-option-publish-icon {
  margin-right: 10px;
  font-size: 16px;
}

.edit {
  background-color: var(--orange300);
  color: var(--black);
  padding: 3px 10px;
  border-radius: 30px;
  font-size: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--black);
  border-bottom: 5px solid var(--black);
  cursor: pointer;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
}

.preview {
  cursor: pointer;
  font-family: "Exo 2", sans-serif;
  color: var(--black);
  display: flex;
  align-items: center;
  background-color: var(--white);
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 14px;
  border: 1px solid var(--grey100);
}

.preview:hover {
  background-color: var(--grey400);
  transition: 0.3s all;
}

.formPublishSettings-preview-icon {
  font-size: 18px;
  color: var(--black);
  margin-right: 5px;
}

/*
.publish:hover {
    border: 1px solid var(--black);
    margin-top: 4px;
    position: relative;
    transition: .15s all;
}
    */

.formPublishSettings-option-preview-opened {
  display: flex;
  justify-content: center;
  align-items: center;
}

.formPublishSettings-option-preview-icon {
  padding: 3px;
  border: 1px solid var(--black);
  border-radius: 50px;
  border-bottom: 4px solid var(--black);
  color: var(--black);
  cursor: pointer;
  background-color: var(--white);
  font-size: 14px;
}

.formPublishSettings-option-preview-icon:hover {
  border: 1px solid var(--black);
  margin-top: 3px;
  transition: 0.15s all;
}
