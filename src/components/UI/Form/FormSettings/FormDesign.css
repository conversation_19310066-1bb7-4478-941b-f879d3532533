.formDesign-container {
  font-family: "Exo 2", sans-serif;
  overflow-y: auto;
  height: calc(100% - 110px);
}

.formDesign-themes-container {
  display: flex;
  justify-content: center;
}

.formDesign-themes-button-container {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 20px 15px 10px 15px;
}

.formDesign-themes-title {
  display: flex;
  justify-content: space-between;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  border-bottom: 1px solid var(--grey200);
  width: 100%;
}

.formDesign-themes-button-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50%;
  padding: 3px;
  background-color: var(--green400);
  font-weight: 500;
  font-size: 15px;
  color: var(--white);
  cursor: default;
}

.formDesign-themes-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50%;
  padding: 3px;
  background-color: var(--grey300);
  font-size: 15px;
  cursor: pointer;
}

.formDesign-myThemes-container {
  padding: 10px 15px 10px 15px;
}

.formDesign-myThemes-createTheme-button {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.formDesign-myThemes-createTheme-button-title {
  margin-right: 10px;
  font-weight: 500;
}

.formDesign-myThemes-createTheme-button-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--black);
  padding: 3px;
  color: var(--white);
  border-radius: 5px;
  margin-right: 10px;
}

.formDesign-myThemes-themes-default {
  font-family: "Exo 2", sans-serif;
  font-weight: 400;
  font-size: 14px;
  cursor: pointer;
  background-color: var(--white);
  border: 1px solid var(--grey200);
  color: var(--black);
  margin-bottom: 5px;
  padding: 2px 10px;
  border-radius: 3px;
}

.formDesign-myThemes-themes-default:hover {
  background-color: var(--grey300);
  transition: 0.15s all;
}

.formDesign-createTheme-container {
  padding: 0 15px;
  overflow-y: auto;
  position: relative;
}

.ant-input-outlined {
  /*border-radius: 3px !important;*/
  height: 40px !important;
}

.formDesign-createTheme-theme {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.formDesign-createTheme-theme-back-icon {
  margin-right: 10px;
  font-size: 24px;
  cursor: pointer;
}

.formDesign-createTheme-theme-input {
  display: flex;
  align-items: center;
  border-radius: 3px !important;
  width: 300px;
}

.formDesign-createTheme-font-container {
  margin-top: 10px;
}

.formDesign-createTheme-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0 0 0;
}

.formDesign-createTheme-field-title {
  margin-right: 10px;
}

.formDesign-createTheme-field-input {
}

.formDesign-createTheme-field-roundedCorner-icons {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.formDesign-createTheme-field-roundedCorner {
  margin-right: 20px;
  font-size: 20px;
  cursor: pointer;
  padding: 3px;
}

.formDesign-createTheme-field-roundedCorner-selected {
  border: 1px solid var(--black);
  background-color: var(--grey300);
  padding: 3px;
  border-radius: 5px;
  margin-right: 20px;
}

.formDesign-createTheme-divider {
  padding: 10px;
  border-bottom: 1px solid var(--grey300);
}

.formDesign-createTheme-field-add-button {
  border: 1px solid var(--black);
  border-bottom: 3px solid var(--black);
  padding: 2px 10px;
  border-radius: 20px;
  cursor: pointer;
}

.formDesign-createTheme-field-add-button:hover {
  border: 1px solid var(--black);
  margin-top: 2px;
  transition: 0.15s all;
}

.formDesign-createTheme-field-buttons {
  position: fixed;
  display: flex;
  justify-content: space-between;
  bottom: 50px;
  width: 270px;
}

.formDesign-createTheme-field-cancelButton {
  display: flex;
  justify-content: center;
  align-content: center;
  background-color: var(--grey300);
  padding: 2px 10px;
  border-radius: 20px;
  border: 1px solid var(--grey100);
  border-bottom: 5px solid var(--grey100);
}

.formDesign-createTheme-field-cancelButton:hover {
  margin-top: 4px;
  border: 1px solid var(--grey100);
  transition: 0.15s all;
  cursor: pointer;
}

.formDesign-createTheme-field-saveButton {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--white);
  color: var(--black);
  padding: 2px 10px;
  border-radius: 20px;
  border: 1px solid var(--black);
  border-bottom: 5px solid var(--black);
}

.formDesign-createTheme-field-saveButton:hover {
  margin-top: 4px;
  border: 1px solid var(--black);
  transition: 0.15s all;
  cursor: pointer;
}

.ant-upload-list-item {
  width: 270px;
  margin-left: -135px !important;
}

.ant-tooltip-inner {
  display: none;
}

.ant-form-item-label > label {
  font-size: 15px !important;
  font-family: "Exo 2" !important;
  color: var(--black) !important;
}

.ant-upload-list-item-error {
  border-color: var(--black) !important;
  color: var(--black) !important;
  margin-left: -220px !important;
}

.ant-upload-list-item-name {
  color: var(--black) !important;
}

.ant-upload-wrapper {
  width: 50px;
}

.formDesign-createTheme-upload-button-wrapper {
  position: relative;
  overflow: hidden;
  display: inline-block;
  cursor: pointer;
}

.formDesign-createTheme-upload-button {
  border: 1px solid var(--black);
  border-bottom: 3px solid var(--black);
  color: var(--black);
  background-color: white;
  padding: 1px 8px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
}

.formDesign-createTheme-upload-button-wrapper:hover
  .formDesign-createTheme-upload-button {
  margin-top: 2px;
  border: 1px solid var(--black);
  cursor: pointer;
}

.formDesign-createTheme-upload-button-wrapper input[type="file"] {
  font-size: 100px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  cursor: pointer;
}

.form-design-createTheme-backgroundImage-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.form-design-createTheme-backgroundImage-preview-image {
  width: 48px;
  height: 48px;
}

.form-design-createTheme-backgroundImage-preview-delete-icon {
  font-size: 24px;
  color: var(--black);
  background-color: var(--white);
  border: 1px solid var(--black);
  border-radius: 30px;
  padding: 4px;
  cursor: pointer;
}

.form-design-createTheme-backgroundImage-preview-delete-icon:hover {
  background-color: var(--red100);
  color: var(--white);
}

.formDesign-createTheme-backgroundImage-error {
  font-size: 12px;
  color: var(--red100);
  font-family: "Exo 2", sans-serif;
  margin-top: 10px;
}

.formDesign-myThemes-themes-container {
  padding: 10px 15px;
}

.formDesign-myThemes-theme {
  border: 1px solid var(--grey200);
  margin-bottom: 15px;
}

.formDesign-myThemes-theme-selected {
  border: 1px solid var(--black);
  margin-bottom: 15px;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 5px 10px;
}

.formDesign-myThemes-theme-header {
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  padding-bottom: 5px;
  border-bottom: 1px solid var(--border);
  padding: 10px;
}

.formDesign-myThemes-theme-question {
  margin-bottom: 5px;
}

.formDesign-myThemes-theme-answer {
  margin-bottom: 5px;
}

.formDesign-myThemes-theme-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 30px;
  margin-top: 10px;
  margin-bottom: 5px;
}

.formDesig-myThemes-theme-options {
  cursor: pointer;
}

.formDesign-myThemes-theme-options-expanded {
  background-color: var(--white);
  margin-top: 100px;
  padding: 10px 8px;
  border: 1px solid var(--black);
  border-bottom: 5px solid var(--black);
  border-radius: 15px;
}

.formDesign-myThemes-theme-options-item {
  font-family: "Exo 2", sans-serif;
  cursor: pointer;
  padding: 3px 20px;
}

.formDesign-myThemes-theme-options-item:hover {
  background-color: var(--grey300);
  border-radius: 30px;
}
