import { useCallback, useEffect, useState } from "react";
import "./FormSettings.css";
import FormDesign from "./FormDesign";
import FormContent from "./FormContent";

const FormSettings = ({
  setLoading,
  standartForm,
  setStandartForm,
  saveChanges,
  selectedField,
  setSelectedField,
  editField,
  selectedTheme,
  setSelectedTheme,
  themeList,
  setThemeList,
  themeId,
  setThemeId,
  font,
  setFont,
  pageBackgroundImage,
  setPageBackgroundImage,
  pageBackgroundColor,
  setPageBackgroundColor,
  backgroundImage,
  setBackgroundImage,
  backgroundColor,
  setBackgroundColor,
  questionColor,
  setQuestionColor,
  answerColor,
  setAnswerColor,
  buttonColor,
  setButtonColor,
  buttonTextColor,
  setButtonTextColor,
  rounded,
  setRounded,
  selectedSettingOption,
  setSelectedSettingOption,
}) => {
  const [isContentDisabled, setContentDisabled] = useState(false);
  const getSelectedSettingOption = (option) => {
    /*
    if (
      option === "content" &&
      (!selectedField || (selectedField && !selectedField.id))
    ) {
      setContentDisabled(true);
      setSelectedSettingOption("design");
      return;
    }
      */
    setSelectedSettingOption(option);
  };

  const memoizedSetSelectedSettingOption = useCallback(
    (value) => {
      setSelectedSettingOption(value);
    },
    [setSelectedSettingOption]
  );

  useEffect(() => {
    if (!selectedField || (selectedField && !selectedField.id)) {
      setContentDisabled(true);
      memoizedSetSelectedSettingOption("design");
    } else {
      setContentDisabled(false);
    }
  }, [selectedField, memoizedSetSelectedSettingOption]);

  return (
    <div className="formSettings-container">
      <div className="formSettings-options">
        <div
          className={
            selectedSettingOption === "content"
              ? "formSettings-option formSettings-option-selected"
              : "formSettings-option"
          }
          style={{
            opacity: isContentDisabled ? ".5" : "",
            cursor: isContentDisabled ? "not-allowed" : "",
          }}
          onClick={() => getSelectedSettingOption("content")}
        >
          Content
        </div>
        <div
          className={
            selectedSettingOption === "design"
              ? "formSettings-option formSettings-option-selected"
              : "formSettings-option"
          }
          onClick={() => getSelectedSettingOption("design")}
        >
          Design
        </div>
        {/*
        <div
          className={
            selectedSettingOption === "logic"
              ? "formSettings-option formSettings-option-selected"
              : "formSettings-option"
          }
          onClick={() => getSelectedSettingOption("logic")}
        >
          Logic
        </div>
        */}
      </div>
      {selectedSettingOption === "design" && (
        <FormDesign
          standartForm={standartForm}
          setStandartForm={setStandartForm}
          selectedTheme={selectedTheme}
          setSelectedTheme={setSelectedTheme}
          themeList={themeList}
          setThemeList={setThemeList}
          themeId={themeId}
          setThemeId={setThemeId}
          font={font}
          setFont={setFont}
          pageBackgroundImage={pageBackgroundImage}
          setPageBackgroundImage={setPageBackgroundImage}
          pageBackgroundColor={pageBackgroundColor}
          setPageBackgroundColor={setPageBackgroundColor}
          backgroundImage={backgroundImage}
          setBackgroundImage={setBackgroundImage}
          backgroundColor={backgroundColor}
          setBackgroundColor={setBackgroundColor}
          questionColor={questionColor}
          setQuestionColor={setQuestionColor}
          answerColor={answerColor}
          setAnswerColor={setAnswerColor}
          buttonColor={buttonColor}
          setButtonColor={setButtonColor}
          buttonTextColor={buttonTextColor}
          setButtonTextColor={setButtonTextColor}
          rounded={rounded}
          setRounded={setRounded}
        />
      )}

      {selectedField &&
        selectedField.id &&
        selectedSettingOption === "content" && (
          <FormContent
            setLoading={setLoading}
            standartForm={standartForm}
            setStandartForm={setStandartForm}
            saveChanges={saveChanges}
            selectedField={selectedField}
            setSelectedField={setSelectedField}
            editField={editField}
            setSelectedSettingOption={setSelectedSettingOption}
          />
        )}
    </div>
  );
};

export default FormSettings;
