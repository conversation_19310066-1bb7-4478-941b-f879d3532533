import { ColorPicker, Input, InputNumber, Radio, Switch, Upload } from "antd";
import FieldItemList from "../FieldToolbox/FielItemList";
import "./FormContent.css";
import { useEffect, useState } from "react";
import { PiImageDuotone } from "react-icons/pi";
import { updateField } from "../../../../services/http";
import SmallLoading from "../../Loading/SmallLoading";
import { generateImageId } from "../../../../pages/form/FormUtil";

const FormContent = ({
  setLoading,
  standartForm,
  setStandartForm,
  selectedField,
  setSelectedField,
}) => {
  const [contentLoading, setContentLoading] = useState(false);
  const [field, setField] = useState(Object.assign({}, selectedField));
  const [debounceTimeout, setDebounceTimeout] = useState(null);
  const [imageList, setImageList] = useState([]);

  useEffect(() => {
    if (field.id !== selectedField.id) {
      setContentLoading(true);
      setTimeout(() => {
        setField(selectedField);
        setSelectedField(selectedField);
        setContentLoading(false);
      }, 100);
    }
  }, [selectedField]);

  const properties = FieldItemList.find(
    (f) => f.type === selectedField.type
  )?.properties;

  useEffect(() => {
    if (field.fileUrl) {
      let file;
      if (field.file) {
        file = field.file;
      } else {
        file = {
          uid: "0",
          name: field.fileName,
          url: `${process.env.REACT_APP_BACKEND_URL}/api/v1/response/files/image/${field.fileUrl}`,
        };
      }
      setImageList([file]);
    }
  }, [selectedField]);

  const saveField = async (updatedField) => {
    try {
      setLoading(true);
      const response = await updateField(updatedField);
    } catch (err) {
      console.log("Updating field error : ", err);
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChanges = (updatedField) => {
    if (!updatedField || !updatedField.type) {
      console.error("Invalid updatedField data");
      return;
    }
    const tempForm = { ...standartForm };
    if (updatedField.type === "WelcomePageField") {
      tempForm.welcomePageItem.title = updatedField.title;
      tempForm.welcomePageItem.description = updatedField.description;
      tempForm.welcomePageItem.buttonText = updatedField.buttonText;
    } else if (updatedField.type === "ThankyouPageField") {
      tempForm.thankyouPageItem.title = updatedField.title;
      tempForm.thankyouPageItem.description = updatedField.description;
      tempForm.thankyouPageItem.redirectUrl = updatedField.redirectUrl;
    } else {
      const tempPages = [...tempForm.pages];
      const tempPage = tempPages.find((p) => p.id === updatedField.pageId);

      if (tempPage) {
        const tempField = tempPage.items.find((f) => f.id === updatedField.id);

        if (tempField) {
          tempField.buttonText = updatedField.buttonText;
          tempField.description = updatedField.description;
          tempField.dividerHeight = updatedField.dividerHeight;
          tempField.dividerStyle = updatedField.dividerStyle;
          if (updatedField.file) {
            tempField.file = updatedField.file;
          }

          tempField.filesLimit = updatedField.filesLimit;
          tempField.fileName = updatedField.fileName;
          if (updatedField.fileUrl) {
            tempField.fileUrl = updatedField.fileUrl;
          }
          tempField.height = updatedField.height;
          tempField.hide = updatedField.hide;
          tempField.lineColor = updatedField.lineColor;
          tempField.placeholder = updatedField.placeholder;
          tempField.required = updatedField.required;
          tempField.spaceBelow = updatedField.spaceBelow;
          tempField.spaceAbove = updatedField.spaceAbove;
          tempField.textAlignment = updatedField.textAlignment;
          tempField.textSize = updatedField.textSize;
          tempField.title = updatedField.title;
          tempField.titleText = updatedField.titleText;
          tempField.width = updatedField.width;
          setField(tempField);
        }

        const pageIndex = tempPages.findIndex(
          (p) => p.id === updatedField.pageId
        );
        if (pageIndex !== -1) {
          tempPages[pageIndex] = { ...tempPage, items: [...tempPage.items] };
        }
      }
      tempForm.pages = tempPages;
    }
    setStandartForm(tempForm);
  };

  const onChange = (property, e) => {
    setImageList(null);
    const propertyName = property.propertyName;
    let value;

    if (e && e.target) {
      value = e.target.value;
    } else {
      value = e;
    }

    if (property && property.propertyType === "colorPicker") {
      value = e.toHexString();
    }

    const updatingField = { ...field };
    if (propertyName === "file" && value.file) {
      setLoading(true);
      value.file.fileId = generateImageId(value.file.type);
      updatingField[propertyName] = value.file;
      if (value.file.fileId) {
        updatingField.fileUrl = value.file.fileId;
      }

      setTimeout(() => {
        setLoading(false);
      }, 100);
    } else {
      updatingField[propertyName] = value;
    }
    handleFieldChanges(updatingField);
    setField((prevState) => ({
      ...prevState,
      [propertyName]: value,
    }));
    setSelectedField(updatingField);

    if (debounceTimeout) {
      clearTimeout(debounceTimeout);
    }
    const timeoutId = setTimeout(() => {
      saveField(updatingField);
    }, 1000);

    setDebounceTimeout(timeoutId);
  };

  const getValueElement = (property) => {
    if (property.propertyType === "input") {
      return (
        <Input
          defaultValue={field[property.propertyName]}
          onChange={(e) => onChange(property, e)}
        />
      );
    }

    if (property.propertyType === "number") {
      return (
        <InputNumber
          className="numberInput-input"
          defaultValue={field[property.propertyName]}
          onChange={(e) => onChange(property, e)}
        />
      );
    }

    if (property.propertyType === "options") {
      return (
        <Radio.Group
          buttonStyle="solid"
          optionType="button"
          defaultValue={field[property.propertyName]}
        >
          {property.options.map((option, index) => (
            <Radio.Button
              key={index}
              value={option}
              onChange={() => onChange(property, option)}
            >
              {option}
            </Radio.Button>
          ))}
        </Radio.Group>
      );
    }

    if (property.propertyType === "switch") {
      return (
        <Switch
          defaultValue={field[property.propertyName]}
          onChange={(e) => onChange(property, e)}
        />
      );
    }

    if (property.propertyType === "colorPicker") {
      return (
        <ColorPicker
          defaultValue={field[property.propertyName]}
          onChange={(e) => onChange(property, e)}
        />
      );
    }

    if (property.propertyType === "upload") {
      return (
        <Upload
          maxCount={1}
          listType="picture"
          accept=".png,.jpg,.jpeg"
          showRemoveIcon={false} // Remove ikonunu gizler
          onChange={(e) => onChange(property, e)}
        >
          <div className="formContent-upload-button-container">
            <PiImageDuotone className="formContent-upload-button-icon" />
            <div className="formContent-upload-button-text">
              CHOOSE AN IMAGE
            </div>
          </div>
          {imageList?.map(
            (image, index) =>
              image && (
                <div
                  className="formContent-uploaded-image-container"
                  key={index}
                >
                  {image.url && (
                    <img
                      src={image.url}
                      className="formContent-uploaded-image"
                      alt="Uploaded"
                    />
                  )}
                  {image.file &&
                    image.file.thumbUrl &&
                    image.fileList &&
                    image.fileList.length === 0 && (
                      <img
                        src={image.file.thumbUrl}
                        className="formContent-uploaded-image"
                        alt="Uploaded"
                      />
                    )}
                </div>
              )
          )}
        </Upload>
      );
    }
  };

  if (contentLoading) {
    return <SmallLoading />;
  }
  return (
    <div className="formContent-container">
      {selectedField.type === "WelcomePageField" && (
        <div className="formContent-welcomePage">Welcome Page</div>
      )}
      {selectedField &&
        selectedField.id &&
        properties &&
        properties.map((property, index) => (
          <div className="formContent-property-container" key={index}>
            <div className="formContent-property-label">
              {property.propertyTitle}
            </div>
            <div className="formContent-property-value">
              {getValueElement(property)}
            </div>
            <div className="formContent-property-explanation">
              {property.propertyExplanation}
            </div>
          </div>
        ))}
      {/*
      <div style={{ padding: "15px" }}>
        <div
          className="formContent-saveButton-container"
          onClick={() => editField(field)}
        >
          SAVE CHANGES
        </div>
      </div>
      */}
    </div>
  );
};

export default FormContent;
