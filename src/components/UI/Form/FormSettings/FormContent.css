.formContent-container {
  font-family: "Exo 2", sans-serif;
  overflow-y: auto;
  height: calc(100% - 110px);
}

.formContent-welcomePage {
  font-size: 16px;
  font-weight: 500;
  padding: 10px 15px;
  border-bottom: 1px solid var(--grey200);
  margin-bottom: 15px;
}

.formContent-property-container {
  padding: 10px 15px;
}

.formContent-property-label {
  font-weight: 600;
  margin-bottom: 5px;
}

.formContent-property-explanation {
  font-size: 12px;
  opacity: 0.9;
  margin-top: 5px;
}

.formContent-property-options-container {
  display: flex;
}

.formContent-property-options-container input {
}

.formContent-property-options-container
  input:checked
  + .formContent-property-option {
  border: 1px solid var(--grey200);
  padding: 5px 10px;
  background-color: var(--black);
  color: var(--white);
  font-weight: 600;
}

.formContent-property-option {
  border: 1px solid var(--grey200);
  padding: 5px 10px;
  cursor: pointer;
}

.formContent-property-option:hover {
  background-color: var(--grey300);
}

.formContent-property-option-selected {
  border: 1px solid var(--grey200);
  padding: 5px 10px;
  background-color: var(--black);
  color: var(--white);
  font-weight: 600;
}

.formContent-saveButton-container {
  position: fixed;
  bottom: 60px;
  width: 270px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 30px;
  border: 1px solid var(--black);
  border-bottom: 5px solid var(--black);
  padding: 5px;
  cursor: pointer;
}

.formContent-saveButton-container:hover {
  border: 1px solid var(--black);
  margin-top: 4px;
  transition: 0.3s all;
}

.ant-switch.ant-switch-checked {
  background-color: var(--black);
}

.ant-switch.ant-switch-checked:hover {
  background-color: var(--grey300) !important;
}

.ant-switch:hover {
  background-color: var(--grey300) !important;
}

.ant-radio-button-wrapper-checked {
  background-color: var(--black) !important;
  border-color: var(--black) !important;
}

.ant-radio-button-wrapper-checked:hover {
  color: var(--white) !important;
}

.ant-radio-button-wrapper:hover {
  color: var(--grey200) !important;
}

.ant-input-number-outlined:focus-within {
  box-shadow: 0 0 0 0 rgba(5, 145, 255, 0) !important;
  border-color: var(--grey200) !important;
}

.numberInput-input-input {
  width: 100%;
  border-color: var(--grey200);
}

.numberInput-input:hover {
  border-color: var(--grey100);
}

.numberInput-input:focus {
  border-color: var(--grey100);
  border-width: 1px;
}

.formContent-upload-button-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border: 1px solid var(--black);
  padding: 5px 10px;
  border-radius: 5px;
  width: 100%;
  background-color: var(--grey400);
  cursor: pointer;
}

.formContent-upload-button-container:hover {
  opacity: 0.8;
}

.formContent-upload-button-icon {
  font-size: 24px;
  margin-right: 10px;
}

.formContent-upload-button-text {
  font-weight: 500;
  font-family: "Exo 2", sans-serif;
}

.formContent-uploaded-image-container {
  display: flex;
  align-items: center;
  margin-top: 10px;
  /*border: 1px solid var(--grey200);*/
  border-radius: 5px;
}

.formContent-uploaded-image {
  width: 48px;
  height: 48px;
  margin-right: 10px;
}

.formContent-uploaded-image-delete {
  font-size: 16px;
  padding: 3px;
  border-radius: 5px;
  color: var(--red200);
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
  cursor: pointer;
}

.formContent-uploaded-image-delete:hover {
  background-color: var(--grey200);
  transition: 0.3s all;
}

.ant-upload-wrapper
  .ant-upload-list.ant-upload-list-picture
  .ant-upload-list-item {
  margin-left: 0px !important;
  height: 60px !important;
  border: 1px solid var(--grey200) !important;
}

.ant-upload-list-item-action.ant-btn:hover {
  background-color: var(--grey200) !important;
}
