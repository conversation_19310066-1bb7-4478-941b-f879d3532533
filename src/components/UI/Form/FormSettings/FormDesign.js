import { ColorPicker, Form, Input, Select, message } from "antd";
import "./FormDesign.css";
import { useEffect, useRef, useState } from "react";
import { AiOutlinePlus } from "react-icons/ai";
import { IoIosArrowBack } from "react-icons/io";
import {
  TbBorderCornerPill,
  TbBorderCornerRounded,
  TbBorderCornerSquare,
} from "react-icons/tb";
import {
  deleteThemeById,
  getThemeById,
  getThemesByOrganization,
  saveForm,
  saveTheme,
} from "../../../../services/http";
import { MdDeleteOutline } from "react-icons/md";
import { useMainContext } from "../../../../context/MainContext";
import SmallLoading from "../../Loading/SmallLoading";
import { SlOptions } from "react-icons/sl";
import ErrorModalWithSelection from "../../Modals/ErrorModalWithOneSelection";
import { pagesWithoutRef, pagesWithRef } from "../../../../pages/form/FormUtil";

const FormDesign = ({
  standartForm,
  setStandartForm,
  selectedTheme,
  setSelectedTheme,
  themeList,
  setThemeList,
  themeId,
  setThemeId,
  font,
  setFont,
  pageBackgroundImage,
  setPageBackgroundImage,
  pageBackgroundColor,
  setPageBackgroundColor,
  backgroundImage,
  setBackgroundImage,
  backgroundColor,
  setBackgroundColor,
  questionColor,
  setQuestionColor,
  answerColor,
  setAnswerColor,
  buttonColor,
  setButtonColor,
  buttonTextColor,
  setButtonTextColor,
  rounded,
  setRounded,
}) => {
  const [loading, setLoading] = useState(false);
  const [themeActive, setThemeActive] = useState("My Themes");
  const [isCreateTheme, setCreateTheme] = useState(false);
  const [pageBackgroundImageError, setPageBackgroundImageError] = useState();
  const [backgroundImageError, setBackgroundImageError] = useState();

  const [expandedThemeOptions, setExpandedThemeOptions] = useState(null);

  const [deleteModal, setDeleteModal] = useState(false);

  const [themeName, setThemeName] = useState("My theme");
  const mainContext = useMainContext();

  const themeOptionsRef = useRef();

  const [messageApi, contextHolder] = message.useMessage();

  const getThemelist = async () => {
    console.log(
      "mainContext.selectedOrganization.id : ",
      mainContext.selectedOrganization.id
    );
    try {
      const response = await getThemesByOrganization(
        mainContext.selectedOrganization.id
      );
      if (response && response.status === 200) {
        const themes = response.data;
        themes.sort(function (a, b) {
          return b.id - a.id;
        });
        setThemeList(themes);
      }
    } catch (error) {
      console.log("Getting themes error : ", error);
    }
  };

  useEffect(() => {
    async function listThemes() {
      await getThemelist();
    }
    listThemes();
  }, [getThemelist]);

  useEffect(() => {
    document.addEventListener("mousedown", handleOutsideClicks);
    return () => {
      document.removeEventListener("mousedown", handleOutsideClicks);
    };
  }, [expandedThemeOptions]);

  const handleOutsideClicks = (event) => {
    if (
      event.target.className !== "workspace-top-title-action-icon" &&
      event.target.className.baseVal !==
        "workspace-top-title-action-icon-svg" &&
      event.target.className.baseVal !== "" &&
      themeOptionsRef &&
      themeOptionsRef.current &&
      !themeOptionsRef.current.contains(event.target)
    ) {
      setExpandedThemeOptions(null);
    }
  };

  const onChangeThemeOptions = (id) => {
    if (id === expandedThemeOptions) {
      setExpandedThemeOptions(null);
    } else {
      setExpandedThemeOptions(id);
    }
  };

  const selectTheme = async (theme) => {
    if (selectedTheme && theme.id === selectedTheme.id) {
      // setSelectedTheme(null);
    } else {
      try {
        setLoading(true);
        const tempForm = standartForm;
        tempForm.workspace = mainContext.selectedWorkspace;
        tempForm.theme = theme;
        tempForm.pages = pagesWithoutRef(tempForm.pages);
        const response = await saveForm(tempForm);
        if (response.status === 200) {
          setSelectedTheme(theme);
          const savedForm = response.data;
          savedForm.pages = pagesWithRef(savedForm.pages);
          setStandartForm(savedForm);
        }
      } catch (err) {
        console.log("Selecting theme error : ", err);
      } finally {
        setLoading(false);
      }
    }
  };

  const selectDefaultTheme = async () => {
    if (selectedTheme && standartForm.theme !== null) {
      try {
        setLoading(true);
        const tempForm = standartForm;
        tempForm.workspace = mainContext.selectedWorkspace;
        tempForm.theme = null;
        tempForm.pages = pagesWithoutRef(tempForm.pages);
        const response = await saveForm(tempForm);
        if (response.status === 200) {
          setSelectedTheme(null);
          const savedForm = response.data;
          savedForm.pages = pagesWithRef(savedForm.pages);
          setStandartForm(savedForm);
        }
      } catch (err) {
        console.log("Selecting theme error : ", err);
      } finally {
        setLoading(false);
      }
    }
  };

  const fontFamilyOptions = [
    {
      value: "Inter",
      label: (
        <div style={{ fontFamily: "Inter", fontSize: "16px" }}>
          Inter (Default)
        </div>
      ),
    },
    {
      value: "Roboto",
      label: (
        <div style={{ fontFamily: "Roboto", fontSize: "16px" }}>Roboto</div>
      ),
    },
    {
      value: "Open Sans",
      label: (
        <div style={{ fontFamily: "Open Sans", fontSize: "16px" }}>
          Open Sans
        </div>
      ),
    },
    {
      value: "Poppins",
      label: (
        <div style={{ fontFamily: "Poppins", fontSize: "16px" }}>Poppins</div>
      ),
    },
    {
      value: "Lato",
      label: <div style={{ fontFamily: "Lato", fontSize: "16px" }}>Lato</div>,
    },
    {
      value: "Oswald",
      label: (
        <div style={{ fontFamily: "Oswald", fontSize: "16px" }}>Oswald</div>
      ),
    },
    {
      value: "Rubik",
      label: <div style={{ fontFamily: "Rubik", fontSize: "16px" }}>Rubik</div>,
    },
    {
      value: "Exo 2",
      label: <div style={{ fontFamily: "Exo 2", fontSize: "16px" }}>Exo 2</div>,
    },
    {
      value: "Ubuntu",
      label: (
        <div style={{ fontFamily: "Ubuntu", fontSize: "16px" }}>Ubuntu</div>
      ),
    },
    {
      value: "Kanit",
      label: <div style={{ fontFamily: "Kanit", fontSize: "16px" }}>Kanit</div>,
    },
    {
      value: "Work Sans",
      label: (
        <div style={{ fontFamily: "Work Sans", fontSize: "16px" }}>
          Work Sans
        </div>
      ),
    },
    {
      value: "Lora",
      label: <div style={{ fontFamily: "Lora", fontSize: "16px" }}>Lora</div>,
    },
    {
      value: "Manrope",
      label: (
        <div style={{ fontFamily: "Manrope", fontSize: "16px" }}>Manrope</div>
      ),
    },
    {
      value: "Barlow",
      label: (
        <div style={{ fontFamily: "Barlow", fontSize: "16px" }}>Barlow</div>
      ),
    },
    {
      value: "Calligraffitti",
      label: (
        <div style={{ fontFamily: "Calligraffitti", fontSize: "16px" }}>
          Calligraffitti
        </div>
      ),
    },
    {
      value: "Montserrat",
      label: (
        <div style={{ fontFamily: "Montserrat", fontSize: "16px" }}>
          Montserrat
        </div>
      ),
    },
  ];

  const onChangeThemeName = (e) => {
    const value = e.target.value;
    setThemeName(value);
  };

  const onChangeFont = (e) => {
    setFont(e);
  };

  const onChangePageBackgroundImage = (event) => {
    setPageBackgroundImageError();
    if (event.target.files.length < 1) return;
    const file = event.target.files[0];

    const fileType = file.type
      .substring(file.type.indexOf("/") + 1, file.type.length)
      .toLowerCase();

    if (
      fileType !== "jpg" &&
      fileType !== "jpeg" &&
      fileType !== "png" &&
      fileType !== "gif"
    ) {
      setPageBackgroundImageError("only JPG, PNG or GIF");
      return;
    }

    if (file.size > 4000000) {
      setPageBackgroundImageError("must be less than 4MB");
      return;
    }

    const fileReader = new FileReader();
    fileReader.onloadend = () => {
      const data = fileReader.result;
      setPageBackgroundImage(data);
    };
    fileReader.readAsDataURL(file);
  };

  const onChangeBackgroundImage = (event) => {
    setBackgroundImageError();
    if (event.target.files.length < 1) return;
    const file = event.target.files[0];

    const fileType = file.type
      .substring(file.type.indexOf("/") + 1, file.type.length)
      .toLowerCase();

    if (
      fileType !== "jpg" &&
      fileType !== "jpeg" &&
      fileType !== "png" &&
      fileType !== "gif"
    ) {
      setBackgroundImageError("only JPG, PNG or GIF");
      return;
    }

    if (file.size > 4000000) {
      setBackgroundImageError("must be less than 4MB");
      return;
    }

    const fileReader = new FileReader();
    fileReader.onloadend = () => {
      const data = fileReader.result;
      setBackgroundImage(data);
    };
    fileReader.readAsDataURL(file);
  };

  const deletePageBackgroundImage = () => {
    setPageBackgroundImageError(null);
    setPageBackgroundImage(null);
  };

  const deleteBackgroundImage = () => {
    setBackgroundImageError(null);
    setBackgroundImage(null);
  };

  const onChangePageBackgroundColor = (color) => {
    setPageBackgroundColor(color);
  };

  const onChangeBackgroundColor = (color) => {
    setBackgroundColor(color);
  };

  const onChangeQuestionColor = (color) => {
    setQuestionColor(color);
  };

  const onChangeAnswerColor = (color) => {
    setAnswerColor(color);
  };

  const onChangeButtonColor = (color) => {
    setButtonColor(color);
  };

  const onChangeButtonTextColor = (color) => {
    setButtonTextColor(color);
  };

  const onChangeRoundedCorner = (rounded) => {
    setRounded(rounded);
  };

  const resetTheme = () => {
    setFont("Inter");
    setThemeName("My theme");
    setPageBackgroundImage(null);
    setPageBackgroundColor("#FFFFFF");
    setBackgroundImage(null);
    setBackgroundColor("#FFFFFF");
    setQuestionColor("#000000");
    setAnswerColor("#000000");
    setButtonColor("#000000");
    setButtonTextColor("#FFFFFF");
    setRounded(1);
  };

  const save = async () => {
    setLoading(true);
    try {
      const theme = {
        id: themeId,
        name: themeName,
        font: font,
        pageBackgroundImage: pageBackgroundImage,
        pageBackgroundColor: pageBackgroundColor,
        backgroundImage: backgroundImage,
        backgroundColor: backgroundColor,
        questionColor: questionColor,
        answerColor: answerColor,
        buttonColor: buttonColor,
        buttonTextColor: buttonTextColor,
        rounded: rounded,
        organizationId: mainContext.selectedOrganization.id,
      };
      const response = await saveTheme(theme);
      if (response.status === 200) {
        setThemeId(response.data.id);
        // setSelectedTheme(response.data);
        await getThemelist();
        setSelectedTheme(response.data);
        setTimeout(() => {
          if (selectedTheme && selectedTheme.id) {
            messageApi.open({
              type: "success",
              content: "Theme is updated.",
            });
          } else {
            messageApi.open({
              type: "success",
              content: "Theme is created.",
            });
          }
        }, 200);
      }
    } catch (err) {
      console.log("Creating theme error : ", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    async function listThemes() {
      await getThemelist();
    }
    listThemes();
  }, [getThemelist]);

  const editTheme = async () => {
    try {
      setExpandedThemeOptions(null);
      setLoading(true);
      const response = await getThemeById(selectedTheme.id);
      if (response.status === 200) {
        const theme = response.data;
        setThemeId(theme.id);
        setThemeName(theme.name);
        setFont(theme.font);
        setPageBackgroundImage(theme.pageBackgroundImage);
        setPageBackgroundColor(theme.pageBackgroundColor);
        setBackgroundImage(theme.backgroundImage);
        setBackgroundColor(theme.backgroundColor);
        setQuestionColor(theme.questionColor);
        setAnswerColor(theme.answerColor);
        setButtonColor(theme.buttonColor);
        setButtonTextColor(theme.buttonTextColor);
        setRounded(theme.rounded);
        setCreateTheme(true);
      }
    } catch (err) {
      console.log("Geting theme error : ", err);
    } finally {
      setLoading(false);
    }
  };

  const duplicate = async () => {
    try {
      setLoading(true);

      const theme = {
        name: selectedTheme.name + " (copy)",
        font: selectedTheme.font,
        pageBackgroundImage: selectedTheme.pageBackgroundImage,
        pageBackgroundColor: selectedTheme.pageBackgroundColor,
        backgroundImage: selectedTheme.backgroundImage,
        backgroundColor: selectedTheme.backgroundColor,
        questionColor: selectedTheme.questionColor,
        answerColor: selectedTheme.answerColor,
        buttonColor: selectedTheme.buttonColor,
        buttonTextColor: selectedTheme.buttonTextColor,
        rounded: selectedTheme.rounded,
        organizationId: mainContext.selectedOrganization.id,
      };

      const response = await saveTheme(theme);
      setCreateTheme(true);
      if (response.status === 200) {
        await getThemelist();
        setSelectedTheme(theme);
        theme.id = response.data.id;
        setThemeId(theme.id);
        setThemeName(theme.name);
        setFont(theme.font);
        setBackgroundImage(theme.backgroundImage);
        setBackgroundColor(theme.backgroundColor);
        setQuestionColor(theme.questionColor);
        setAnswerColor(theme.answerColor);
        setButtonColor(theme.buttonColor);
        setButtonTextColor(theme.buttonTextColor);
        setRounded(theme.rounded);
        setCreateTheme(true);
        setExpandedThemeOptions(null);
        setTimeout(() => {
          messageApi.open({
            type: "success",
            content: "Theme is duplicated.",
          });
        }, 200);
      }
    } catch (err) {
      console.log("Duplicating theme error : ", err);
    } finally {
      setLoading(false);
    }
  };

  const deleteTheme = async () => {
    const themeId = selectedTheme.id;
    try {
      setLoading(true);
      const response = await deleteThemeById(themeId);
      if (response.status === 200) {
        setDeleteModal(false);
        setExpandedThemeOptions(null);
        await getThemelist();
      }
    } catch (err) {
      console.log("Deleting theme error : ", err);
    } finally {
      setLoading(false);
    }
  };

  const getRounded = (theme) => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "15px";
    }
  };

  if (loading) {
    return <SmallLoading />;
  }

  return (
    <>
      {contextHolder}
      <div className="formDesign-container">
        <div className="formDesign-themes-container">
          {!isCreateTheme && (
            <div className="formDesign-themes-button-container">
              <div className="formDesign-themes-title">
                Themes{" "}
                <div
                  className="formDesign-myThemes-themes-default"
                  onClick={selectDefaultTheme}
                >
                  Select default theme
                </div>
              </div>

              {/*
              <div
                className={
                  themeActive === "My Themes"
                    ? "formDesign-themes-button-selected"
                    : "formDesign-themes-button"
                }
                style={{
                  borderTopLeftRadius: "5px",
                }}
                onClick={() => onChangeThemeActive("My Themes")}
              >
                My Themes
              </div>
              
              <div
                className={
                  themeActive === "Gallery"
                    ? "formDesign-themes-button-selected"
                    : "formDesign-themes-button"
                }
                style={{
                  borderTopRightRadius: "5px",
                }}
                onClick={() => onChangeThemeActive("Gallery")}
              >
                Gallery
              </div>
              */}
            </div>
          )}
        </div>
        <div className="formDesign-myThemes-container">
          {!isCreateTheme && themeActive === "My Themes" && (
            <div
              className="formDesign-myThemes-createTheme-button"
              onClick={() => {
                resetTheme();
                setThemeId();
                setCreateTheme(true);
              }}
            >
              <div className="formDesign-myThemes-createTheme-button-icon">
                <AiOutlinePlus />
              </div>
              <div className="formDesign-myThemes-createTheme-button-title">
                Create Theme
              </div>
            </div>
          )}
        </div>

        {!isCreateTheme &&
          themeActive === "My Themes" &&
          themeList &&
          themeList.length > 0 && (
            <div className="formDesign-myThemes-themes-container">
              {themeList.map((theme, index) => (
                <div
                  key={index}
                  className={
                    selectedTheme && selectedTheme.id === theme.id
                      ? "formDesign-myThemes-theme-selected"
                      : "formDesign-myThemes-theme"
                  }
                >
                  <div className="formDesign-myThemes-theme-header">
                    <div>{theme.name}</div>
                    <div>
                      {selectedTheme &&
                        selectedTheme.id === theme.id &&
                        expandedThemeOptions === null && (
                          <SlOptions
                            onClick={() => onChangeThemeOptions(theme.id)}
                            className="formDesig-myThemes-theme-options"
                          />
                        )}
                    </div>
                    {expandedThemeOptions === theme.id && (
                      <div
                        ref={themeOptionsRef}
                        className="formDesign-myThemes-theme-options-expanded"
                      >
                        <div
                          className="formDesign-myThemes-theme-options-item"
                          onClick={editTheme}
                        >
                          Edit
                        </div>
                        <div
                          className="formDesign-myThemes-theme-options-item"
                          onClick={duplicate}
                        >
                          Duplicate
                        </div>
                        <div
                          className="formDesign-myThemes-theme-options-item"
                          onClick={() => setDeleteModal(true)}
                        >
                          Delete
                        </div>
                      </div>
                    )}
                  </div>
                  <div
                    style={{
                      padding: "15px",
                      backgroundColor: !theme.pageBackgroundImage
                        ? theme.pageBackgroundColor
                        : "#ffffff",
                      backgroundImage: "url(" + theme.pageBackgroundImage + ")",
                      backgroundSize: "cover",
                      backgroundRepeat: "round",
                    }}
                    onClick={() => selectTheme(theme)}
                  >
                    <div
                      style={{
                        backgroundColor: !theme.backgroundImage
                          ? theme.backgroundColor
                          : "#ffffff",
                        backgroundImage: "url(" + theme.backgroundImage + ")",
                        backgroundSize: "cover",
                        backgroundRepeat: "round",
                        padding: "10px",
                        fontFamily: theme.font,
                      }}
                    >
                      <div
                        className="formDesign-myThemes-theme-question"
                        style={{
                          color: theme.questionColor,
                          fontFamily: theme.font,
                        }}
                      >
                        Question
                      </div>
                      <div
                        className="formDesign-myThemes-theme-answer"
                        style={{
                          color: theme.answerColor,
                          fontFamily: theme.font,
                        }}
                      >
                        Answer
                      </div>
                    </div>
                    <div
                      style={{ display: "flex", justifyContent: "flex-end" }}
                    >
                      <div
                        className="formDesign-myThemes-theme-button"
                        style={{
                          backgroundColor: theme.buttonColor,
                          borderRadius: getRounded(theme),
                        }}
                      >
                        <div
                          style={{
                            color: theme.buttonTextColor,
                            fontFamily: theme.font,
                          }}
                        >
                          Button
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

        {isCreateTheme && (
          <div className="formDesign-createTheme-container">
            <div className="formDesign-createTheme-theme">
              <div className="formDesign-createTheme-theme-input">
                <IoIosArrowBack
                  className="formDesign-createTheme-theme-back-icon"
                  onClick={() => {
                    setThemeId();
                    setCreateTheme(false);
                  }}
                />

                <Input value={themeName} onChange={onChangeThemeName} />
              </div>
            </div>
            <div className="formDesign-createTheme-font-container">
              <div className="formDesign-createTheme-form-family">
                <div className="formDesign-createTheme-form-family-label">
                  Font
                </div>
                <div className="formDesign-createTheme-form-family-input">
                  <Select
                    size={20}
                    defaultValue="Inter (Default)"
                    value={font}
                    onChange={onChangeFont}
                    style={{ width: "100%" }}
                    options={fontFamilyOptions}
                  />
                </div>
              </div>
            </div>
            <div className="formDesign-createTheme-divider" />
            <div className="formDesign-createTheme-field">
              <div
                style={{
                  width: "100%",
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                <div className="formDesign-createTheme-field-title">
                  Page Background image
                </div>
                {!pageBackgroundImage && (
                  <div
                    className="formDesign-createTheme-upload-button-wrapper"
                    onChange={onChangePageBackgroundImage}
                  >
                    <div className="formDesign-createTheme-upload-button">
                      Add
                    </div>
                    <input type="file" name="myfile" />
                  </div>
                )}
              </div>
            </div>

            {pageBackgroundImage && (
              <div className="form-design-createTheme-backgroundImage-preview">
                <img
                  src={pageBackgroundImage}
                  className="form-design-createTheme-backgroundImage-preview-image"
                  alt="pageBackground"
                />
                <MdDeleteOutline
                  className="form-design-createTheme-backgroundImage-preview-delete-icon"
                  onClick={deletePageBackgroundImage}
                />
              </div>
            )}
            {pageBackgroundImageError &&
              pageBackgroundImageError.length > 5 && (
                <div className="formDesign-createTheme-backgroundImage-error">
                  {pageBackgroundImageError}
                </div>
              )}
            {!pageBackgroundImage && (
              <div className="formDesign-createTheme-field">
                <div className="formDesign-createTheme-field-title">
                  Page Background color
                </div>
                <div className="formDesign-createTheme-field-input">
                  <Form.Item>
                    <ColorPicker
                      defaultValue={pageBackgroundColor}
                      value={pageBackgroundColor}
                      onChange={(c) => {
                        onChangePageBackgroundColor(c.toHexString());
                      }}
                    />
                  </Form.Item>
                </div>
              </div>
            )}
            <div className="formDesign-createTheme-divider" />

            <div className="formDesign-createTheme-field">
              <div
                style={{
                  width: "100%",
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                <div className="formDesign-createTheme-field-title">
                  Form Background image
                </div>
                {!backgroundImage && (
                  <div
                    className="formDesign-createTheme-upload-button-wrapper"
                    onChange={onChangeBackgroundImage}
                  >
                    <div className="formDesign-createTheme-upload-button">
                      Add
                    </div>
                    <input type="file" name="myfile" />
                  </div>
                )}
              </div>
            </div>

            {backgroundImage && (
              <div className="form-design-createTheme-backgroundImage-preview">
                <img
                  src={backgroundImage}
                  className="form-design-createTheme-backgroundImage-preview-image"
                  alt="backgroundImage"
                />
                <MdDeleteOutline
                  className="form-design-createTheme-backgroundImage-preview-delete-icon"
                  onClick={deleteBackgroundImage}
                />
              </div>
            )}
            {backgroundImageError && backgroundImageError.length > 5 && (
              <div className="formDesign-createTheme-backgroundImage-error">
                {backgroundImageError}
              </div>
            )}
            {!backgroundImage && (
              <div className="formDesign-createTheme-field">
                <div className="formDesign-createTheme-field-title">
                  Form Background color
                </div>
                <div className="formDesign-createTheme-field-input">
                  <Form.Item>
                    <ColorPicker
                      defaultValue={backgroundColor}
                      value={backgroundColor}
                      onChange={(c) => {
                        onChangeBackgroundColor(c.toHexString());
                      }}
                    />
                  </Form.Item>
                </div>
              </div>
            )}
            <div className="formDesign-createTheme-divider" />
            <div className="formDesign-createTheme-field">
              <div className="formDesign-createTheme-field-title">
                Questions color
              </div>
              <div className="formDesign-createTheme-field-input">
                <ColorPicker
                  defaultValue={questionColor}
                  value={questionColor}
                  onChange={(c) => {
                    onChangeQuestionColor(c.toHexString());
                  }}
                />
              </div>
            </div>
            <div className="formDesign-createTheme-field">
              <div className="formDesign-createTheme-field-title">
                Answers color
              </div>
              <div className="formDesign-createTheme-field-input">
                <ColorPicker
                  defaultValue={answerColor}
                  value={answerColor}
                  onChange={(c) => {
                    onChangeAnswerColor(c.toHexString());
                  }}
                />
              </div>
            </div>

            <div className="formDesign-createTheme-field">
              <div className="formDesign-createTheme-field-title">
                Buttons color
              </div>
              <div className="formDesign-createTheme-field-input">
                <ColorPicker
                  defaultValue={buttonColor}
                  value={buttonColor}
                  onChange={(c) => {
                    onChangeButtonColor(c.toHexString());
                  }}
                />
              </div>
            </div>
            <div className="formDesign-createTheme-field">
              <div className="formDesign-createTheme-field-title">
                Button text color
              </div>
              <div className="formDesign-createTheme-field-input">
                <ColorPicker
                  defaultValue={buttonTextColor}
                  value={buttonTextColor}
                  onChange={(c) => {
                    onChangeButtonTextColor(c.toHexString());
                  }}
                />
              </div>
            </div>
            <div className="formDesign-createTheme-divider" />

            <div
              className="formDesign-createTheme-field"
              style={{
                flexDirection: "column",
                alignItems: "flex-start",
              }}
            >
              <div
                className="formDesign-createTheme-field-title"
                style={{ marginBottom: "10px" }}
              >
                Rounded corners
              </div>
              <div className="formDesign-createTheme-field-roundedCorner-icons">
                <div
                  className={
                    rounded === 1
                      ? "formDesign-createTheme-field-roundedCorner-selected"
                      : "formDesign-createTheme-field-roundedCorner"
                  }
                  onClick={() => onChangeRoundedCorner(1)}
                >
                  <TbBorderCornerSquare />
                </div>
                <div
                  className={
                    rounded === 2
                      ? "formDesign-createTheme-field-roundedCorner-selected"
                      : "formDesign-createTheme-field-roundedCorner"
                  }
                  onClick={() => onChangeRoundedCorner(2)}
                >
                  <TbBorderCornerRounded />
                </div>
                <div
                  className={
                    rounded === 3
                      ? "formDesign-createTheme-field-roundedCorner-selected"
                      : "formDesign-createTheme-field-roundedCorner"
                  }
                  onClick={() => onChangeRoundedCorner(3)}
                >
                  <TbBorderCornerPill />
                </div>
              </div>
            </div>
            <div className="formDesign-createTheme-divider" />

            <div className="formDesign-createTheme-field-buttons">
              <div
                className="formDesign-createTheme-field-cancelButton"
                onClick={resetTheme}
              >
                Reset
              </div>
              <div
                className="formDesign-createTheme-field-saveButton"
                onClick={save}
              >
                Save Changes
              </div>
            </div>
          </div>
        )}
        <ErrorModalWithSelection
          open={deleteModal}
          handleClose={() => setDeleteModal(false)}
          title="Are you sure you want to delete the theme?"
          message="If you confirm, the selected theme will be deleted."
          buttonTitle="Delete Theme"
          buttonAction={deleteTheme}
        />
      </div>
    </>
  );
};

export default FormDesign;
