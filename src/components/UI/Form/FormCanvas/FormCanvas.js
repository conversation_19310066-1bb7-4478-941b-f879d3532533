import { MdDeleteOutline } from "react-icons/md";
import "./FormCanvas.css";
import { TbGripVertical } from "react-icons/tb";
import { Draggable, Droppable } from "react-beautiful-dnd";
import FieldRender from "./FieldRender";
import { GoPlus } from "react-icons/go";
import ButtonField from "../Fields/ButtonField";
import { saveForm } from "../../../../services/http";
import { pagesWithRef } from "../../../../pages/form/FormUtil";
import SmallLoading from "../../Loading/SmallLoading";

const FormCanvas = ({
  loading,
  setLoading,
  index,
  standartForm,
  setStandartForm,
  isOpenToolbox,
  buttonColor,
  pages,
  setPages,
  page,
  draggingPage,
  selectedPage,
  setSelectedPage,
  selectPage,
  selectedField,
  setSelectedField,
  selectedSettingOption,
  setSelectedSettingOption,
  selectField,
  selectedTheme,
  openDeletePageModal,
  openDeleteFieldModal,
  duplicateField,
  saveOptions,
}) => {
  /*
  const container = {
    width: isOpenToolbox ? "calc(100% - 650px)" : "calc(100% - 550px)",
    marginLeft: isOpenToolbox ? "15px" : "200px",
  };
  */

  const saveChanges = async (pages) => {
    try {
      const tempForm = standartForm;
      tempForm.pages = pages;
      const response = await saveForm(tempForm);
      if (response.status === 200) {
        const savedForm = response.data;
        const addedRefToPages = pagesWithRef(savedForm.pages);
        setPages(addedRefToPages);
        savedForm.pages = addedRefToPages;
        setStandartForm(savedForm);
      }
    } catch (err) {
      console.log("Dropping page error : ", err);
    } finally {
    }
  };

  if (loading) {
    <SmallLoading />;
  }

  return (
    <>
      <Droppable droppableId={page.id} key={page.id}>
        {(provided) => (
          <div {...provided.droppableProps} ref={provided.innerRef}>
            <div
              className="formCanvas-outer-container"
              style={{
                display: "flex",
                justifyContent: "flex-end",
                alignItems: "center",
              }}
            >
              <div className="formCanvas-top-container">
                {pages && pages.length > 1 && (
                  <div className="formCanvas-item-index">Page {index + 1}</div>
                )}
                <div className="formCanvas-item-delete-container">
                  <div className="formCanvas-item-delete-icon">
                    <MdDeleteOutline />
                  </div>
                  <div
                    className="formCanvas-item-delete-title"
                    onClick={() => openDeletePageModal(page)}
                  >
                    Remove page
                  </div>
                </div>
              </div>

              <div
                className={
                  selectedPage && selectedPage.id === page.id
                    ? "formCanvas-container-selected"
                    : "formCanvas-container"
                }
                style={{
                  display: pages.length === 1 ? "block" : "flex",
                  opacity: draggingPage?.id === page.id ? ".4" : "",
                  backgroundColor:
                    selectedTheme && !selectedTheme.backgroundImage
                      ? selectedTheme.backgroundColor
                      : "",
                  backgroundImage: selectedTheme
                    ? "url(" + selectedTheme.backgroundImage + ")"
                    : "",
                  backgroundRepeat: "round",
                  boxShadow:
                    draggingPage?.id === page.id
                      ? "rgba(0, 0, 0, 0.5) 5px 5px 5px"
                      : "rgba(0, 0, 0, 0) 0px 0px 0px",
                }}
                onClick={() => selectPage(page)}
              >
                <div
                  className="formCanvas-fields-container"
                  style={{
                    width: selectedField ? "100%" : "",
                  }}
                >
                  {pages.length === 1 && !page.items && (
                    <div className="formCanvas-noField-container">
                      <div className="formCanvas-noField-innerContainer">
                        <GoPlus className="formCancas-noField-icon" />
                        you can add your first question from the left menu.
                      </div>
                    </div>
                  )}
                  <div className="items-container">
                    {page.items?.map((item, index) => (
                      <Draggable
                        draggableId={item.id}
                        index={index}
                        key={item.id}
                      >
                        {(provided) => (
                          <div
                            className="item-container"
                            {...provided.dragHandleProps}
                            {...provided.draggableProps}
                            ref={provided.innerRef}
                          >
                            {FieldRender(
                              loading,
                              setLoading,
                              standartForm,
                              setStandartForm,
                              saveChanges,
                              pages,
                              setPages,
                              selectedPage,
                              setSelectedPage,
                              item,
                              page.items,
                              selectedField,
                              setSelectedField,
                              selectedTheme,
                              selectField,
                              setSelectedSettingOption,
                              openDeleteFieldModal,
                              duplicateField,
                              saveOptions
                            )}

                            {provided.placeholder}
                          </div>
                        )}
                      </Draggable>
                    ))}
                  </div>
                </div>

                {pages.length > 1 && (
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "center",
                    }}
                  >
                    <div className="formCanvas-holder-container">
                      <TbGripVertical className="formCanvas-holder-icon" />
                    </div>
                  </div>
                )}
              </div>
              <div
                key={page.id}
                ref={page.ref}
                style={{ marginTop: "80px", marginBottom: "-80px" }}
              />
              <div className="formCanvas-button-container">
                <ButtonField
                  pages={pages}
                  page={page}
                  buttonText1="SUBMIT"
                  selectedTheme={selectedTheme}
                />
              </div>

              {provided.placeholder}
            </div>
          </div>
        )}
      </Droppable>
    </>
  );
};

export default FormCanvas;
