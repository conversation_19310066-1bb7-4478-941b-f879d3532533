.formCanvas-outer-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    width: 800px;
    padding-top: 20px;
}

.formCanvas-container {
    position: relative;
    display: flex;
    justify-content: space-between;
    min-height: 400px;
    background-color: var(--white);
    box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;
    border: 1px solid var(--grey300) !important;
    margin-top: 10px;
    margin-bottom: 20px;
    width: 100%;
}

.formCanvas-container-selected {
    position: relative;
    display: flex;
    justify-content: space-between;
    min-height: 400px;
    background-color: var(--white);
    box-shadow: rgba(0, 0, 0, 0.25) 0px 0px 5px !important;
    border: 1px solid var(--grey200) !important;
    margin-top: 10px;
    margin-bottom: 20px;
    width: 100%;
}

@media screen and (width < 1600px) {
    .formCanvas-outer-container {
        width: 650px;
    }
}

@media screen and (width < 1400px) {
    .formCanvas-outer-container {
        width: 550px;
    }
}

@media screen and (width < 1250px) {
    .formCanvas-outer-container {
        width: 350px;
    }
}
   

.formCanvas-noField-container {
    height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.formCanvas-noField-innerContainer {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 30px;
    border: 1px dashed var(--grey100);
    background-color: #f9f9f9;
    border-radius: 10px;
    color: #525252;
    font-size: 18px;
    font-weight: 300;
    
}

.formCancas-noField-icon {
    font-size: 36px;
    margin-top: 5px;
    color: var(--black);
}

.formCanvas-fields-container {
    padding: 0 20px;
    margin-top: 20px;
    width: 100%;
}

.formCanvas-settings-container {
    flex-direction: column;
    align-items: center;
    visibility: visible;
    padding: 10px 5px;
    border-radius: 30px;
    background-color: var(--white);
    visibility: hidden;
}

.formCanvas-settings-container-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    visibility: visible;
    padding: 10px 5px 10px 10px;
    background-color: var(--white);
    visibility: visible;
    background-color: var(--white);
    
}

.formCanvas-top-container {
    display: flex;
    justify-content: flex-end;
    width: 100%;
}

.formCanvas-top-container:hover .formCanvas-item-index{
    display: none;
}

.formCanvas-top-container:hover .formCanvas-item-delete-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3px 10px;
    border: 1px solid var(--red100);
    background-color: var(--white);
    height: 25px;
    border-radius: 20px;
}

.items-container {
    height: 100%;
    width: 100%;
}

.formCanvas-item-index {
    font-size: 14px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--black);
    height: 25px;
    width: 70px;
    border-radius: 20px;
    display: flex;
    cursor: pointer;
    background-color: var(--white);
}

.formCanvas-item-delete-container {
    display: none;
}

.formCanvas-item-delete-icon {
    cursor: pointer;
    font-size: 16px;
    color: var(--red100);
    border-radius: 100%;
    margin-right: 5px;
}

.formCanvas-item-delete-title {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: var(--red100);
    cursor: pointer;
}

.formCanvas-settings-item-order-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.formCanvas-settings-item-order-item {
    font-size: 14px;
    background-color: white;
    border: 1px solid var(--black);
    border-bottom: 3px solid var(--black);
    color: var(--black);
    margin-bottom: 5px;
    padding: 5px;
    border-radius: 100%;
}

.formCanvas-settings-item-order-item:hover {
    border: 1px solid var(--black);
    margin-top: 2px;
    cursor: pointer;
    transition: .15s all;
}

.formCanvas-settings-item-order-bottom {
    font-size: 24px;
    background-color: white;
    border: 1px solid var(--black);
    border-bottom: 5px solid var(--black);
    color: var(--black);
    padding: 5px;
    border-radius: 100%;
}

.formCanvas-holder-container {
    
    visibility: hidden;
}

.formCanvas-holder-icon {
    font-size: 32px;
    color: var(--grey100);
}

.formCanvas-container:hover .formCanvas-holder-container {
    visibility: visible;
    position: relative;
}

.formCanvas-container-selected:hover .formCanvas-holder-container {
    visibility: visible;
}

.formCanvas-container:hover {
    /*border: 1px solid var(--grey100) !important;*/
}

.formCanvas-button-container {
    width: 100%;
    margin-bottom: 20px;
}
