import ButtonField from "../Fields/ButtonField";
import CaptchaField from "../Fields/CaptchaField";
import CheckboxField from "../Fields/CheckboxField";
import DatePickerField from "../Fields/DatePickerField";
import DividerField from "../Fields/DividerField";
import EmailField from "../Fields/EmailField";
import FileUploadField from "../Fields/FileUploadField";
import Heading1Field from "../Fields/Heading1Field";
import Heading2Field from "../Fields/Heading2Field";
import Heading3Field from "../Fields/Heading3Field";
import ImageField from "../Fields/ImageField";
import ImageUploadField from "../Fields/ImageUploadField";
import LongInputField from "../Fields/LongInputField";
import MultiSelectMenuField from "../Fields/MultiSelectMenuField";
import NumberField from "../Fields/NumberField";
import PhoneNumberField from "../Fields/PhoneNumberField";
import ScaleRatingField from "../Fields/ScaleRatingField";
import SelectMenuField from "../Fields/SelectMenuField";
import ShortInputField from "../Fields/ShortInputField";
import SingleSelectField from "../Fields/SingleSelectField";
import SmileRatingField from "../Fields/SmileRatingField";
import StarRatingField from "../Fields/StarRatingField";
import TextField from "../Fields/TextField";
import TimePickerField from "../Fields/TimePickerField";
import VideoUploadField from "../Fields/VideoUploadField";
import WelcomePageField from "../Fields/WelcomePageField";

function FieldRender(
  loading,
  setLoading,
  standartForm,
  setStandartForm,
  saveChanges,
  pages,
  setPages,
  selectedPage,
  setSelectedPage,
  item,
  items,
  selectedField,
  setSelectedField,
  selectedTheme,
  selectField,
  setSelectedSettingOption,
  openDeleteFieldModal,
  duplicateField,
  saveOptions
) {
  if (item.type === "Heading1Field") {
    return (
      <Heading1Field
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "Heading2Field") {
    return (
      <Heading2Field
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "Heading3Field") {
    return (
      <Heading3Field
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "TextField") {
    return (
      <TextField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "ShortInputField") {
    return (
      <ShortInputField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "LongInputField") {
    return (
      <LongInputField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "SelectMenuField") {
    return (
      <SelectMenuField
        loading={loading}
        setLoading={setLoading}
        pages={pages}
        setPages={setPages}
        selectedPage={selectedPage}
        setSelectedPage={setSelectedPage}
        items={items}
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
        saveOptions={saveOptions}
      />
    );
  }

  if (item.type === "MultiSelectMenuField") {
    return (
      <MultiSelectMenuField
        loading={loading}
        setLoading={setLoading}
        pages={pages}
        setPages={setPages}
        selectedPage={selectedPage}
        setSelectedPage={setSelectedPage}
        items={items}
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
        saveOptions={saveOptions}
      />
    );
  }

  if (item.type === "CheckboxField") {
    return (
      <CheckboxField
        loading={loading}
        setLoading={setLoading}
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
        saveOptions={saveOptions}
      />
    );
  }

  if (item.type === "SingleSelectField") {
    return (
      <SingleSelectField
        loading={loading}
        setLoading={setLoading}
        pages={pages}
        setPages={setPages}
        selectedPage={selectedPage}
        setSelectedPage={setSelectedPage}
        items={items}
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
        saveOptions={saveOptions}
      />
    );
  }

  if (item.type === "EmailField") {
    return (
      <EmailField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "NumberField") {
    return (
      <NumberField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "PhoneNumberField") {
    return (
      <PhoneNumberField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "TimePickerField") {
    return (
      <TimePickerField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "DatePickerField") {
    return (
      <DatePickerField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "FileUploadField") {
    return (
      <FileUploadField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "ImageUploadField") {
    return (
      <ImageUploadField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "VideoUploadField") {
    return (
      <VideoUploadField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "StarRatingField") {
    return (
      <StarRatingField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "ScaleRatingField") {
    return (
      <ScaleRatingField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "SmileRatingField") {
    return (
      <SmileRatingField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "DividerField") {
    return (
      <DividerField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "ImageField") {
    return (
      <ImageField
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }
  if (item.type === "ButtonField") {
    return (
      <ButtonField
        pages={pages}
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "CaptchaField") {
    return (
      <CaptchaField
        pages={pages}
        field={item}
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
        selectField={selectField}
        setSelectedSettingOption={setSelectedSettingOption}
        openDeleteFieldModal={openDeleteFieldModal}
        duplicateField={duplicateField}
      />
    );
  }

  if (item.type === "WelcomePageField") {
    return (
      <WelcomePageField
        selectedField={selectedField}
        setSelectedField={setSelectedField}
        selectedTheme={selectedTheme}
      />
    );
  }
}

export default FieldRender;
