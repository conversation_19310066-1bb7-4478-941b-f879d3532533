import { useEffect } from "react";
import "./ThankyouPage.css";

import { MdDeleteOutline } from "react-icons/md";
import ThankyouPageField from "../Fields/ThankyouPageField";

const ThankyouPage = ({
  field,
  pages,
  selectField,
  selectedField,
  setSelectedField,
  selectedTheme,
  setDeleteThankyouPageModal,
}) => {
  useEffect(() => {});

  return (
    <div className="thankyouPage-outer-container">
      <div className="thankyouPage-top-container">
        <div className="thankyouPage-top-title">Thank You Page</div>
        <div
          className="thankyouPage-top-remove"
          onClick={() => setDeleteThankyouPageModal(true)}
        >
          <div className="thankyouPage-top-delete-icon">
            <MdDeleteOutline />
          </div>
          Remove Thank you Page
        </div>
      </div>
      <div
        className="thankyouPage-container"
        onClick={() => selectField(field)}
        style={{
          backgroundColor:
            selectedTheme && !selectedTheme.backgroundImage
              ? selectedTheme.backgroundColor
              : "",
          backgroundImage: selectedTheme
            ? "url(" + selectedTheme.backgroundImage + ")"
            : "",
          backgroundRepeat: "round",
          border:
            selectedField?.type === "ThankyouPageField"
              ? "1px solid var(--black) !important"
              : "",
          boxShadow:
            selectedField?.type === "ThankyouPageField"
              ? "rgba(0, 0, 0, 0.25) 1px 1.5px 3px !important"
              : "",
        }}
      >
        <ThankyouPageField
          field={field}
          selectedField={selectedField}
          selectedTheme={selectedTheme}
        />
      </div>
    </div>
  );
};

export default ThankyouPage;
