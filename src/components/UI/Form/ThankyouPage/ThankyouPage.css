.thankyouPage-outer-container {
    margin-bottom: 30px;
}

.thankyouPage-top-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
}

.thankyouPage-top-title {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3px 10px;
    background-color: var(--white);
    height: 25px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
}

.thankyouPage-top-remove {
    display: none;
    align-items: center;
    justify-content: center;
    padding: 3px 10px;
    background-color: var(--white);
    border: 1px solid var(--red100);
    color: var(--red100);
    height: 25px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
}


.thankyouPage-top-container:hover .thankyouPage-top-remove {
    display: flex;
}

.thankyouPage-top-container:hover .thankyouPage-top-title {
    display: none;
}

.thankyouPage-top-delete-icon {
    cursor: pointer;
    font-size: 16px;
    color: var(--red100);
    border-radius: 100%;
    margin-right: 5px;
}

.thankyouPage-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 800px;
    padding: 20px;
    min-height: 400px;
    cursor: pointer;
    border: 1px solid var(--grey300);
    box-shadow: rgba(0, 0, 0, 0.15) 0px 1px 4px;
}

@media screen and (width < 1600px) {
    .thankyouPage-container {
        width: 650px;
    }
}

@media screen and (width < 1400px) {
    .thankyouPage-container {
        width: 550px;
    }
}

@media screen and (width < 1250px) {
    .thankyouPage-container {
        width: 350px;
    }
}