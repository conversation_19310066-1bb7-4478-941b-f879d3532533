.welcomePage-outer-container {
    margin: 30px 0;

}

.welcomePage-top-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
}

.welcomePage-top-title {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3px 10px;
    background-color: var(--white);
    height: 25px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
}

.welcomePage-top-remove {
    display: none;
    align-items: center;
    justify-content: center;
    padding: 3px 10px;
    background-color: var(--white);
    border: 1px solid var(--red100);
    color: var(--red100);
    height: 25px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
}


.welcomePage-top-container:hover .welcomePage-top-remove {
    display: flex;
}

.welcomePage-top-container:hover .welcomePage-top-title {
    display: none;
}

.welcomePage-top-delete-icon {
    cursor: pointer;
    font-size: 16px;
    color: var(--red100);
    border-radius: 100%;
    margin-right: 5px;
}

.welcomePage-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 800px;
    padding: 20px;
    min-height: 400px;
    cursor: pointer;
    border: 1px solid var(--grey300);
    /*box-shadow: rgba(0, 0, 0, 0.15) 0px 1px 4px;*/
}

@media screen and (width < 1600px) {
    .welcomePage-container {
        width: 650px;
    }
}

@media screen and (width < 1400px) {
    .welcomePage-container {
        width: 550px;
    }
}

@media screen and (width < 1250px) {
    .welcomePage-container {
        width: 350px;
    }
}