import { useEffect } from "react";
import "./WelcomePage.css";
import WelcomePageField from "../Fields/WelcomePageField";
import { MdDeleteOutline } from "react-icons/md";

const WelcomePage = ({
  field,
  selectField,
  selectedField,
  setSelectedField,
  selectedTheme,
  setDeleteWelcomePageModal,
}) => {
  return (
    <div className="welcomePage-outer-container">
      <div className="welcomePage-top-container">
        <div className="welcomePage-top-title">Welcome Page</div>
        <div
          className="welcomePage-top-remove"
          onClick={() => setDeleteWelcomePageModal(true)}
        >
          <div className="welcomePage-top-delete-icon">
            <MdDeleteOutline />
          </div>
          Remove Welcome Page
        </div>
      </div>
      <div
        className="welcomePage-container"
        onClick={() => {
          selectField(field);
        }}
        style={{
          backgroundColor:
            selectedTheme && !selectedTheme.backgroundImage
              ? selectedTheme.backgroundColor
              : "",
          backgroundImage: selectedTheme
            ? "url(" + selectedTheme.backgroundImage + ")"
            : "",
          backgroundRepeat: "round",
          border:
            selectedField?.type === "WelcomePageField"
              ? "1px solid var(--black) !important"
              : "",
          boxShadow:
            selectedField?.type === "WelcomePageField"
              ? "rgba(0, 0, 0, 0.25) 1px 1.5px 3px !important"
              : "",
        }}
      >
        <WelcomePageField
          field={field}
          selectedField={selectedField}
          selectField={selectField}
          selectedTheme={selectedTheme}
        />
      </div>
    </div>
  );
};

export default WelcomePage;
