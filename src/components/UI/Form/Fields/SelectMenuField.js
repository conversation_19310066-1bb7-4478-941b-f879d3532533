import { TbGripVertical } from "react-icons/tb";
import "./FieldGlobal.css";
import { useEffect, useRef, useState } from "react";
import { MdDeleteOutline, MdWarning } from "react-icons/md";
import { HiOutlineDuplicate } from "react-icons/hi";
import { Select } from "antd";
import { IoSettingsOutline } from "react-icons/io5";
import { generateOptionId } from "../FieldUtil";
import { deepClone } from "../../../../pages/form/FormUtil";
import OptionSettingsModal from "../../Modals/OptionSettingsModal";

const SelectMenuField = ({
  loading,
  setLoading,
  field,
  selectedField,
  selectedTheme,
  selectField,
  openDeleteFieldModal,
  duplicateField,
  saveOptions,
}) => {
  const [tempField, setTempField] = useState(deepClone(field));
  const [openOptionsModal, setOpenOptionsModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState();
  const ref = useRef();

  useEffect(() => {}, [tempField]);

  useEffect(() => {
    if (field.options !== tempField.options) {
      setTempField(deepClone(field));
    }
  }, [openOptionsModal]);

  const getInputBorderRadius = () => {
    if (selectedTheme.rounded === 1) {
      return "0px";
    } else if (selectedTheme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  const onChangeOption = (e, option) => {
    const value = e.target.value;
    const changedOption = tempField.options.find((o) => o.id === option.id);
    changedOption.fieldId = selectedField.id;
    changedOption.label = value;
    changedOption.value = value;
    const updatedField = tempField;
    updatedField.options.splice(changedOption.order, 1, changedOption);
    setTempField(updatedField);
  };

  const deleteOption = (option) => {
    if (tempField.options.length === 1) {
      setErrorMessage("There must be at least one option !");
      return;
    }
    setLoading(true);
    const temp = { ...tempField };
    const deletedOptionIndex = temp.options.findIndex(
      (opt) => opt.id === option.id
    );
    const updatedOptions = [...temp.options];
    updatedOptions.splice(deletedOptionIndex, 1);
    temp.options = updatedOptions;
    setTempField(temp);
    setTimeout(() => {
      setLoading(false);
    }, 10);
  };

  const addOption = () => {
    setErrorMessage(null);
    setLoading(true);

    const newOption = {
      id: generateOptionId(),
      fieldId: selectedField.id,
      value: "",
      label: "",
      order:
        tempField.options && tempField.options.length > 0
          ? tempField.options.length
          : 0,
    };

    const changedField = { ...tempField };
    changedField.options = [...changedField.options, newOption];
    setTempField(changedField);

    setTimeout(() => {
      setLoading(false);
    }, 10);
  };

  const saveChanges = async () => {
    setErrorMessage(null);
    setLoading(true);
    await saveOptions(tempField.options);

    setTimeout(() => {
      setLoading(false);
      setOpenOptionsModal(false);
    }, 10);
  };

  const handleDragAndDrop = (results) => {
    setErrorMessage(null);
    const { source, destination, type } = results;

    if (!destination) return;

    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    )
      return;

    if (type === "group") {
      const reorderedOptions = tempField.options;

      const optionSourceIndex = source.index;
      const optionDestinatonIndex = destination.index;

      const [removedOption] = reorderedOptions.splice(optionSourceIndex, 1);
      reorderedOptions.splice(optionDestinatonIndex, 0, removedOption);

      reorderedOptions.forEach((element, index) => {
        element.order = index;
      });

      setTempField(tempField);
    }
  };

  return (
    <>
      <div
        ref={ref}
        style={{
          border:
            selectedField && selectedField.id === field.id
              ? "1px solid var(--grey100)"
              : "",
        }}
        className="field-container"
        onClick={() => selectField(field)}
      >
        <div
          className="field-settings-container"
          style={{
            visibility:
              selectedField && selectedField.id === field.id ? "visible" : "",
          }}
        >
          <div className="field-settings-inner-container">
            <div
              className="field-settings-duplicate-button"
              onClick={duplicateField}
            >
              <HiOutlineDuplicate className="field-settings-duplicate-button-icon" />
            </div>

            <div className="field-settings-setting-button">
              <IoSettingsOutline className="field-settings-setting-button-icon" />
            </div>

            <div
              className="field-settings-delete-button"
              onClick={openDeleteFieldModal}
            >
              <MdDeleteOutline className="field-settings-delete-button-icon" />
            </div>
          </div>
        </div>

        <div className="field-field-inner-container">
          <div className="field-field-container">
            <div
              style={{
                display: "flex",
                flexDirection:
                  field.textAlignment === "LEFT" ||
                  field.textAlignment === "RIGHT"
                    ? "row"
                    : "column",
                justifyContent:
                  field.textAlignment === "LEFT"
                    ? "space-between"
                    : field.textAlignment === "RIGHT"
                    ? "flex-end"
                    : "",
              }}
            >
              <div
                className="field-field-label"
                style={{
                  fontFamily: selectedTheme ? selectedTheme.font : "Inter",
                  color: selectedTheme ? selectedTheme.questionColor : "",
                  textAlign: field["textAlignment"],
                }}
              >
                {field.title}
                {field["required"] && (
                  <span className="field-field-required">*</span>
                )}
              </div>

              <div className="field-field-input-container">
                <div
                  className="field-field-input-cover"
                  style={{
                    /*borderColor: selectedTheme
                        ? selectedTheme.questionColor
                        : "",*/
                    borderRadius: selectedTheme
                      ? getInputBorderRadius()
                      : "3px",
                  }}
                >
                  <Select
                    className="field-field-input"
                    style={{
                      borderColor: selectedTheme
                        ? selectedTheme.questionColor
                        : "",
                      borderRadius: selectedTheme ? getInputBorderRadius() : "",
                    }}
                    options={field.options}
                    placeholder={field.placeholder}
                  />
                </div>
                {selectedField && selectedField.id === field.id && (
                  <div
                    className="field-field-editOptions"
                    onClick={() => setOpenOptionsModal(true)}
                  >
                    Edit options
                  </div>
                )}
              </div>
              <div
                className="field-field-description"
                style={{
                  fontFamily: selectedTheme ? selectedTheme.font : "Inter",
                  color: selectedTheme ? selectedTheme.answerColor : "",
                  textAlign: field["textAlignment"],
                }}
              >
                {field.description}
              </div>
            </div>
          </div>
          <div className="field-holder-container">
            <TbGripVertical />
          </div>
        </div>
        {field && field["hide"] && (
          <div className="field-hide-container">
            <MdWarning className="field-hide-icon" />
            <div className="field-hide-text">
              This field is hidden and will not be seen on the form
            </div>
          </div>
        )}
      </div>

      <OptionSettingsModal
        field={tempField}
        open={openOptionsModal}
        onOk={saveChanges}
        onCancel={() => setOpenOptionsModal(false)}
        handleDragAndDrop={handleDragAndDrop}
        onChangeOption={onChangeOption}
        loading={loading}
        errorMessage={errorMessage}
        addOption={addOption}
        deleteOption={deleteOption}
      />
    </>
  );
};

export default SelectMenuField;
