import "./WelcomePageField.css";

const WelcomePageField = ({ field, selectedTheme }) => {
  const getInputBorderRadius = () => {
    if (selectedTheme.rounded === 1) {
      return "0px";
    } else if (selectedTheme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };
  return (
    <div
      className="welcomePageField-container"
      style={{
        fontFamily:
          selectedTheme && selectedTheme.font ? selectedTheme.font : "Inter",
        color:
          selectedTheme && selectedTheme.questionColor
            ? selectedTheme.questionColor
            : "",
      }}
    >
      <div className="welcomePageField-title">{field?.title}</div>
      <div className="welcomePageField-description">{field?.description}</div>
      <div
        className="welcomePageField-button"
        style={{
          borderRadius:
            selectedTheme && selectedTheme.rounded
              ? getInputBorderRadius()
              : "",
          backgroundColor:
            selectedTheme && selectedTheme.buttonColor
              ? selectedTheme.buttonColor
              : "#000",
        }}
      >
        {field?.buttonText}
      </div>
    </div>
  );
};

export default WelcomePageField;
