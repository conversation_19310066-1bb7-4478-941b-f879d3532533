import "./ThankyouPageField.css";
import { MdOutlineMarkEmailRead } from "react-icons/md";

const ThankyouPageField = ({ field, selectedTheme }) => {
  const getInputBorderRadius = () => {
    if (selectedTheme.rounded === 1) {
      return "0px";
    } else if (selectedTheme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };
  return (
    <div
      className="thankyouPageField-container"
      style={{
        fontFamily:
          selectedTheme && selectedTheme.font ? selectedTheme.font : "Inter",
        color:
          selectedTheme && selectedTheme.questionColor
            ? selectedTheme.questionColor
            : "",
      }}
    >
      <div
        className="thankyouPage-icon"
        style={{
          color:
            selectedTheme && selectedTheme.questionColor
              ? selectedTheme.questionColor
              : "",
        }}
      >
        <MdOutlineMarkEmailRead />
      </div>
      <div className="thankyouPageField-title">{field?.title}</div>
      <div className="thankyouPageField-description">{field?.description}</div>
      {field?.redirectUrl && field.redirectUrl?.length > 5 && (
        <div
          className="thankyouPageField-redirectUrl"
          style={{
            borderRadius:
              selectedTheme && selectedTheme.rounded
                ? getInputBorderRadius()
                : "",
            backgroundColor:
              selectedTheme && selectedTheme.buttonColor
                ? selectedTheme.buttonColor
                : "#000",
          }}
        >
          <a href={field?.redirectUrl}>{field?.redirectUrl}</a>
        </div>
      )}
    </div>
  );
};

export default ThankyouPageField;
