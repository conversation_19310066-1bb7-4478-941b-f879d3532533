import { TbGripVertical } from "react-icons/tb";
import "./FieldGlobal.css";
import { useRef } from "react";
import {
  MdDeleteOutline,
  MdOutlineCloudUpload,
  MdWarning,
} from "react-icons/md";
import { HiOutlineDuplicate } from "react-icons/hi";
import { IoSettingsOutline } from "react-icons/io5";

const FileUploadField = ({
  field,
  selectedField,
  selectedTheme,
  selectField,
  openDeleteFieldModal,
  duplicateField,
}) => {
  const ref = useRef();

  const getInputBorderRadius = () => {
    if (selectedTheme.rounded === 1) {
      return "0px";
    } else if (selectedTheme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  return (
    <div
      ref={ref}
      style={{
        border:
          selectedField && selectedField.id === field.id
            ? "1px solid var(--grey100)"
            : "",
      }}
      className="field-container"
      onClick={() => selectField(field)}
    >
      <div
        className="field-settings-container"
        style={{
          visibility:
            selectedField && selectedField.id === field.id ? "visible" : "",
        }}
      >
        <div className="field-settings-inner-container">
          <div
            className="field-settings-duplicate-button"
            onClick={duplicateField}
          >
            <HiOutlineDuplicate className="field-settings-duplicate-button-icon" />
          </div>

          <div className="field-settings-setting-button">
            <IoSettingsOutline className="field-settings-setting-button-icon" />
          </div>

          <div
            className="field-settings-delete-button"
            onClick={openDeleteFieldModal}
          >
            <MdDeleteOutline className="field-settings-delete-button-icon" />
          </div>
        </div>
      </div>

      <div className="field-field-inner-container">
        <div className="field-field-container">
          <div
            style={{
              display: "flex",
              flexDirection:
                field.textAlignment === "LEFT" ||
                field.textAlignment === "RIGHT"
                  ? "row"
                  : "column",
              justifyContent:
                field.textAlignment === "LEFT"
                  ? "space-between"
                  : field.textAlignment === "RIGHT"
                  ? "flex-end"
                  : "",
            }}
          >
            <div
              className="field-field-label"
              style={{
                fontFamily: selectedTheme ? selectedTheme.font : "Inter",
                color: selectedTheme ? selectedTheme.questionColor : "",
                textAlign: field["textAlignment"],
              }}
            >
              {field.title}
              {field["required"] && (
                <span
                  style={{
                    color: "var(--red100)",
                    fontWeight: "600",
                    marginLeft: "10px",
                  }}
                >
                  *
                </span>
              )}
            </div>
            <div style={{ width: "100%" }}>
              <div
                className="field-field-input-upload-container"
                style={{
                  borderColor: selectedTheme ? selectedTheme.questionColor : "",
                  borderRadius: selectedTheme ? getInputBorderRadius() : "",
                }}
              >
                <div
                  className="field-field-input-icon"
                  style={{
                    color: selectedTheme ? selectedTheme.questionColor : "",
                  }}
                >
                  <MdOutlineCloudUpload
                    style={{
                      color: selectedTheme ? selectedTheme.questionColor : "",
                    }}
                  />
                </div>
                <div
                  className="field-field-input-placeholder"
                  style={{
                    color: selectedTheme ? selectedTheme.questionColor : "",
                    fontFamily: selectedTheme ? selectedTheme.font : "Inter",
                  }}
                >
                  {field.placeholder}
                </div>
                <div
                  className="field-field-input-description"
                  style={{
                    color: selectedTheme ? selectedTheme.questionColor : "",
                    fontFamily: selectedTheme ? selectedTheme.font : "Inter",
                  }}
                >
                  Drag and drop files here
                </div>
              </div>

              <div
                className="field-field-description"
                style={{
                  fontFamily: selectedTheme ? selectedTheme.font : "Inter",
                  color: selectedTheme ? selectedTheme.answerColor : "",
                  textAlign: field["textAlignment"],
                }}
              >
                {field.description}
              </div>
            </div>
          </div>
        </div>
        <div className="field-holder-container">
          <TbGripVertical />
        </div>
      </div>
      {field && field["hide"] && (
        <div className="field-hide-container">
          <MdWarning className="field-hide-icon" />
          <div className="field-hide-text">
            This field is hidden and will not be seen on the form
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUploadField;
