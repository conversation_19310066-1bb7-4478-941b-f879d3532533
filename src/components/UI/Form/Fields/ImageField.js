import { TbGripVertical } from "react-icons/tb";
import "./FieldGlobal.css";
import { useRef } from "react";
import { MdDeleteOutline, MdWarning } from "react-icons/md";
import { HiOutlineDuplicate } from "react-icons/hi";
import { IoSettingsOutline } from "react-icons/io5";
import { PiImageDuotone } from "react-icons/pi";

const ImageField = ({
  field,
  selectedField,
  selectField,
  openDeleteFieldModal,
  duplicateField,
}) => {
  const ref = useRef();

  return (
    <div
      ref={ref}
      style={{
        border:
          selectedField && selectedField.id === field.id
            ? "1px solid var(--grey100)"
            : "",
      }}
      className="field-container"
      onClick={() => selectField(field)}
    >
      <div
        className="field-settings-container"
        style={{
          visibility:
            selectedField && selectedField.id === field.id ? "visible" : "",
        }}
      >
        <div className="field-settings-inner-container">
          <div
            className="field-settings-duplicate-button"
            onClick={duplicateField}
          >
            <HiOutlineDuplicate className="field-settings-duplicate-button-icon" />
          </div>

          <div className="field-settings-setting-button">
            <IoSettingsOutline className="field-settings-setting-button-icon" />
          </div>

          <div
            className="field-settings-delete-button"
            onClick={openDeleteFieldModal}
          >
            <MdDeleteOutline className="field-settings-delete-button-icon" />
          </div>
        </div>
      </div>
      {field.file || field.fileUrl ? (
        <div
          style={{
            width: "100%",
            display: "flex",
            justifyContent: field.textAlignment,
          }}
        >
          {field.file?.file?.thumbUrl && (
            <img
              src={field.file.file.thumbUrl}
              style={{ width: field.width, height: field.height }}
              alt="fileImage"
            />
          )}
          {field.file?.thumbUrl && (
            <img
              src={field.file.thumbUrl}
              style={{ width: field.width, height: field.height }}
              alt="fileImage"
            />
          )}
          {!field.file && field.fileUrl && (
            <img
              src={`${process.env.REACT_APP_BACKEND_URL}/api/v1/response/files/image/${field.fileUrl}`}
              style={{ width: field.width, height: field.height }}
              alt="fileImage"
            />
          )}
        </div>
      ) : (
        <div className="field-imageField-inner-container">
          <div className="field-imageField-image">
            <PiImageDuotone />
          </div>

          <div className="field-holder-container">
            <TbGripVertical />
          </div>
        </div>
      )}

      {field && field["hide"] && (
        <div className="field-hide-container">
          <MdWarning className="field-hide-icon" />
          <div className="field-hide-text">
            This field is hidden and will not be seen on the form
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageField;
