import { TbGripVertical } from "react-icons/tb";
import "./FieldGlobal.css";
import { useRef } from "react";
import { MdDeleteOutline, MdWarning } from "react-icons/md";
import { HiOutlineDuplicate } from "react-icons/hi";
import { IoSettingsOutline } from "react-icons/io5";

const DividerField = ({
  field,
  selectedField,

  selectField,
  openDeleteFieldModal,
  duplicateField,
}) => {
  const ref = useRef();

  return (
    <div
      ref={ref}
      style={{
        border:
          selectedField && selectedField.id === field.id
            ? "1px solid var(--grey100)"
            : "",
      }}
      className="field-container"
      onClick={() => selectField(field)}
    >
      <div
        className="field-settings-container"
        style={{
          visibility:
            selectedField && selectedField.id === field.id ? "visible" : "",
        }}
      >
        <div
          className="field-settings-duplicate-button"
          onClick={duplicateField}
        >
          <HiOutlineDuplicate className="field-settings-duplicate-button-icon" />
        </div>

        <div className="field-settings-setting-button">
          <IoSettingsOutline className="field-settings-setting-button-icon" />
        </div>

        <div
          className="field-settings-delete-button"
          onClick={openDeleteFieldModal}
        >
          <MdDeleteOutline className="field-settings-delete-button-icon" />
        </div>
      </div>

      <div className="field-field-inner-container">
        <div style={{ width: "100%" }}>
          <div
            className="field-divider"
            style={{
              borderWidth: field.dividerHeight
                ? field.dividerHeight + "px"
                : "1px",
              borderStyle: field.dividerStyle ? field.dividerStyle : "Solid",
              marginBottom: field.spaceBelow ? field.spaceBelow + "px" : "5px",
              marginTop: field.spaceAbove ? field.spaceAbove + "px" : "5px",
              borderColor: field.lineColor ? field.lineColor : "var(--grey200)",
            }}
          />
        </div>

        <div className="field-holder-container">
          <TbGripVertical />
        </div>
      </div>
      {field && field["hide"] && (
        <div className="field-hide-container">
          <MdWarning className="field-hide-icon" />
          <div className="field-hide-text">
            This field is hidden and will not be seen on the form
          </div>
        </div>
      )}
    </div>
  );
};

export default DividerField;
