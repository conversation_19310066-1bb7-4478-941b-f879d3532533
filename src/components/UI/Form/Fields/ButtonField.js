import "./ButtonField.css";

const ButtonField = ({ pages, page, selectedTheme }) => {
  const getInputBorderRadius = () => {
    if (selectedTheme.rounded === 1) {
      return "0px";
    } else if (selectedTheme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  if (pages.length === 1) {
    return (
      <div
        className="buttonField-container"
        style={{
          justifyContent: "flex-end",
        }}
      >
        <div
          className="buttonField-button-container"
          style={{
            fontFamily:
              selectedTheme && selectedTheme.font
                ? selectedTheme.font
                : "Inter",
            backgroundColor:
              selectedTheme && selectedTheme.buttonColor
                ? selectedTheme.buttonColor
                : "",
            color:
              selectedTheme && selectedTheme.buttonTextColor
                ? selectedTheme.buttonTextColor
                : "",
            borderRadius:
              selectedTheme && selectedTheme.rounded
                ? getInputBorderRadius()
                : "",
          }}
        >
          Submit
        </div>
      </div>
    );
  }

  if (pages.length > 1 && pages[0].id === page.id) {
    return (
      <div
        className="buttonField-container"
        style={{
          justifyContent: "flex-end",
        }}
      >
        <div
          className="buttonField-button-container"
          style={{
            fontFamily:
              selectedTheme && selectedTheme.font
                ? selectedTheme.font
                : "Inter",
            backgroundColor:
              selectedTheme && selectedTheme.buttonColor
                ? selectedTheme.buttonColor
                : "",
            color:
              selectedTheme && selectedTheme.buttonTextColor
                ? selectedTheme.buttonTextColor
                : "",
            borderRadius:
              selectedTheme && selectedTheme.rounded
                ? getInputBorderRadius()
                : "",
          }}
        >
          Next
        </div>
      </div>
    );
  }

  if (pages.length > 1 && pages[pages.length - 1].id === page.id) {
    return (
      <div
        className="buttonField-container"
        style={{
          justifyContent: "space-between",
        }}
      >
        <div
          className="buttonField-backButton-container"
          style={{
            fontFamily:
              selectedTheme && selectedTheme.font
                ? selectedTheme.font
                : "Inter",
            backgroundColor:
              selectedTheme && selectedTheme.buttonColor
                ? selectedTheme.buttonColor
                : "",
            color:
              selectedTheme && selectedTheme.buttonTextColor
                ? selectedTheme.buttonTextColor
                : "",
            borderRadius:
              selectedTheme && selectedTheme.rounded
                ? getInputBorderRadius()
                : "",
          }}
        >
          Back
        </div>
        <div
          className="buttonField-button-container"
          style={{
            fontFamily:
              selectedTheme && selectedTheme.font
                ? selectedTheme.font
                : "Inter",
            backgroundColor:
              selectedTheme && selectedTheme.buttonColor
                ? selectedTheme.buttonColor
                : "",
            color:
              selectedTheme && selectedTheme.buttonTextColor
                ? selectedTheme.buttonTextColor
                : "",
            borderRadius:
              selectedTheme && selectedTheme.rounded
                ? getInputBorderRadius()
                : "",
          }}
        >
          Submit
        </div>
      </div>
    );
  }

  return (
    <div
      className="buttonField-container"
      style={{
        justifyContent: "space-between",
      }}
    >
      <div
        className="buttonField-backButton-container"
        style={{
          fontFamily:
            selectedTheme && selectedTheme.font ? selectedTheme.font : "Inter",
          backgroundColor:
            selectedTheme && selectedTheme.buttonColor
              ? selectedTheme.buttonColor
              : "",
          color:
            selectedTheme && selectedTheme.buttonTextColor
              ? selectedTheme.buttonTextColor
              : "",
          borderRadius:
            selectedTheme && selectedTheme.rounded
              ? getInputBorderRadius()
              : "",
        }}
      >
        Back
      </div>
      <div
        className="buttonField-button-container"
        style={{
          fontFamily:
            selectedTheme && selectedTheme.font ? selectedTheme.font : "Inter",
          backgroundColor:
            selectedTheme && selectedTheme.buttonColor
              ? selectedTheme.buttonColor
              : "",
          color:
            selectedTheme && selectedTheme.buttonTextColor
              ? selectedTheme.buttonTextColor
              : "",
          borderRadius:
            selectedTheme && selectedTheme.rounded
              ? getInputBorderRadius()
              : "",
        }}
      >
        Next
      </div>
    </div>
  );
};

export default ButtonField;
