.field-container {
    width: 100%;
    margin-bottom: 10px;
    padding: 5px;
}

.field-container:hover .field-holder-container {
    visibility: visible;
}

.field-settings-container {
    position: relative;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    visibility: hidden;
    margin-bottom: 5px;
}

.field-settings-inner-container {
    display: flex;
    width: fit-content;
    padding: 5px 5px 5px 10px ;
    position: absolute;
    top: 0;
    right: 0;
    border-bottom-left-radius: 20px;
}

.field-settings-duplicate-button {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--white);
    border: 1px solid var(--grey200);
    border-radius: 5px;
    margin-right: 10px;
    cursor: pointer;
    width: 24px;
    height: 24px;
}

.field-settings-duplicate-button:hover {
    background-color: var(--grey300);
    transition: .5s all;
}

.field-settings-duplicate-button-icon {
    font-size: 18px;
    color: var(--black);
}

.field-settings-duplicate-button-text {
    font-size: 12px;
    font-weight: 600;
    color: var(--black);
    font-family: "Exo 2", sans-serif;
}

.field-settings-setting-button {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--white);
    border: 1px solid var(--grey200);
    border-radius: 5px;
    margin-right: 10px;
    cursor: pointer;
    width: 24px;
    height: 24px;
}

.field-settings-setting-button:hover {
    background-color: var(--grey300);
    color: var(--white);
    transition: .5s all;
}

.field-settings-setting-button-icon {
    font-size: 18px;
    color: var(--black);
}

.field-settings-setting-button-text {
    font-size: 12px;
    font-weight: 600;
    color: var(--black);
    font-family: "Exo 2", sans-serif;
}

.field-settings-delete-button {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--white);
    border: 1px solid var(--grey200);
    border-radius: 5px;
    margin-right: 5px;
    cursor: pointer;
    width: 24px;
    height: 24px;
}

.field-settings-delete-button:hover {
    background-color: var(--grey300);
    color: var(--black);
    transition: .5s all;
}

.field-settings-delete-button-icon {
    font-size: 18px;
    color: var(--black);
}

.field-settings-delete-button-text {
    font-size: 12px;
    font-weight: 600;
    color: var(--black);
    font-family: "Exo 2", sans-serif;
}

.field-field-editOptions {
    font-size: 12px; 
    background-color: var(--white);
    border: 1px solid var(--grey200);
    color: var(--grey100);
    cursor: pointer;
    color: var(--black);
    border-radius: 3px;
    margin: 10px 0;
    padding: 0 15px;
    text-align: center;
}

.field-field-editOptions:hover {
    background-color: var(--grey300);
    transition: .5s all;
}

.field-optionsModal-outer-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.field-optionsModal-container {
    display: flex;
    align-items: center;   
}

.field-optionsModal-holder-container {
    visibility: hidden;
    color: var(--grey100);
    padding-right: 20px;
    font-size: 18px;
}

.field-optionsModal-outer-container:hover .field-optionsModal-holder-container {
    visibility: visible;
}

.field-optionsModal-buttonContainer {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
    margin-bottom: -10px;
}

.field-optionsModal-optionInput {
    border: 1px solid var(--grey200);
    padding: 3px 0 3px 10px;
    width: 250px;
}

.field-optionsModal-deleteOption {
    color: var(--red100);
    font-size: 12px;
    margin-left: 10px;
    cursor: pointer;
}

.field-optionsModal-deleteOption:hover {
    opacity: .8;
}

.field-optionsModal-error {
    display: flex;
    align-items: center;
    font-family: "Exo 2", sans-serif;
    color: var(--red100);
    font-size: 13px;
}

.field-optionsModal-addOption {
    display: flex;
    align-items: center;
    margin-top: 25px;
    cursor: pointer;
    width: fit-content;
}

.field-optionsModal-addOption:hover {
    opacity: .8;
}

.field-optionsModal-addOption-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 5px;
    font-size: 14px;
    color: var(--black);
}

.field-optionsModal-addOption-title {
    font-size: 14px;
    font-family: 'Exo 2', sans-serif;
    font-weight: 500;
    width: 100%;
}

.field-field-inner-container {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
}

.field-imageField-inner-container {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
}

.field-imageField-image {
    width: 200px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 80px;
    color: var(--grey100);
    padding: 5px;
    background-color: var(--grey400);
    border: 1px solid var(--grey200);
}

.field-field-container {
    padding: 5px;
    width: 100%;
}

.field-field-label {
    display: flex;
    font-size: 15px;
    font-weight: 500;
    margin: 15px 20px 5px 0;
}

.field-header1-field-label {
    font-size: 30px;
    font-weight: 500;
}

.field-header2-field-label {
    font-size: 24px;
    font-weight: 500;
}

.field-header3-field-label {
    font-size: 20px;
    font-weight: 500;
}

.field-field-required {
    color: var(--red100);
    font-weight: 600;
    margin-left: 10px;
}

.field-field-description {
    display: flex;
    font-size: 12px;
    opacity: .9;
    margin-top: 5px;
}

.field-holder-container {
    visibility: hidden;
    color: var(--grey100);
    font-size: 18px;
}

.field-hide-container {
    width: auto;
    display: flex;
    align-content: center;
    background-color: #fff7ed;
    border: 1px solid #ff8800;
    border-radius: 10px;
    padding: 1px 10px;
}

.field-hide-icon {
    font-size: 13px;
    color: #ff8800;
    margin-right: 5px;
    margin-top: 2px;
}

.field-hide-text {
    font-size: 11px;
    color: #ff8800;
    font-family: "Exo 2", sans-serif;
}

.field-field-input-container {
    width: 100%; 
    display: flex; 
    flex-direction: column;
    align-items: flex-start;
}

.field-field-input-cover {
    padding: 7px;
    background: #fff;
    border-width: 1px;
    width: 100%;
}

.field-field-input-cover:hover {
    border-width: 1px;
    border-color: var(--grey100);
}

.field-field-input-cover:focus {
    border-color: var(--grey100);
    border-width: 1px;
}

.field-field-input {
   /* width: 300px !important;*/
    /*border-color: var(--grey200) !important;*/
    width: 100% !important;
    
}


.field-field-input:hover {
    border-color: var(--grey200) !important;
}

.field-field-input:focus {
    border-color: var(--grey300);
    border-width: 1px;
}

.field-field-input-upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 15px;
    border: 1px dashed var(--grey200);
}

.field-field-input-icon {
    font-size: 42px;
}

a {
    color: var(--black);
}

a:hover {
    color: var(--black);
    opacity: .8;
}

a:focus {
    color: var(--black);
    opacity: .8;
}

.ant-picker-outlined::after{
    border-color: var(--grey200);
    box-shadow: 0 0 0 0 rgba(5, 145, 255, 0)
}

.ant-select-selector {
    background-color: rgb(0,0,0,0);
    border-radius: 0 !important;
    border-color: var(--grey200) !important;
    border-width: 0 !important;
    border-color: var(--grey200);
}

.ant-select-focused {
    border-width: 0 !important;
    border-color: red !important;
}

.ant-select-selector:hover {
    border-color: var(--grey100) !important;
    border-width: 0 !important;
    box-shadow: 0 0 0 0px rgba(0, 145, 255, 0) !important;
}

.ant-select:hover {
    border-width: 0 !important;
    border-color: var(--grey100) !important;
}

.ant-select-single {
    height: 25px !important;
}


.ant-btn-primary {
    background-color: var(--black) !important;
}

.ant-picker-dropdown .ant-picker-time-panel-column >li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
    background-color: var(--grey200);
}

.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-selected:not(.ant-picker-cell-disabled) .ant-picker-cell-inner{
    background-color: var(--grey100);
}

.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
    border-color: var(--black);
}

.ant-btn-primary {
    background-color: var(--white) !important;
    color: var(--black);
}

.ant-btn:hover {
    background-color: var(--black) !important;
    color: var(--white) !important;
    opacity: 1 !important;
}

.ant-picker-dropdown .ant-picker-time-panel-column >li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
    background-color: var(--grey300);
}

::-ms-input-placeholder {
    color: var(--grey100) !important;
    font-weight: 400 !important;
    font-size: 13px !important;
    font-family: 'Inter', sans-serif;
}

::-ms-input-placeholder {
    color: var(--grey100) !important;
    font-weight: 400 !important;
    font-size: 13px !important;
    font-family: 'Inter', sans-serif;
}


.ant-checkbox-checked:not(.ant-checkbox-disabled) .ant-checkbox-inner {
    background-color: var(--black);
    border-color: var(--black);
}

.ant-checkbox-checked:not(.ant-checkbox-disabled) .ant-checkbox-inner:hover {
    background-color: var(--black) !important;
}

.ant-checkbox-inner {
    border: 1px solid var(--grey100)
}

