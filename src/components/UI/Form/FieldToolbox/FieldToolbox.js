import "./FieldToolbox.css";

import FieldItemList from "./FielItemList";
import FieldItem from "./FieldItem";
import { MdKeyboardArrowDown } from "react-icons/md";
import { useState } from "react";

import { saveForm } from "../../../../services/http";
import {
  generateFiedId,
  generateOptionId,
  pagesWithoutRef,
  pagesWithRef,
} from "../../../../pages/form/FormUtil";
import { useMainContext } from "../../../../context/MainContext";

const FieldToolbox = ({
  setLoading,
  isOpenToolbox,
  handleOpen,
  selectedPage,
  setSelectedPage,
  standartForm,
  selectedTheme,
  setStandartForm,
  usedCaptcha,
  setUsedCaptcha,
  usedGoogleAuthenticate,
  setUsedGoogleAuthenticate,
  usedWeb3,
  setUsedWeb3,
  usedWelcomePage,
  setUsedWelcomePage,
  setWelcomePageItem,
  usedThankyouPage,
  setUsedThankyouPage,
  setThankyouPageItem,
  pages,
  setPages,

  welcomePageRef,
  thankyouPageRef,
}) => {
  const [closedFieldHeaders, setClosedFieldHeaders] = useState([]);

  const mainContext = useMainContext();

  const [fieldList] = useState(
    FieldItemList.reduce((acc, currentItem) => {
      const key = currentItem.headerTitle;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(currentItem);

      return acc;
    }, {})
  );
  let [groupedByHeaderTitle, setGroupedByHeaderTitle] = useState(
    FieldItemList.reduce((acc, currentItem) => {
      const key = currentItem.headerTitle;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(currentItem);

      return acc;
    }, {})
  );

  const openClosedFields = (key) => {
    const index = closedFieldHeaders.indexOf(key);
    const headers = closedFieldHeaders;
    if (index !== -1) {
      headers.splice(index, 1);
    } else {
      headers.push(key);
    }
    setClosedFieldHeaders([...headers]);
  };

  const searchFields = (value) => {
    const lowerCaseKeyword = value?.target?.value.toLowerCase();

    if (lowerCaseKeyword && lowerCaseKeyword.length > 0) {
      const filteredData = [];
      setClosedFieldHeaders([]);

      for (const key in groupedByHeaderTitle) {
        if (groupedByHeaderTitle.hasOwnProperty(key)) {
          filteredData[key] = groupedByHeaderTitle[key].filter((block) => {
            return block.title.toLowerCase().includes(lowerCaseKeyword);
          });
        }
      }

      setGroupedByHeaderTitle(filteredData);
    } else {
      setGroupedByHeaderTitle(fieldList);
    }
  };

  const addField = async (field) => {
    const tempForm = { ...standartForm };
    if (selectedTheme && selectedTheme.id) {
      tempForm.theme = selectedTheme;
    }
    if (field.type === "GoogleAuthenticateField") {
      tempForm.googleAuthenticate = true;
      setUsedGoogleAuthenticate(true);
    }

    if (field.type === "Web3Field") {
      tempForm.web3 = true;
      setUsedWeb3(true);
    }
    const tempPages = [...pages];
    const itemId = generateFiedId();
    let pageId;
    if (field.type === "WelcomePageField") {
      pageId = "welcomePage";
    } else if (field.type === "ThankyouPageField") {
      pageId = "thankyouPage";
    } else {
      pageId = selectedPage.id;
    }

    let modifyTitle;
    if (field.type === "WelcomePageField") {
      modifyTitle = "Welcome. Let's get started";
    } else if (field.type === "ThankyouPageField") {
      modifyTitle = "Thank You !";
    } else {
      modifyTitle = field.title;
    }

    const items = tempForm.pages[selectedPage.order].items
      ? tempForm.pages[selectedPage.order].items
      : [];
    const item = {
      id: itemId,
      pageId: pageId,
      order: items && items.length > 0 ? items.length : 0,
      type: field.type,
      value: null,
      title: modifyTitle,
      published: false,
      placeholder: field.placeholder,
      buttonText: field.buttonText,
      description: field.description,
      redirectUrl: "",
      required: false,
      textSize: "DEFAULT",
      textAlignment: field.textAlignment,
      hide: false,
    };

    if (field.type === "WelcomePageField") {
      setWelcomePageItem(item);
      setUsedWelcomePage(true);
      tempForm.welcomePage = true;
      item.pageId = "welcomePage";
      tempForm.welcomePageItem = item;

      setTimeout(() => {
        welcomePageRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 100);
    }

    if (field.type === "ThankyouPageField") {
      setUsedThankyouPage(true);
      setThankyouPageItem(item);
      tempForm.thankyouPage = true;
      item.pageId = "thankyouPage";
      tempForm.thankyouPageItem = item;

      setTimeout(() => {
        thankyouPageRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 100);
    }

    if (
      field.type === "SelectMenuField" ||
      field.type === "MultiSelectMenuField" ||
      field.type === "CheckboxField" ||
      field.type === "SingleSelectField"
    ) {
      const option1 = {
        id: generateOptionId(),
        fieldId: itemId,
        value: "Option 1",
        label: "Option 1",
        order: 0,
      };
      const option2 = {
        id: generateOptionId(),
        fieldId: itemId,
        value: "Option 2",
        label: "Option 2",
        order: 1,
      };
      const option3 = {
        id: generateOptionId(),
        fieldId: itemId,
        value: "Option 3",
        label: "Option 3",
        order: 2,
      };
      const options = [option1, option2, option3];
      item.options = options;
    }

    if (field.type === "DividerField") {
      item.lineColor = "#d6d6d6";
      item.dividerStyle = "Solid";
      item.dividerHeight = 1;
      item.spaceBelow = 5;
      item.spaceAbove = 5;
    }

    if (
      field.type === "FileUploadField" ||
      field.type === "ImageUploadField" ||
      field.type === "VideoUploadField"
    ) {
      item.filesLimit = 1;
    }

    if (field.type === "ImageField") {
      item.width = 120;
      item.height = 100;
      item.file = null;
    }
    if (item.type === "CaptchaField") {
      item.used = true;
      setUsedCaptcha(true);
      tempForm.captcha = true;
    }
    if (item.type !== "WelcomePageField" && item.type !== "ThankyouPageField") {
      items.push(item);
    }
    const updatingPage = selectedPage;
    updatingPage.items = items;
    tempForm.workspace = mainContext.selectedWorkspace;
    tempPages[selectedPage.order] = updatingPage;
    setSelectedPage(updatingPage);

    try {
      setLoading(true);
      const clearedRefsToPages = pagesWithoutRef(tempPages);
      tempForm.pages = clearedRefsToPages;
      console.log("tempForm : ", tempForm);
      const response = await saveForm(tempForm);
      if (response.status === 200) {
        const savedForm = response.data;
        savedForm.pages = pagesWithRef(tempPages);
        setPages(savedForm.pages);
        //setSelectedField(item);
        setTimeout(() => {
          setTimeout(() => {
            selectedPage.ref?.current?.scrollIntoView({
              block: "end",
              behavior: "smooth",
            });
          }, 100);
        }, 100);

        setStandartForm(savedForm);
      }
    } catch (err) {
      console.log("Adding field error : ", err);
    } finally {
      setLoading(false);
    }
    // setSelectedSettingOption("design");
  };

  return (
    <>
      {!isOpenToolbox && (
        <div
          className="fieldToolbox-open-container"
          onClick={() => handleOpen(!isOpenToolbox)}
        >
          <div>Form Elements</div>
        </div>
      )}

      <div
        className={
          isOpenToolbox
            ? "fieldToolbox-container"
            : "fieldToolbox-container-closed"
        }
      >
        <div className="fieldToolbox-header-container">
          <div>
            <div className="fieldToolbox-header-title">Form Elements</div>
            {/*
            {isOpenToolbox && (
              <div
                className="fieldToolbox-close-container"
                onClick={() => handleOpen(!isOpenToolbox)}
              >
                <IoMdClose />
              </div>
            
            )}
            */}
          </div>
        </div>
        <div className="fieldToolbox-search-container">
          <input
            type="text"
            placeholder="Search fields"
            onChange={(value) => searchFields(value)}
          />
        </div>
        <div className="fieldToolbox-fields">
          {Object.keys(groupedByHeaderTitle).map((key) => (
            <div key={key}>
              <div
                key={key}
                className="fieldToolbox-fields-header-container"
                onClick={() => openClosedFields(key)}
              >
                <div className="fieldToolbox-fields-header-title">{key}</div>
                <div
                  className="fieldToolbox-fields-header-icon"
                  style={{
                    transform: closedFieldHeaders.includes(key)
                      ? "rotate(180deg)"
                      : "",
                  }}
                >
                  <MdKeyboardArrowDown />
                </div>
              </div>
              {!closedFieldHeaders.includes(key) && (
                <ul style={{ marginBottom: "15px" }}>
                  {groupedByHeaderTitle[key]
                    .filter((f) => f.visible)
                    .map((item) => (
                      <li key={item.id}>
                        <FieldItem
                          field={item}
                          addField={addField}
                          usedCaptcha={usedCaptcha}
                          usedGoogleAuthenticate={usedGoogleAuthenticate}
                          usedWeb3={usedWeb3}
                          usedWelcomePage={usedWelcomePage}
                          usedThankyouPage={usedThankyouPage}
                        />
                      </li>
                    ))}
                </ul>
              )}
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default FieldToolbox;
