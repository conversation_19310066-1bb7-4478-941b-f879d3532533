.fieldToolbox-container {
  width: 270px;
  border: 1px solid var(--border);
  background-color: var(--white);
  position: relative;
  top: 90px;
  height: calc(100% - 130px);
  left: 20px;
  transition: 0.15s all;
  transform: scale(1);
  /* box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;*/
  border: 1px solid var(--grey200);
}

.fieldToolbox-header-container {
  display: flex;
  justify-content: space-between;
  padding: 13px 0px 15px 20px;
  background-color: var(--grey400);
  position: fixed;
  width: 268px;
  overflow: hidden;
  height: 55px;
  border-bottom: 1px solid var(--grey200);
}

.fieldToolbox-header-title {
  font-size: 18px;
  font-weight: 600;
  font-family: "Exo 2", sans-serif;
  color: var(--black);
}

.fieldToolbox-container-closed {
  position: fixed;
  visibility: hidden;
  top: 0;
  left: 0;
  transition: 0.15s all;
  transform: scale(0);
}

.fieldToolbox-close-container {
  position: absolute;
  left: 230px;
  top: 10px;
  background-color: var(--red300);
  padding: 4px;
  color: var(--black);
  border-radius: 100%;
  border: 1px solid var(--black);
  border-bottom: 5px solid var(--black);
  cursor: pointer;
  font-size: 18px;
  transition: 0.15s width;
  z-index: 999;
}

.fieldToolbox-close-container:hover {
  border: 1px solid var(--black);
  margin-top: 5px;
  transition: 0.15s all;
}

.fieldToolbox-open-container {
  top: 90px;
  position: fixed;
  left: 20px;
  background: var(--white);
  padding: 8px 20px;
  font-weight: 500;
  border-radius: 30px;
  border: 1px solid var(--black);
  border-bottom: 5px solid var(--black);
  font-size: 18px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: 0.15s width;
}

.fieldToolbox-open-container:hover {
  border: 1px solid var(--black);
  margin-top: 5px;
  transition: 0.15s all;
}

.fieldToolbox-search-container {
  padding: 10px;
  margin-top: 55px;
  overflow: hidden;
}

.fieldToolbox-search-container input {
  display: flex;
  align-items: center;
  border-radius: 3px;
  height: 30px !important;
  width: 100%;
  padding-left: 10px;
  border: 1px solid var(--grey200);
}

.fieldToolbox-search-container input:focus {
  border-width: 1px !important;
}

.fieldToolbox-fields {
  padding: 0 15px;
  overflow-y: scroll;
  height: calc(100% - 145px);
}

.fieldToolbox-fields-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0px;
  padding: 5px;
}

.fieldToolbox-fields-header-title {
  font-weight: 500;
}

.fieldToolbox-fields-header-icon {
  font-size: 20px;
}

::placeholder {
  font-family: "Exo 2", sans-serif;
  color: var(--grey100) !important;
  opacity: 1 !important; /* Firefox */
  font-weight: 400;
  font-size: 14px;
}

::-ms-input-placeholder {
  color: var(--grey100) !important;
  opacity: 1;
}

::-ms-input-placeholder {
  color: var(--grey100) !important;
}
