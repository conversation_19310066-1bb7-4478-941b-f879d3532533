import "./FieldItem.css";

const FieldItem = (props) => {
  const {
    field,
    addField,
    usedCaptcha,
    usedGoogleAuthenticate,
    usedWeb3,
    usedWelcomePage,
    usedThankyouPage,
  } = props;

  return (
    <div
      className={
        (field.type === "CaptchaField" && usedCaptcha) ||
        (field.type === "GoogleAuthenticateField" && usedGoogleAuthenticate) ||
        (field.type === "Web3Field" && usedWeb3) ||
        (field.type === "WelcomePageField" && usedWelcomePage) ||
        (field.type === "ThankyouPageField" && usedThankyouPage)
          ? "fieldItem-container-disabled"
          : "fieldItem-container"
      }
      style={{
        cursor:
          (field.type === "CaptchaField" && usedCaptcha) ||
          (field.type === "GoogleAuthenticateField" &&
            usedGoogleAuthenticate) ||
          (field.type === "Web3Field" && usedWeb3) ||
          (field.type === "WelcomePageField" && usedWelcomePage) ||
          (field.type === "ThankyouPageField" && usedThankyouPage)
            ? "no-drop"
            : "crosshair",
      }}
      onClick={() =>
        (field.type === "CaptchaField" && usedCaptcha) ||
        (field.type === "GoogleAuthenticateField" && usedGoogleAuthenticate) ||
        (field.type === "Web3Field" && usedWeb3) ||
        (field.type === "WelcomePageField" && usedWelcomePage) ||
        (field.type === "ThankyouPageField" && usedThankyouPage)
          ? {}
          : addField(field)
      }
    >
      <div
        className="fieldItem-icon"
        style={{ backgroundColor: field.iconBgColor }}
      >
        {field.icon}
      </div>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          width: "100%",
        }}
      >
        <div
          className="fieldItem-title"
          style={{
            color:
              (field.type === "CaptchaField" && usedCaptcha) ||
              (field.type === "GoogleAuthenticateField" &&
                usedGoogleAuthenticate) ||
              (field.type === "Web3Field" && usedWeb3) ||
              (field.type === "WelcomePageField" && usedWelcomePage) ||
              (field.type === "ThankyouPageField" && usedThankyouPage)
                ? "#818181"
                : "",
          }}
        >
          {field.title}
        </div>

        {field.type === "CaptchaField" && usedCaptcha && (
          <div className="fieldItem-used-container">in use</div>
        )}
        {field.type === "GoogleAuthenticateField" && usedGoogleAuthenticate && (
          <div className="fieldItem-used-container">in use</div>
        )}
        {field.type === "Web3Field" && usedWeb3 && (
          <div className="fieldItem-used-container">in use</div>
        )}
        {field.type === "WelcomePageField" && usedWelcomePage && (
          <div className="fieldItem-used-container">in use</div>
        )}
        {field.type === "ThankyouPageField" && usedThankyouPage && (
          <div className="fieldItem-used-container">in use</div>
        )}
      </div>
    </div>
  );
};

export default FieldItem;
