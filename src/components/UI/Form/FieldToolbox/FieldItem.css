.fieldItem-container {
  display: flex;
  align-items: center;
  padding: 8px 5px;
  cursor: crosshair;
  transition: all 0.1s ease;
}

.fieldItem-container-disabled {
  display: flex;
  align-items: center;
  padding: 8px 5px;
  cursor: crosshair;
}

.fieldItem-icon {
  font-size: 22px;
  padding: 7px;
  border-radius: 3px;
  border: 1px solid #000;
  /*border-left: 5px solid #000;*/
  margin-right: 10px;
}

.fieldItem-title {
  font-size: 15px;
  font-family: "Exo 2", sans-serif;
}

.fieldItem-container:hover .fieldItem-icon {
  border: 1px solid var(--black);
  transition: 0.15s all;
  margin-left: 5px;
}

.fieldItem-container:hover .fieldItem-title {
  transition: 0.15s all;
  font-weight: 600;
}

.fieldItem-container:active {
  opacity: 0.7;
  transform: scale(1.1);
  margin-left: 15px;
}

.fieldItem-used-container {
  display: flex;
  justify-content: center;
  align-content: center;
  padding: 2px 4px;
  border: 1px solid var(--grey100);
  background-color: var(--grey400);
  color: #515151;
  border-radius: 30px;
  font-size: 11px;
}
