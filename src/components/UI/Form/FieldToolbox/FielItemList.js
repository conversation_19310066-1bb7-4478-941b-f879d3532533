import { FaRegStar } from "react-icons/fa6";
import { GoImage } from "react-icons/go";
import {
  IoCalendarNumberOutline,
  IoTimeOutline,
  IoVideocamOutline,
} from "react-icons/io5";
import {
  LuHeading1,
  LuHeading2,
  LuHeading3,
  LuPhone,
  LuSmile,
  LuText,
} from "react-icons/lu";
import {
  MdAlternateEmail,
  MdFirstPage,
  MdLastPage,
  MdOutlineCalendarViewDay,
  MdOutlineChecklist,
  MdOutlineCloudUpload,
  MdOutlineRadioButtonChecked,
  MdOutlineShortText,
  MdOutlineVerifiedUser,
} from "react-icons/md";
import { PiImageDuotone } from "react-icons/pi";
import { RiCheckboxMultipleLine, RiGoogleLine, RiText } from "react-icons/ri";
import { RxDividerHorizontal } from "react-icons/rx";

import { TbBrandGoogleAnalytics, TbNumber123 } from "react-icons/tb";

const FieldItemList = [
  {
    id: 1,
    headerTitle: "Text Blocks",
    icon: <LuHeading1 />,
    iconBgColor: "#BED7DC",
    type: "Heading1Field",
    title: "Heading 1",
    description: "Subheading text",
    textAlignment: "LEFT",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Heading Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Subheading Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      {
        propertyName: "textAlignment",
        propertyTitle: "Text Alignment",
        propertyType: "options",
        options: ["LEFT", "CENTER", "RIGHT"],
        propertyExplanation: "Select how the heading is aligned horizontally",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 2,
    headerTitle: "Text Blocks",
    icon: <LuHeading2 />,
    iconBgColor: "#BED7DC",
    type: "Heading2Field",
    title: "Heading 2",
    description: "Subheading text",
    textAlignment: "LEFT",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Heading Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Subheading Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      {
        propertyName: "textAlignment",
        propertyTitle: "Text Alignment",
        propertyType: "options",
        options: ["LEFT", "CENTER", "RIGHT"],
        propertyExplanation: "",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 3,
    headerTitle: "Text Blocks",
    icon: <LuHeading3 />,
    iconBgColor: "#BED7DC",
    type: "Heading3Field",
    title: "Heading 3",
    description: "Subheading text",
    textAlignment: "LEFT",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Heading Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Subheading Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      {
        propertyName: "textAlignment",
        propertyTitle: "Text Alignment",
        propertyType: "options",
        options: ["LEFT", "CENTER", "RIGHT"],
        propertyExplanation: "",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 4,
    headerTitle: "Text Blocks",
    icon: <RiText />,
    iconBgColor: "#BED7DC",
    type: "TextField",
    title: "Text Content",
    textSize: "DEFAULT",
    textAlignment: "LEFT",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Heading Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "textSize",
        propertyTitle: "Text Size",
        propertyType: "options",
        options: ["DEFAULT", "LARGE", "SMALL"],
        propertyExplanation: "",
      },
      {
        propertyName: "textAlignment",
        propertyTitle: "Text Alignment",
        propertyType: "options",
        options: ["LEFT", "CENTER", "RIGHT"],
        propertyExplanation: "",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },

  {
    id: 5,
    headerTitle: "Input Blocks",
    icon: <MdOutlineShortText />,
    iconBgColor: "#FFD1E3",
    type: "ShortInputField",
    title: "Short Input",
    placeholder: "Placeholder",
    description: "Description",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "placeholder",
        propertyTitle: "Placeholder Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      /*
      {
        propertyName: "textAlignment",
        propertyTitle: "Label Alignment",
        propertyType: "options",
        options: ["TOP", "LEFT", "RIGHT"],
        propertyExplanation: "Select how the label is aligned horizontally",
      },
      */
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 6,
    headerTitle: "Input Blocks",
    icon: <LuText />,
    iconBgColor: "#FFD1E3",
    type: "LongInputField",
    title: "Long Input",
    placeholder: "Placeholder",
    description: "Description",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "placeholder",
        propertyTitle: "Placeholder Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      /*
      {
        propertyName: "textAlignment",
        propertyTitle: "Label Alignment",
        propertyType: "options",
        options: ["TOP", "LEFT", "RIGHT"],
        propertyExplanation: "Select how the label is aligned horizontally",
      },
*/
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 7,
    headerTitle: "Input Blocks",
    icon: <MdOutlineCalendarViewDay />,
    iconBgColor: "#FFD1E3",
    type: "SelectMenuField",
    value: null,
    title: "Select Menu",
    placeholder: "Please select an option",
    description: "Description",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "placeholder",
        propertyTitle: "Placeholder Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },

  {
    id: 9,
    headerTitle: "Input Blocks",
    icon: <MdOutlineChecklist />,
    iconBgColor: "#FFD1E3",
    type: "MultiSelectMenuField",
    title: "Multi Select Menu",
    placeholder: "Please select an option / options",
    description: "Description",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "placeholder",
        propertyTitle: "Placeholder Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 8,
    headerTitle: "Input Blocks",
    icon: <MdOutlineRadioButtonChecked />,
    iconBgColor: "#FFD1E3",
    type: "SingleSelectField",
    title: "Single Choice",
    placeholder: "Please select an option",
    description: "Description",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "placeholder",
        propertyTitle: "Placeholder Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 10,
    headerTitle: "Input Blocks",
    icon: <RiCheckboxMultipleLine />,
    iconBgColor: "#FFD1E3",
    type: "CheckboxField",
    title: "Multi Choices",
    description: "Description",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },

      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 11,
    headerTitle: "Input Blocks",
    icon: <MdAlternateEmail />,
    iconBgColor: "#FFD1E3",
    type: "EmailField",
    title: "Email",
    placeholder: "Please enter your email address",
    description: "<EMAIL>",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "placeholder",
        propertyTitle: "Placeholder Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      /*
      {
        propertyName: "textAlignment",
        propertyTitle: "Label Alignment",
        propertyType: "options",
        options: ["TOP", "LEFT", "RIGHT"],
        propertyExplanation: "Select how the label is aligned horizontally",
      },
      */
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 12,
    headerTitle: "Input Blocks",
    icon: <TbNumber123 />,
    iconBgColor: "#FFD1E3",
    type: "NumberField",
    title: "Number",
    placeholder: "e.g, 13",
    description: "Description",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "placeholder",
        propertyTitle: "Placeholder Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      /*
      {
        propertyName: "textAlignment",
        propertyTitle: "Label Alignment",
        propertyType: "options",
        options: ["TOP", "LEFT", "RIGHT"],
        propertyExplanation: "Select how the label is aligned horizontally",
      },
      */
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 13,
    headerTitle: "Input Blocks",
    icon: <LuPhone />,
    iconBgColor: "#FFD1E3",
    type: "PhoneNumberField",
    title: "Phone Number",
    // placeholder: "(*************",
    description: "Please enter a valid phone number",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      /*
      {
        propertyName: "placeholder",
        propertyTitle: "Placeholder Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      */
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      /*
      {
        propertyName: "textAlignment",
        propertyTitle: "Label Alignment",
        propertyType: "options",
        options: ["TOP", "LEFT", "RIGHT"],
        propertyExplanation: "Select how the label is aligned horizontally",
      },
      */
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 14,
    headerTitle: "Input Blocks",
    icon: <IoTimeOutline />,
    iconBgColor: "#FFD1E3",
    type: "TimePickerField",
    title: "Time Picker",
    placeholder: "HH : MM : SS",
    description: "Hour Minutes Seconds",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      /*
      {
        propertyName: "placeholder",
        propertyTitle: "Placeholder Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      */
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      /*
      {
        propertyName: "textAlignment",
        propertyTitle: "Label Alignment",
        propertyType: "options",
        options: ["TOP", "LEFT", "RIGHT"],
        propertyExplanation: "Select how the label is aligned horizontally",
      },
      */
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 15,
    headerTitle: "Input Blocks",
    icon: <IoCalendarNumberOutline />,
    iconBgColor: "#FFD1E3",
    type: "DatePickerField",
    title: "Date Picker",
    placeholder: "YYYY-MM-DD",
    description: "Year Month Day",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      /*
      {
        propertyName: "placeholder",
        propertyTitle: "Placeholder Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      */
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      /*
      {
        propertyName: "textAlignment",
        propertyTitle: "Label Alignment",
        propertyType: "options",
        options: ["TOP", "LEFT", "RIGHT"],
        propertyExplanation: "Select how the label is aligned horizontally",
      },
      */
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 16,
    headerTitle: "Input Blocks",
    icon: <MdOutlineCloudUpload />,
    iconBgColor: "#FFD1E3",
    type: "FileUploadField",
    title: "File Upload",
    placeholder: "Browse Files",
    description: "Description",
    filesLimit: 1,
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      /*
      {
        propertyName: "placeholder",
        propertyTitle: "Placeholder Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      */
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      {
        propertyName: "filesLimit",
        propertyTitle: "Limit Number of Files",
        propertyType: "number",
        propertyExplanation:
          "Limit the number of files accepted for each submission",
      },
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 17,
    headerTitle: "Input Blocks",
    icon: <GoImage />,
    iconBgColor: "#FFD1E3",
    type: "ImageUploadField",
    title: "Image Upload",
    placeholder: "Browse Files",
    description: "Description",
    filesLimit: 1,
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      {
        propertyName: "filesLimit",
        propertyTitle: "Limit Number of Files",
        propertyType: "number",
        propertyExplanation:
          "Limit the number of files accepted for each submission",
      },
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 18,
    headerTitle: "Input Blocks",
    icon: <IoVideocamOutline />,
    iconBgColor: "#FFD1E3",
    type: "VideoUploadField",
    title: "Video Upload",
    placeholder: "Browse Files",
    description: "Description",
    filesLimit: 1,
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      {
        propertyName: "filesLimit",
        propertyTitle: "Limit Number of Files",
        propertyType: "number",
        propertyExplanation:
          "Limit the number of files accepted for each submission",
      },
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 19,
    headerTitle: "Rating & Ranking",
    icon: <FaRegStar />,
    iconBgColor: "#ffe2af",
    type: "StarRatingField",
    title: "Star Rating",
    placeholder: "",
    description: "Description",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      /*
      {
        propertyName: "textAlignment",
        propertyTitle: "Label Alignment",
        propertyType: "options",
        options: ["TOP", "LEFT", "RIGHT"],
        propertyExplanation: "Select how the label is aligned horizontally",
      },
      */
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 20,
    headerTitle: "Rating & Ranking",
    icon: <TbBrandGoogleAnalytics />,
    iconBgColor: "#ffe2af",
    type: "ScaleRatingField",
    title: "Scale Rating",
    placeholder: "",
    description: "Description",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 21,
    headerTitle: "Rating & Ranking",
    icon: <LuSmile />,
    iconBgColor: "#ffe2af",
    type: "SmileRatingField",
    title: "Smile Rating",
    placeholder: "",
    description: "Description",
    required: false,
    textAlignment: "TOP",
    hide: true,
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Field Label",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },
  {
    id: 22,
    headerTitle: "Page Elements",
    icon: <RxDividerHorizontal />,
    iconBgColor: "#bdead3",
    type: "DividerField",
    title: "Divider",
    required: false,
    textAlignment: "TOP",
    hide: false,
    lineColor: "#d6d6d6",
    dividerStyle: "Solid",
    dividerHeight: 1,
    spaceBelow: 5,
    spaceAbove: 5,
    visible: true,
    properties: [
      {
        propertyName: "lineColor",
        propertyTitle: "Line Color",
        propertyType: "colorPicker",
        propertyExplanation: "Select or enter a divider color",
      },
      {
        propertyName: "dividerStyle",
        propertyTitle: "Divider Style",
        propertyType: "options",
        options: ["Solid", "Dotted", "Dashed"],
        propertyExplanation: "Select a style for your divider",
      },
      {
        propertyName: "dividerHeight",
        propertyTitle: "Divider Height",
        propertyType: "number",
        propertyExplanation: "Enter the divider height",
      },
      {
        propertyName: "spaceBelow",
        propertyTitle: "Space Below",
        propertyType: "number",
        propertyExplanation: "Add space below the divider",
      },
      {
        propertyName: "spaceAbove",
        propertyTitle: "Space Above",
        propertyType: "number",
        propertyExplanation: "Add space above the divider",
      },
    ],
  },
  {
    id: 23,
    headerTitle: "Page Elements",
    icon: <PiImageDuotone />,
    iconBgColor: "#bdead3",
    type: "ImageField",
    title: "Image",
    required: false,
    textAlignment: "CENTER",
    hide: true,
    file: null,
    width: 120,
    height: 100,
    visible: true,
    properties: [
      {
        propertyName: "file",
        propertyTitle: "Image",
        propertyType: "upload",
        propertyExplanation: "",
      },
      {
        propertyName: "width",
        propertyTitle: "Width Size",
        propertyType: "number",
        propertyExplanation: "",
      },
      {
        propertyName: "height",
        propertyTitle: "Height Size",
        propertyType: "number",
        propertyExplanation: "",
      },
      {
        propertyName: "textAlignment",
        propertyTitle: "Image Alignment",
        propertyType: "options",
        options: ["LEFT", "CENTER", "RIGHT"],
        propertyExplanation: "Select how the image is aligned horizontally",
      },
    ],
  },
  {
    id: 24,
    headerTitle: "Page Elements",
    icon: <PiImageDuotone />,
    iconBgColor: "#bdead3",
    type: "ButtonField",
    title: "Button",
    buttonText1: "Submit",
    buttonText2: "Next",
    buttonText3: "Back",
    required: false,
    textAlignment: "CENTER",
    hide: true,
    visible: false,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Button Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
      /*
      {
        propertyName: "textAlignment",
        propertyTitle: "Label Alignment",
        propertyType: "options",
        options: ["TOP", "LEFT", "RIGHT"],
        propertyExplanation: "Select how the label is aligned horizontally",
      },
      */
      {
        propertyName: "required",
        propertyTitle: "Required",
        propertyType: "switch",
        propertyExplanation: "Prevent submission if this field is empty",
      },
      {
        propertyName: "hide",
        propertyTitle: "Hide Field",
        propertyType: "switch",
        propertyExplanation: "",
      },
    ],
  },

  {
    id: 25,
    headerTitle: "Verify",
    icon: <MdOutlineVerifiedUser />,
    iconBgColor: "#d1bdea",
    type: "CaptchaField",
    title: "Captcha",
    description: "Description",
    required: true,
    textAlignment: "CENTER",
    hide: true,
    visible: true,
    used: false,
    properties: [
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the heading",
      },
    ],
  },

  {
    id: 26,
    headerTitle: "Verify",
    icon: <RiGoogleLine />,
    iconBgColor: "#d1bdea",
    type: "GoogleAuthenticateField",
    title: "Google",
    visible: true,
  },

  /*
  {
    id: 27,
    headerTitle: "Verify",
    icon: <MdCurrencyBitcoin />,
    iconBgColor: "#d1bdea",
    type: "Web3Field",
    title: "Web3",
    visible: true,
  },
  */

  {
    id: 28,
    headerTitle: "Pages",
    icon: <MdFirstPage />,
    iconBgColor: "#dfdfdf",
    type: "WelcomePageField",
    title: "Welcome Page",
    titleText: "Hello! Welcome the form",
    buttonText: "Start",
    description: "Description (optional)",
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Hello Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the welcome text",
      },
      {
        propertyName: "buttonText",
        propertyTitle: "Button Text",
        propertyType: "input",
        propertyExplanation: "",
      },
    ],
  },

  {
    id: 29,
    headerTitle: "Pages",
    icon: <MdLastPage />,
    iconBgColor: "#dfdfdf",
    type: "ThankyouPageField",
    title: "Thank You Page",
    titleText: "Hello! Welcome the form",
    description: "Your submission has been received.",
    redirectUrl: "",
    visible: true,
    properties: [
      {
        propertyName: "title",
        propertyTitle: "Hello Text",
        propertyType: "input",
        propertyExplanation: "",
      },
      {
        propertyName: "description",
        propertyTitle: "Desription Text",
        propertyType: "input",
        propertyExplanation: "Add smaller text below the hello text",
      },
      {
        propertyName: "redirectUrl",
        propertyTitle: "Redirect Url",
        propertyType: "input",
        propertyExplanation: "Enter the URL you want to redirect to",
      },
    ],
  },
];

export default FieldItemList;
