import "./Leftside.css";
import { GoShareAndroid } from "react-icons/go";
import { MdOutlineEmail } from "react-icons/md";
import { CgWebsite } from "react-icons/cg";

const Leftside = ({
  selectedLeftsideMenuItem,
  setSelectedLeftsideMenuItem,
}) => {
  return (
    <div className="publish-leftside-container">
      <div
        onClick={() => setSelectedLeftsideMenuItem("shareLink")}
        className={
          selectedLeftsideMenuItem === "shareLink"
            ? "publish-leftside-item-selected"
            : "publish-leftside-item"
        }
      >
        <div className="publish-leftside-item-icon">
          <GoShareAndroid />
        </div>
        <div className="publish-leftside-item-title">Share the link</div>
      </div>

      <div
        onClick={() => setSelectedLeftsideMenuItem("shareEmail")}
        className={
          selectedLeftsideMenuItem === "shareEmail"
            ? "publish-leftside-item-selected"
            : "publish-leftside-item"
        }
      >
        <div className="publish-leftside-item-icon">
          <MdOutlineEmail />
        </div>
        <div className="publish-leftside-item-title">Share with email</div>
      </div>

      <div
        onClick={() => setSelectedLeftsideMenuItem("embedWebpage")}
        className={
          selectedLeftsideMenuItem === "embedWebpage"
            ? "publish-leftside-item-selected"
            : "publish-leftside-item"
        }
      >
        <div className="publish-leftside-item-icon">
          <CgWebsite />
        </div>
        <div className="publish-leftside-item-title">Embed in a web page</div>
      </div>
    </div>
  );
};

export default Leftside;
