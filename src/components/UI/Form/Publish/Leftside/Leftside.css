.publish-leftside-container {
  width: 260px;
  border: 1px solid var(--border);
  background-color: var(--grey400);
  position: relative;
  height: calc(100% - 25px);
  left: 20px;
  transition: 0.15s all;
  transform: scale(1);
  /*box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;*/
  border: 1px solid var(--grey200);
  font-family: "Exo 2", sans-serif;
  padding-top: 8px;
}

.publish-leftside-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  margin: 5px 20px;
  cursor: pointer;
}

.publish-leftside-item:hover {
  background-color: var(--grey200);
  border-radius: 5px;
}

.publish-leftside-item-selected {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  margin: 5px 20px;
  background-color: var(--black);
  border-radius: 5px;
  cursor: pointer;
  color: var(--white);
}

.publish-leftside-item-icon {
  font-size: 18px;
  margin-right: 10px;
}

.publish-leftside-item-title {
  font-size: 16px;
}
