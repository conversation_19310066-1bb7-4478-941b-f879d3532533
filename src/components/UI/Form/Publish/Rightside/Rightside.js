import EmbedWebpageRightside from "./EmbedWebpage/EmbedWebpageRightside";
import "./Rightside.css";
import ShareEmailRightside from "./ShareEmail/ShareEmailRightside";
import ShareLinkRightside from "./ShareLink/ShareLinkRightside";

const Rightside = ({
  formId,
  selectedLeftsideMenuItem,
  emailSenderName,
  setEmailSenderName,
  emailSenderEmail,
  setEmailSenderEmail,
  emailToList,
  setEmailToList,
  setLoading,
  triggerAction,
  setTriggerAction,
  embedMode,
  setEmbedMode,
  setDrawerProcess,
  drawerButtonText,
  setDrawerButtonText,
  drawerButtonBackground,
  setDrawerButtonBackground,
  drawerButtonTextColor,
  setDrawerButtonTextColor,
  setDrawerPosition,
  isInPageProcess,
  setInPageProcess,
  inPageWidth,
  setInPageWidth,
  inPageWidthType,
  setInPageWidthType,
  inPageHeight,
  setInPageHeight,
  inPageHeightType,
  setInPageHeightType,
  isIFrameProcess,
  setIFrameProcess,
  iFrameWidth,
  setIFrameWith,
  iFrameWidthType,
  setIFrameWithType,
  iFrameHeight,
  setIFrameHeight,
  iFrameHeightType,
  setIFrameHeightType,
  isPopupProcess,
  setPopupProcess,
  popupButtonText,
  setPopupButtonText,
  popupButtonBackground,
  setPopupButtonBackground,
  popupButtonTextColor,
  setPopupButtonTextColor,
  isPopoverProcess,
  setPopoverProcess,
  popoverButtonBackground,
  setPopoverButtonBackground,
  popoverIconColor,
  setPopoverIconColor,
}) => {
  return (
    <>
      {selectedLeftsideMenuItem === "shareLink" && (
        <ShareLinkRightside formId={formId} />
      )}
      {selectedLeftsideMenuItem === "shareEmail" && (
        <ShareEmailRightside
          emailSenderName={emailSenderName}
          setEmailSenderName={setEmailSenderName}
          emailSenderEmail={emailSenderEmail}
          setEmailSenderEmail={setEmailSenderEmail}
          emailToList={emailToList}
          setEmailToList={setEmailToList}
          setLoading={setLoading}
          triggerAction={triggerAction}
          setTriggerAction={setTriggerAction}
        />
      )}
      {selectedLeftsideMenuItem === "embedWebpage" && (
        <EmbedWebpageRightside
          embedMode={embedMode}
          setEmbedMode={setEmbedMode}
          setDrawerProcess={setDrawerProcess}
          drawerButtonText={drawerButtonText}
          setDrawerButtonText={setDrawerButtonText}
          drawerButtonBackground={drawerButtonBackground}
          setDrawerButtonBackground={setDrawerButtonBackground}
          drawerButtonTextColor={drawerButtonTextColor}
          setDrawerButtonTextColor={setDrawerButtonTextColor}
          setDrawerPosition={setDrawerPosition}
          isInPageProcess={isInPageProcess}
          setInPageProcess={setInPageProcess}
          inPageWidth={inPageWidth}
          setInPageWidth={setInPageWidth}
          inPageWidthType={inPageWidthType}
          setInPageWidthType={setInPageWidthType}
          inPageHeight={inPageHeight}
          setInPageHeight={setInPageHeight}
          inPageHeightType={inPageHeightType}
          setInPageHeightType={setInPageHeightType}
          isIFrameProcess={isIFrameProcess}
          setIFrameProcess={setIFrameProcess}
          iFrameWidth={iFrameWidth}
          setIFrameWith={setIFrameWith}
          iFrameWidthType={iFrameWidthType}
          setIFrameWithType={setIFrameWithType}
          iFrameHeight={iFrameHeight}
          setIFrameHeight={setIFrameHeight}
          iFrameHeightType={iFrameHeightType}
          setIFrameHeightType={setIFrameHeightType}
          isPopupProcess={isPopupProcess}
          setPopupProcess={setPopupProcess}
          popupButtonText={popupButtonText}
          setPopupButtonText={setPopupButtonText}
          popupButtonBackground={popupButtonBackground}
          setPopupButtonBackground={setPopupButtonBackground}
          popupButtonTextColor={popupButtonTextColor}
          setPopupButtonTextColor={setPopupButtonTextColor}
          isPopoverProcess={isPopoverProcess}
          setPopoverProcess={setPopoverProcess}
          popoverButtonBackground={popoverButtonBackground}
          setPopoverButtonBackground={setPopoverButtonBackground}
          popoverIconColor={popoverIconColor}
          setPopoverIconColor={setPopoverIconColor}
        />
      )}
    </>
  );
};

export default Rightside;
