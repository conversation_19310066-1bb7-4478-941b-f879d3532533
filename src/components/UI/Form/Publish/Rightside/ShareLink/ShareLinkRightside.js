import {
  FaFacebook,
  FaLinkedin,
  FaTelegramPlane,
  FaW<PERSON>sapp,
} from "react-icons/fa";
import "./ShareLinkRightside.css";
import { Popover } from "antd";
import { FaXTwitter } from "react-icons/fa6";
import logo from "../../../../../../assets/images/logo/logo-white-title-50.png";
import {
  FacebookShareButton,
  TwitterShareButton,
  LinkedinShareButton,
  WhatsappShareButton,
  TelegramShareButton,
} from "react-share";
import { useState } from "react";
import React from "react";

const ShareLinkRightside = ({ formId }) => {
  const [url, setUrl] = useState("https://form.formiqo.com/" + formId);
  const [title, setTitle] = useState("FORMIQO.COM");
  const [description, setDescription] = useState(
    "Build form share every where !"
  );

  const [shareTitle, setShareTitle] = useState("My branded formiqo");
  const [shareDescription, setShareDescription] = useState(
    "Turn data collection into an experience with formiqo. Create beautiful online forms, surveys, quizzes, and so much more."
  );

  return (
    <div className="publishPage-shareLinkRightSide-container">
      <div className="publishPage-shareLinkRightSide-social-container">
        <div className="publishPage-shareLinkRightSide-social-header">
          Share Options
        </div>
        <div className="publishPage-shareLinkRightSide-social-title">
          Share in social platforms
        </div>

        <div className="publishPage-shareLinkRightSide-social-items">
          <FacebookShareButton
            url={"https://www.formiqo.com"}
            title={title}
            description={description}
          >
            <Popover
              placement="bottom"
              title="Share in Facebook"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div className="publishPage-shareLinkRightSide-social-item">
                <FaFacebook />
              </div>
            </Popover>
          </FacebookShareButton>

          <TwitterShareButton url={url} title={title} description={description}>
            <Popover
              placement="bottom"
              title="Share in X"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div className="publishPage-shareLinkRightSide-social-item">
                <FaXTwitter />
              </div>
            </Popover>
          </TwitterShareButton>
          <LinkedinShareButton url={url} source={title} summary={description}>
            <Popover
              placement="bottom"
              title="Share in LinkedIn"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div className="publishPage-shareLinkRightSide-social-item">
                <FaLinkedin />
              </div>
            </Popover>
          </LinkedinShareButton>

          <WhatsappShareButton
            url={url}
            title={title}
            description={description}
          >
            <Popover
              placement="bottom"
              title="Share in Whatsapp"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div className="publishPage-shareLinkRightSide-social-item">
                <FaWhatsapp />
              </div>
            </Popover>
          </WhatsappShareButton>

          <TelegramShareButton
            url={url}
            title={title}
            description={description}
          >
            <Popover
              placement="bottom"
              title="Share in Telegram"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div className="publishPage-shareLinkRightSide-social-item">
                <FaTelegramPlane />
              </div>
            </Popover>
          </TelegramShareButton>
        </div>

        <div className="publishPage-shareLinkRightSide-social-settings">
          <div className="publishPage-shareLinkRightSide-social-setting-item">
            <div className="publishPage-shareLinkRightSide-social-setting-item-title">
              Social share preview
            </div>

            <div className="publishPage-shareLinkRightSide-social-setting-preview">
              <img src={logo} alt="logo" />
            </div>
          </div>

          <div className="publishPage-shareLinkRightSide-social-setting-item">
            <div className="publishPage-shareLinkRightSide-social-setting-item">
              <div className="publishPage-shareLinkRightSide-social-setting-item-title">
                Link title
              </div>
              <input
                disabled
                defaultValue="FORM.FORMIQO.COM"
                className="publishPage-shareLinkRightSide-social-setting-item-input"
              />
            </div>
          </div>

          <div className="publishPage-shareLinkRightSide-divider" />

          <div className="publishPage-shareLinkRightSide-social-setting-item">
            <div className="publishPage-shareLinkRightSide-social-setting-item-title">
              Social share title
            </div>

            <input
              disabled
              defaultValue={shareTitle}
              className="publishPage-shareLinkRightSide-social-setting-item-input"
            />
          </div>

          <div className="publishPage-shareLinkRightSide-social-setting-item">
            <div className="publishPage-shareLinkRightSide-social-setting-item-title">
              Social share description
            </div>

            <textarea
              disabled
              defaultValue={shareDescription}
              style={{ height: "120px" }}
              name="sociaShareDescription"
              className="publishPage-shareLinkRightSide-social-setting-item-input"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShareLinkRightside;
