.publishPage-shareLinkRightSide-container {
  width: 260px;
  border: 1px solid var(--border);
  background-color: var(--grey400);
  position: relative;
  height: calc(100% - 25px);
  right: 20px;
  transition: 0.15s all;
  transform: scale(1);
  /*box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;*/
  border: 1px solid var(--grey200);
  font-family: "Exo 2", sans-serif;
  padding-top: 8px;
  overflow-y: auto;
}

.publishPage-shareLinkRightSide-social-header {
  font-size: 16px;
  font-weight: 500;
  color: var(--black);
  border-bottom: 1px solid var(--grey100);
  padding: 0 0 10px 0;
  margin-bottom: 10px;
  margin-left: -10px;
  margin-right: -10px;
  padding-top: 10px;
  padding-left: 8px;
  margin-top: -16px;
}

.publishPage-shareLinkRightSide-social-title {
  font-size: 15px;
  font-family: "Exo 2", sans-serif;
  margin-bottom: 10px;
  font-weight: 500;
  color: var(--black);
}

.publishPage-shareLinkRightSide-social-container {
  padding: 8px 10px;
}

.publishPage-shareLinkRightSide-social-items {
  display: flex;
  justify-content: space-around;
  border-bottom: 1px solid var(--grey100);
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.publishPage-shareLinkRightSide-social-item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  background-color: var(--white);
  border: 1px solid var(--grey100);
  color: var(--black);
  width: 36px;
  height: 36px;
  border-radius: 5px;
  cursor: pointer;
}

.publishPage-shareLinkRightSide-social-item:hover {
  background-color: var(--black);
  color: var(--white);
  transition: 0.5s all;
}

.publishPage-shareLinkRightSide-social-settings {
}

.publishPage-shareLinkRightSide-social-setting-item {
  margin-bottom: 20px;
}

.publishPage-shareLinkRightSide-social-setting-item-title {
  font-size: 15px;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  color: var(--black);
  margin-bottom: 10px;
}

.publishPage-shareLinkRightSide-social-setting-item-input {
  width: 100%;
  height: 35px;
  border-radius: 5px;
  border: 1px solid var(--grey100);
  background-color: var(--grey200);
  padding: 10px;
  font-size: 13px;
  color: var(--black);
}

.publishPage-shareLinkRightSide-social-setting-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100px;
  background-color: white;
  border-radius: 5px;
}

.publishPage-shareLinkRightSide-divider {
  border-bottom: 1px solid var(--grey100);
  margin-bottom: 20px;
}

.publishPage-shareLinkRightSide-social-setting-item-uploadButton {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  background-color: var(--black);
  color: white;
  border-radius: 5px;
  cursor: pointer;
}

.publishPage-shareLinkRightSide-social-setting-item-uploadButton:hover {
  opacity: 0.8;
}
