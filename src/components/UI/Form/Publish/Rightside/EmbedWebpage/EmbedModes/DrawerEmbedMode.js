import { ColorPicker } from "antd";
import "./DrawerEmbedMode.css";
import { VscLayoutSidebarLeft, VscLayoutSidebarRight } from "react-icons/vsc";
import { useState } from "react";

const DrawerEmbedMode = ({
  setDrawerProcess,
  drawerButtonText,
  setDrawerButtonText,
  drawerButtonBackground,
  setDrawerButtonBackground,
  drawerButtonTextColor,
  setDrawerButtonTextColor,
  setDrawerPosition,
}) => {
  const [position, setPosition] = useState("left");

  const onChangeButtonText = (e) => {
    const value = e.target.value;
    if (value.length <= 20) {
      setDrawerButtonText(value);
      setDrawerProcess(true);
    }
  };

  const onChangeButtonBackground = (e) => {
    setDrawerButtonBackground(e);
    setDrawerProcess(true);
  };

  const onChangeButtonTextColor = (e) => {
    setDrawerButtonTextColor(e);
    setDrawerProcess(true);
  };

  const onChangePosition = (position) => {
    setPosition(position);
    setDrawerPosition(position);
    setDrawerProcess(true);
  };

  return (
    <div className="drawerEmbedMode-embed-options">
      <div className="drawerEmbedMode-embed-options-title">Settings</div>
      <div className="drawerEmbedMode-embed-option-container">
        <div className="drawerEmbedMode-embed-option-title">Button Text</div>

        <input
          className="drawerEmbedMode-embed-option-input"
          value={drawerButtonText}
          onChange={onChangeButtonText}
        />
        <div
          style={{
            fontSize: "12px",
            textAlign: "end",
            color: "var(--black)",
            opacity: ".6",
            marginTop: "5px",
          }}
        >
          {drawerButtonText.length}/20
        </div>
      </div>

      <div
        className="drawerEmbedMode-embed-option-container"
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            width: "100%",
            alignItems: "center",
            marginTop: "20px",
          }}
        >
          <div className="drawerEmbedMode-embed-option-title">Button Color</div>
          <ColorPicker
            value={drawerButtonBackground}
            onChange={(c) => {
              onChangeButtonBackground(c.toHexString());
            }}
          />
        </div>

        <div
          style={{
            display: "flex",
            width: "100%",
            justifyContent: "space-between",
            alignItems: "center",
            marginTop: "30px",
          }}
        >
          <div className="drawerEmbedMode-embed-option-title">Text Color</div>
          <ColorPicker
            value={drawerButtonTextColor}
            onChange={(c) => {
              onChangeButtonTextColor(c.toHexString());
            }}
          />
        </div>

        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            width: "100%",
            marginTop: "35px",
          }}
        >
          <div className="drawerEmbedMode-embed-option-title">Position</div>

          <div style={{ display: "flex" }}>
            <VscLayoutSidebarLeft
              onClick={() => onChangePosition("left")}
              className={
                position === "left"
                  ? "drawerEmbedMode-embed-option-position-icon-selected"
                  : "drawerEmbedMode-embed-option-position-icon"
              }
              style={{ marginRight: "20px" }}
            />
            <VscLayoutSidebarRight
              onClick={() => onChangePosition("right")}
              className={
                position === "right"
                  ? "drawerEmbedMode-embed-option-position-icon-selected"
                  : "drawerEmbedMode-embed-option-position-icon"
              }
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DrawerEmbedMode;
