import "./IFrameEmbedMode.css";
import { InputNumber, Select } from "antd";

const IFrameEmbedMode = ({
  isIFrameProcess,
  setIFrameProcess,
  iFrameWidth,
  setIFrameWith,
  iFrameWidthType,
  setIFrameWithType,
  iFrameHeight,
  setIFrameHeight,
  iFrameHeightType,
  setIFrameHeightType,
}) => {
  const onChageWidth = (e) => {
    setIFrameWith(e);
    setIFrameProcess(true);
  };

  const onChangeWidthType = (e) => {
    setIFrameWithType(e);
    setIFrameProcess(true);
  };

  const onChangeHeight = (e) => {
    setIFrameHeight(e);
    setIFrameProcess(true);
  };

  const onChangeHeightType = (e) => {
    setIFrameHeightType(e);
    setIFrameProcess(true);
  };

  return (
    <div className="iFrameEmbedMode-embed-options">
      <div className="iFrameEmbedMode-embed-options-title">Settings</div>
      <div className="iFrameEmbedMode-embed-option-container">
        <div className="iFrameEmbedMode-embed-option-title">Width</div>
        <div style={{ display: "flex", justifyContent: "flex-start" }}>
          <InputNumber
            value={iFrameWidth}
            onChange={onChageWidth}
            className="iFrameEmbedMode-embed-option-input"
            style={{ marginRight: "10px" }}
          />
          <div
            style={{
              fontSize: "12px",
              textAlign: "end",
              color: "var(--black)",
              opacity: ".6",
              marginTop: "5px",
            }}
          ></div>
          <div className="iFrameEmbedMode-embed-option-input-select">
            <Select defaultValue={iFrameWidthType} onChange={onChangeWidthType}>
              <Select.Option value="percent">%</Select.Option>
              <Select.Option value="pixel">px</Select.Option>
            </Select>
          </div>
        </div>
      </div>

      <div className="iFrameEmbedMode-embed-option-container">
        <div className="iFrameEmbedMode-embed-option-title">Height</div>
        <div style={{ display: "flex", justifyContent: "flex-start" }}>
          <InputNumber
            value={iFrameHeight}
            onChange={onChangeHeight}
            className="iFrameEmbedMode-embed-option-input"
            style={{ marginRight: "10px" }}
          />
          <div
            style={{
              fontSize: "12px",
              textAlign: "end",
              color: "var(--black)",
              opacity: ".6",
              marginTop: "5px",
            }}
          ></div>
          <div className="iFrameEmbedMode-embed-option-input-select">
            <Select
              defaultValue={iFrameHeightType}
              onChange={onChangeHeightType}
            >
              <Select.Option value="percent">%</Select.Option>
              <Select.Option value="pixel">px</Select.Option>
            </Select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IFrameEmbedMode;
