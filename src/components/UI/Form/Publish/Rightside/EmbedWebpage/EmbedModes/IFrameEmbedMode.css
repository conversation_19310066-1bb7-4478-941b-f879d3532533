.iFrameEmbedMode-embed-options {
    font-family: "Exo 2", sans-serif;
}

.iFrameEmbedMode-embed-options-title {

    font-weight: 500;
    font-size: 15px;
    color: var(--black);
}

.iFrameEmbedMode-embed-option-container {
    margin-top: 20px;
}

.iFrameEmbedMode-embed-option-title {
    color: var(--black);
    opacity: .9;
    font-size: 14px;
    margin-bottom: 5px;
}

.iFrameEmbedMode-embed-option-input {
    width: 100px !important;
    height: 35px;
    border-radius: 5px;
    border: 1px solid var(--grey100);
    background-color: var(--white);
    padding: 3px;
    font-size: 13px;
    color: var(--black);
}

.iFrameEmbedMode-embed-option-input-select {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 60px;
    border-radius: 5px;
    border: 1px solid var(--grey100);
    background-color: var(--white);
    padding: 3px;
    font-size: 13px;
    color: var(--black);
}

.iFrameEmbedMode-embed-option-position-icon {
    font-size: 30px;
    border: 1px solid var(--grey100);
    border-radius: 4px;
    color: var(--black);
    padding: 5px;
    cursor: pointer;
}

.iFrameEmbedMode-embed-option-position-icon-selected {
    font-size: 30px;
    border: 1px solid var(--black);
    background-color: var(--black);
    border-radius: 4px;
    color: var(--white);
    padding: 5px;
    cursor: pointer;
}

.ant-input-number-outlined:focus-within {
    box-shadow: 0 0 0 0 rgba(5, 145, 255, 0);
}

::placeholder {
    color: var(--grey100) !important;
    font-weight: 300 !important;
    font-size: 13px !important;
    font-family: 'Inter', sans-serif;
}

::-ms-input-placeholder {
    color: var(--grey100) !important;
    font-weight: 300 !important;
    font-size: 13px !important;
    font-family: 'Inter', sans-serif;
}

::-ms-input-placeholder {
    color: var(--grey100) !important;
    font-weight: 300 !important;
    font-size: 13px !important;
    font-family: 'Inter', sans-serif;
}