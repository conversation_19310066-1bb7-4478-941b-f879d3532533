import { ColorPicker } from "antd";
import "./PopupEmbedMode.css";
import { useState } from "react";

const PopupEmbedMode = ({
  setPopupProcess,
  popupButtonText,
  setPopupButtonText,
  popupButtonBackground,
  setPopupButtonBackground,
  popupButtonTextColor,
  setPopupButtonTextColor,
}) => {
  const [position, setPosition] = useState("left");

  const onChangeButtonText = (e) => {
    const value = e.target.value;
    if (value.length <= 20) {
      setPopupButtonText(value);
      setPopupProcess(true);
    }
  };

  const onChangeButtonBackground = (e) => {
    setPopupButtonBackground(e);
    setPopupProcess(true);
  };

  const onChangeButtonTextColor = (e) => {
    setPopupButtonTextColor(e);
    setPopupProcess(true);
  };

  return (
    <div className="popupEmbedMode-embed-options">
      <div className="popupEmbedMode-embed-options-title">Settings</div>
      <div className="popupEmbedMode-embed-option-container">
        <div className="popupEmbedMode-embed-option-title">Button Text</div>

        <input
          className="popupEmbedMode-embed-option-input"
          value={popupButtonText}
          onChange={onChangeButtonText}
        />
        <div
          style={{
            fontSize: "12px",
            textAlign: "end",
            color: "var(--black)",
            opacity: ".6",
            marginTop: "5px",
          }}
        >
          {popupButtonText.length}/20
        </div>
      </div>

      <div
        className="popupEmbedMode-embed-option-container"
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            width: "100%",
            alignItems: "center",
            marginTop: "20px",
          }}
        >
          <div className="popupEmbedMode-embed-option-title">Button Color</div>
          <ColorPicker
            value={popupButtonBackground}
            onChange={(c) => {
              onChangeButtonBackground(c.toHexString());
            }}
          />
        </div>

        <div
          style={{
            display: "flex",
            width: "100%",
            justifyContent: "space-between",
            alignItems: "center",
            marginTop: "30px",
          }}
        >
          <div className="popupEmbedMode-embed-option-title">Text Color</div>
          <ColorPicker
            value={popupButtonTextColor}
            onChange={(c) => {
              onChangeButtonTextColor(c.toHexString());
            }}
          />
        </div>

        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            width: "100%",
            marginTop: "35px",
          }}
        ></div>
      </div>
    </div>
  );
};

export default PopupEmbedMode;
