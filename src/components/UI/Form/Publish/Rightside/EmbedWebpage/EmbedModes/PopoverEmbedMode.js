import { ColorPicker } from "antd";
import "./PopoverEmbedMode.css";

const PopoverEmbedMode = ({
  setPopoverProcess,
  popoverButtonBackground,
  setPopoverButtonBackground,
  popoverIconColor,
  setPopoverIconColor,
}) => {
  const onChangeButtonBackground = (e) => {
    setPopoverButtonBackground(e);
    setPopoverProcess(true);
  };

  const onChangeIconColor = (e) => {
    setPopoverIconColor(e);
    setPopoverProcess(true);
  };

  return (
    <div className="popoverEmbedMode-embed-options">
      <div className="popoverEmbedMode-embed-options-title">Settings</div>
      <div className="popoverEmbedMode-embed-option-container">
        <div
          className="popoverEmbedMode-embed-option-container"
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
              alignItems: "center",
              marginTop: "20px",
            }}
          >
            <div className="popoverEmbedMode-embed-option-title">
              Button Color
            </div>
            <ColorPicker
              value={popoverButtonBackground}
              onChange={(c) => {
                onChangeButtonBackground(c.toHexString());
              }}
            />
          </div>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
              alignItems: "center",
              marginTop: "20px",
            }}
          >
            <div className="popoverEmbedMode-embed-option-title">
              Icon Color
            </div>
            <ColorPicker
              value={popoverIconColor}
              onChange={(c) => {
                onChangeIconColor(c.toHexString());
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PopoverEmbedMode;
