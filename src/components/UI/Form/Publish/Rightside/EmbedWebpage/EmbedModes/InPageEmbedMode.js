import "./InPageEmbedMode.css";
import { InputNumber, Select } from "antd";

const InPageEmbedMode = ({
  isInPageProcess,
  setInPageProcess,
  inPageWidth,
  setInPageWidth,
  inPageWidthType,
  setInPageWidthType,
  inPageHeight,
  setInPageHeight,
  inPageHeightType,
  setInPageHeightType,
}) => {
  const onChageWidth = (e) => {
    setInPageWidth(e);
    setInPageProcess(true);
  };

  const onChangeWidthType = (e) => {
    setInPageWidthType(e);
    setInPageProcess(true);
  };

  const onChangeHeight = (e) => {
    setInPageHeight(e);
    setInPageProcess(true);
  };

  const onChangeHeightType = (e) => {
    setInPageHeightType(e);
    setInPageProcess(true);
  };

  return (
    <div className="inPageEmbedMode-embed-options">
      <div className="inPageEmbedMode-embed-options-title">Settings</div>
      <div className="inPageEmbedMode-embed-option-container">
        <div className="inPageEmbedMode-embed-option-title">Width</div>
        <div style={{ display: "flex", justifyContent: "flex-start" }}>
          <InputNumber
            value={inPageWidth}
            onChange={onChageWidth}
            className="inPageEmbedMode-embed-option-input"
            style={{ marginRight: "10px" }}
          />
          <div
            style={{
              fontSize: "12px",
              textAlign: "end",
              color: "var(--black)",
              opacity: ".6",
              marginTop: "5px",
            }}
          ></div>
          <div className="inPageEmbedMode-embed-option-input-select">
            <Select defaultValue={inPageWidthType} onChange={onChangeWidthType}>
              <Select.Option value="percent">%</Select.Option>
              <Select.Option value="pixel">px</Select.Option>
            </Select>
          </div>
        </div>
      </div>

      <div className="inPageEmbedMode-embed-option-container">
        <div className="inPageEmbedMode-embed-option-title">Height</div>
        <div style={{ display: "flex", justifyContent: "flex-start" }}>
          <InputNumber
            value={inPageHeight}
            onChange={onChangeHeight}
            className="inPageEmbedMode-embed-option-input"
            style={{ marginRight: "10px" }}
          />
          <div
            style={{
              fontSize: "12px",
              textAlign: "end",
              color: "var(--black)",
              opacity: ".6",
              marginTop: "5px",
            }}
          ></div>
          <div className="inPageEmbedMode-embed-option-input-select">
            <Select
              defaultValue={inPageHeightType}
              onChange={onChangeHeightType}
            >
              <Select.Option value="percent">%</Select.Option>
              <Select.Option value="pixel">px</Select.Option>
            </Select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InPageEmbedMode;
