import "./EmbedWebpageRightside.css";
import { Select } from "antd";
import React, { useEffect } from "react";
import { MdOutlineCenterFocusStrong } from "react-icons/md";
import { PiFrameCornersLight } from "react-icons/pi";
import { GoScreenFull } from "react-icons/go";
import { TbBoxAlignBottomRight, TbBoxAlignLeft } from "react-icons/tb";
import DrawerEmbedMode from "./EmbedModes/DrawerEmbedMode";
import { AiOutlinePicLeft } from "react-icons/ai";
import InPageEmbedMode from "./EmbedModes/InPageEmbedMode";
import IFrameEmbedMode from "./EmbedModes/IFrameEmbedMode";
import PopupEmbedMode from "./EmbedModes/PopupEmbedMode";
import PopoverEmbedMode from "./EmbedModes/PopoverEmbedMode";

const EmbedWebpageRightside = ({
  embedMode,
  setEmbedMode,
  setDrawerProcess,
  drawerButtonText,
  setDrawerButtonText,
  drawerButtonBackground,
  setDrawerButtonBackground,
  drawerButtonTextColor,
  setDrawerButtonTextColor,
  setDrawerPosition,
  isInPageProcess,
  setInPageProcess,
  inPageWidth,
  setInPageWidth,
  inPageWidthType,
  setInPageWidthType,
  inPageHeight,
  setInPageHeight,
  inPageHeightType,
  setInPageHeightType,
  isIFrameProcess,
  setIFrameProcess,
  iFrameWidth,
  setIFrameWith,
  iFrameWidthType,
  setIFrameWithType,
  iFrameHeight,
  setIFrameHeight,
  iFrameHeightType,
  setIFrameHeightType,
  isPopupProcess,
  setPopupProcess,
  popupButtonText,
  setPopupButtonText,
  popupButtonBackground,
  setPopupButtonBackground,
  popupButtonTextColor,
  setPopupButtonTextColor,
  isPopoverProcess,
  setPopoverProcess,
  popoverButtonBackground,
  setPopoverButtonBackground,
  popoverIconColor,
  setPopoverIconColor,
}) => {
  useEffect(() => {
    if (!embedMode) {
      setEmbedMode("inPage");
    }
  }, [embedMode]);
  const onChangeEmbedMode = (e) => {
    setEmbedMode(e);
  };

  return (
    <div className="publishPage-embedWebpageRightside-container">
      <div className="publishPage-embedWebpageRightside-header-container">
        <div className="publishPage-embedWebpageRightside-header">
          Embed Options
        </div>

        <div className="publishPage-embedWebpageRightside-embed-settings">
          <div className="publishPage-embedWebpageRightside-embed-setting-item">
            <div
              className="publishPage-embedWebpageRightside-embed-setting-item-title"
              style={{ marginTop: "15px", marginBottom: "8px" }}
            >
              Modes
            </div>
            <div className="publishPage-embedWebpageRightside-embed-setting-options-container">
              <div
                style={{
                  height: "45px",
                  backgroundColor: "#fff",
                  display: "flex",
                  alignItems: "center",
                  borderRadius: "5px",
                }}
              >
                <Select
                  style={{ width: "100%", height: "50px !important" }}
                  onChange={onChangeEmbedMode}
                  placeholder="Please select embed mode"
                  defaultValue="inPage"
                >
                  <Select.Option value="inPage">
                    <div className="publishPage-embedWebpageRightside-embed-setting-option-item">
                      <div className="publishPage-embedWebpageRightside-embed-setting-option-item-icon">
                        <AiOutlinePicLeft />
                      </div>
                      <div className="publishPage-embedWebpageRightside-embed-setting-option-item-title">
                        In Page
                      </div>
                    </div>
                  </Select.Option>
                  <Select.Option value="fullPage">
                    <div className="publishPage-embedWebpageRightside-embed-setting-option-item">
                      <div className="publishPage-embedWebpageRightside-embed-setting-option-item-icon">
                        <GoScreenFull />
                      </div>
                      <div className="publishPage-embedWebpageRightside-embed-setting-option-item-title">
                        Full Page
                      </div>
                    </div>
                  </Select.Option>
                  <Select.Option value="iFrame">
                    <div className="publishPage-embedWebpageRightside-embed-setting-option-item">
                      <div className="publishPage-embedWebpageRightside-embed-setting-option-item-icon">
                        <PiFrameCornersLight />
                      </div>
                      <div className="publishPage-embedWebpageRightside-embed-setting-option-item-title">
                        IFrame
                      </div>
                    </div>
                  </Select.Option>
                  <Select.Option value="drawer">
                    <div className="publishPage-embedWebpageRightside-embed-setting-option-item">
                      <div className="publishPage-embedWebpageRightside-embed-setting-option-item-icon">
                        <TbBoxAlignLeft />
                      </div>
                      <div className="publishPage-embedWebpageRightside-embed-setting-option-item-title">
                        Drawer
                      </div>
                    </div>
                  </Select.Option>
                  <Select.Option value="popup">
                    <div className="publishPage-embedWebpageRightside-embed-setting-option-item">
                      <div className="publishPage-embedWebpageRightside-embed-setting-option-item-icon">
                        <MdOutlineCenterFocusStrong />
                      </div>
                      <div className="publishPage-embedWebpageRightside-embed-setting-option-item-title">
                        Popup
                      </div>
                    </div>
                  </Select.Option>
                  <Select.Option value="popover">
                    <div className="publishPage-embedWebpageRightside-embed-setting-option-item">
                      <div className="publishPage-embedWebpageRightside-embed-setting-option-item-icon">
                        <TbBoxAlignBottomRight />
                      </div>
                      <div className="publishPage-embedWebpageRightside-embed-setting-option-item-title">
                        Popover
                      </div>
                    </div>
                  </Select.Option>
                </Select>
              </div>

              <div className="publishPage-embedWebpageRightside-divider" />
              {embedMode === "inPage" && (
                <InPageEmbedMode
                  isInPageProcess={isInPageProcess}
                  setInPageProcess={setInPageProcess}
                  inPageWidth={inPageWidth}
                  setInPageWidth={setInPageWidth}
                  inPageWidthType={inPageWidthType}
                  setInPageWidthType={setInPageWidthType}
                  inPageHeight={inPageHeight}
                  setInPageHeight={setInPageHeight}
                  inPageHeightType={inPageHeightType}
                  setInPageHeightType={setInPageHeightType}
                />
              )}
              {embedMode === "fullPage" && <></>}
              {embedMode === "iFrame" && (
                <IFrameEmbedMode
                  isIFrameProcess={isIFrameProcess}
                  setIFrameProcess={setIFrameProcess}
                  iFrameWidth={iFrameWidth}
                  setIFrameWith={setIFrameWith}
                  iFrameWidthType={iFrameWidthType}
                  setIFrameWithType={setIFrameWithType}
                  iFrameHeight={iFrameHeight}
                  setIFrameHeight={setIFrameHeight}
                  iFrameHeightType={iFrameHeightType}
                  setIFrameHeightType={setIFrameHeightType}
                />
              )}
              {embedMode === "drawer" && (
                <DrawerEmbedMode
                  setDrawerProcess={setDrawerProcess}
                  drawerButtonText={drawerButtonText}
                  setDrawerButtonText={setDrawerButtonText}
                  drawerButtonBackground={drawerButtonBackground}
                  setDrawerButtonBackground={setDrawerButtonBackground}
                  drawerButtonTextColor={drawerButtonTextColor}
                  setDrawerButtonTextColor={setDrawerButtonTextColor}
                  setDrawerPosition={setDrawerPosition}
                />
              )}
              {embedMode === "popup" && (
                <PopupEmbedMode
                  isPopupProcess={isPopupProcess}
                  setPopupProcess={setPopupProcess}
                  popupButtonText={popupButtonText}
                  setPopupButtonText={setPopupButtonText}
                  popupButtonBackground={popupButtonBackground}
                  setPopupButtonBackground={setPopupButtonBackground}
                  popupButtonTextColor={popupButtonTextColor}
                  setPopupButtonTextColor={setPopupButtonTextColor}
                />
              )}
              {embedMode === "popover" && (
                <PopoverEmbedMode
                  isPopoverProcess={isPopoverProcess}
                  setPopoverProcess={setPopoverProcess}
                  popoverButtonBackground={popoverButtonBackground}
                  setPopoverButtonBackground={setPopoverButtonBackground}
                  popoverIconColor={popoverIconColor}
                  setPopoverIconColor={setPopoverIconColor}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmbedWebpageRightside;
