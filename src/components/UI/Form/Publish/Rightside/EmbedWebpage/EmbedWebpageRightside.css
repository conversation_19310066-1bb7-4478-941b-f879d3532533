.publishPage-embedWebpageRightside-container {
  width: 260px;
  border: 1px solid var(--border);
  background-color: var(--grey400);
  position: relative;
  height: calc(100% - 25px);
  right: 20px;
  transition: 0.15s all;
  transform: scale(1);
  /*box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;*/
  border: 1px solid var(--grey200);
  font-family: "Exo 2", sans-serif;
  padding-top: 8px;
  overflow-y: auto;
}

.publishPage-embedWebpageRightside-header {
  font-size: 17px;
  font-weight: 500;
  color: var(--black);
  border-bottom: 1px solid var(--grey100);
  padding: 0 0 10px 0;
  margin-bottom: 10px;
  margin-left: -10px;
  margin-right: -10px;
  padding-top: 10px;
  padding-left: 8px;

  margin-top: -16px;
}

.publishPage-embedWebpageRightside-header-container {
  padding: 8px 10px;
}

.publishPage-embedWebpageRightside-social-items {
  display: flex;
  justify-content: space-around;
  border-bottom: 1px solid var(--grey100);
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.publishPage-embedWebpageRightside-embed-setting-option-item-icon {
  font-size: 20px;
  margin-right: 10px;
  background: var(--black);
  color: var(--white);
  border-radius: 5px;
  padding: 4px;
}

.publishPage-embedWebpageRightside-embed-setting-option-item-title {
  font-size: 15px;
  font-weight: 500;
  font-family: "Exo 2", sans-serif;
}

.publishPage-embedWebpageRightside-embed-settings {
}

.publishPage-embedWebpageRightside-embed-setting-item {
  margin-bottom: 20px;
}

.publishPage-embedWebpageRightside-embed-setting-item-title {
  font-size: 15px;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  color: var(--black);
  margin-bottom: 15px;
}

.publishPage-embedWebpageRightside-embed-setting-options-container {
}

.publishPage-embedWebpageRightside-embed-setting-option-item {
  display: flex;
  align-items: center;
}

.publishPage-embedWebpageRightside-embed-setting-option-item {
}

.ant-select-single {
  height: 25px !important;
}

.publishPage-embedWebpageRightside-social-setting-item-input {
  width: 100%;
  height: 35px;
  border-radius: 5px;
  border: 1px solid var(--grey100);
  background-color: var(--grey200);
  padding: 10px;
  font-size: 13px;
  color: var(--black);
}

.publishPage-embedWebpageRightside-social-setting-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100px;
  background-color: white;
  border-radius: 5px;
}

.publishPage-embedWebpageRightside-divider {
  border-bottom: 1px solid var(--grey100);
  margin: 20px 0;
}

.publishPage-embedWebpageRightside-social-setting-item-uploadButton {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  background-color: var(--black);
  color: white;
  border-radius: 5px;
  cursor: pointer;
}

.publishPage-embedWebpageRightside-social-setting-item-uploadButton:hover {
  opacity: 0.8;
}

.publishPage-embedWebpageRightside-embed-options {
  font-family: "Exo 2", sans-serif;
}

.publishPage-embedWebpageRightside-embed-options-title {
  font-weight: 500;
  font-size: 15px;
  color: var(--black);
}

.publishPage-embedWebpageRightside-embed-option-container {
  margin-top: 15px;
}

.publishPage-embedWebpageRightside-embed-option-title {
  color: var(--black);
  opacity: 0.9;
  font-size: 14px;
  margin-bottom: 5px;
}

.publishPage-embedWebpageRightside-embed-option-input {
  width: 100%;
  height: 35px;
  border-radius: 5px;
  border: 1px solid var(--grey100);
  background-color: var(--white);
  padding: 10px;
  font-size: 13px;
  color: var(--black);
}
