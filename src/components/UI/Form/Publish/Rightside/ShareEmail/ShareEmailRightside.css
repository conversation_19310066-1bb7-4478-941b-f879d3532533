.publishPage-shareEmailRightside-container {
  width: 260px;
  border: 1px solid var(--border);
  background-color: var(--grey400);
  position: relative;
  height: calc(100% - 25px);
  right: 20px;
  transition: 0.15s all;
  transform: scale(1);
  /*box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;*/
  border: 1px solid var(--grey200);
  font-family: "Exo 2", sans-serif;
  padding-top: 8px;
  overflow-y: auto;
}

.publishPage-shareEmailRightside-settings-container {
  padding: 8px 10px;
}

.publishPage-shareEmailRightside-setting-item {
  margin-bottom: 20px;
}

.publishPage-shareEmailRightside-setting-item-title {
  font-size: 15px;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  color: var(--black);
  margin-bottom: 10px;
}

.publishPage-shareEmailRightside-setting-item-input {
  width: 100%;
  height: 35px;
  border-radius: 5px;
  border: 1px solid var(--grey100);
  background-color: var(--white);
  padding: 10px;
  font-size: 13px;
  color: var(--black);
}

.publishPage-shareEmailRightside-setting-item-addToButton {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 10px;
  border-radius: 5px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--black);
  border: 1px solid var(--black);
  color: var(--white);
  cursor: pointer;
  margin-top: 10px;
}

.publishPage-shareEmailRightside-setting-item-addToButton:hover {
  opacity: 0.8;
}

.publishPage-shareEmailRightside-setting-item-addToButton-disabled {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 10px;
  border-radius: 5px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--grey200);
  border: 1px solid var(--grey100);
  color: #575757;
  cursor: default;
  margin-top: 10px;
}

.publishPage-shareEmailRightside-setting-item-input:focus {
}

.publishPage-shareEmail-modal-container {
  display: flex;
  flex-direction: column;
}

.publishPage-shareEmailRightSide-divider {
  border-bottom: 1px solid var(--grey100);
  margin-bottom: 20px;
}

.publishPage-shareEmailRightside-error {
  font-size: 14px;
  color: var(--red100);
  font-weight: 500;
  padding: 1px 2px;
  margin-top: 1px;
}
