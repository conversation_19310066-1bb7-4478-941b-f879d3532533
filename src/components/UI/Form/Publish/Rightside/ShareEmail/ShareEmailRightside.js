import { Modal, message } from "antd";
import "./ShareEmailRightside.css";
import { useState } from "react";

const ShareEmailRightside = ({
  emailSenderName,
  setEmailSenderName,
  emailSenderEmail,
  setEmailSenderEmail,
  emailToList,
  setEmailToList,
  setTriggerAction,
}) => {
  const [emailForTo, setEmailForTo] = useState();
  const [emailForToError, setEmailForToError] = useState();
  const [openedErrorModal, setOpenedErrorModal] = useState(false);
  const [modalErrorMessage, setModalErrorMessage] = useState();

  const [messageApi, contextHolder] = message.useMessage();

  const onChangeEmailForTo = (e) => {
    const value = e.target.value;
    if (value && value.length > 0) {
      if (
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
          value
        )
      ) {
        setEmailForToError(null);
      } else {
        setEmailForToError("Enter a valid e-email address");
      }
    } else {
      setEmailForToError(null);
    }
    setEmailForTo(value);
  };

  const addEmailEmailOnToList = () => {
    setTriggerAction(true);
    const error = isErrorAddedEmail();
    if (error === true) {
      return;
    }
    const tempEmailTolist = emailToList;
    tempEmailTolist.push(emailForTo);

    setEmailForTo("");
    messageApi.open({
      type: "success",
      content: "Added mail in mail to list",
    });
    setTimeout(() => {
      setTriggerAction(false);
      setEmailToList(tempEmailTolist);
    }, 200);
  };

  const isErrorAddedEmail = () => {
    if (emailToList.includes(emailForTo)) {
      setOpenedErrorModal(true);
      setModalErrorMessage("Email has already been added");
      return true;
    }
    return false;
  };

  const onChangeEmailSenderName = (e) => {
    const value = e.target.value;
    setEmailSenderName(value);
  };

  const onChangeEmailSenderMail = (e) => {
    const value = e.target.value;
    setEmailSenderEmail(value);
  };

  return (
    <>
      {contextHolder}
      <div className="publishPage-shareEmailRightside-container">
        <div className="publishPage-shareEmailRightside-settings-container">
          <div className="publishPage-shareEmailRightside-setting-item">
            <div className="publishPage-shareEmailRightside-setting-item-title">
              Email Address
            </div>
            <input
              value={emailForTo}
              onChange={onChangeEmailForTo}
              className="publishPage-shareEmailRightside-setting-item-input"
            />
            <div className="publishPage-shareEmailRightside-error">
              {emailForToError}
            </div>
            <div
              className={
                emailForTo && emailForTo.length > 0 && !emailForToError
                  ? "publishPage-shareEmailRightside-setting-item-addToButton"
                  : "publishPage-shareEmailRightside-setting-item-addToButton-disabled"
              }
              onClick={
                emailForTo && emailForTo.length > 0 && !emailForToError
                  ? addEmailEmailOnToList
                  : () => {}
              }
            >
              Add Email for To
            </div>
          </div>
          <div className="publishPage-shareEmailRightSide-divider" />
          <div className="publishPage-shareEmailRightside-setting-item">
            <div className="publishPage-shareEmailRightside-setting-item-title">
              Email sender name
            </div>
            <input
              value={emailSenderName}
              onChange={onChangeEmailSenderName}
              className="publishPage-shareEmailRightside-setting-item-input"
            />
          </div>
          <div className="publishPage-shareEmailRightside-setting-item">
            <div
              defaultValue="<EMAIL>"
              className="publishPage-shareEmailRightside-setting-item-title"
            >
              Email reply-to email
            </div>
            <input
              value={emailSenderEmail}
              onChange={onChangeEmailSenderMail}
              className="publishPage-shareEmailRightside-setting-item-input"
            />
          </div>
        </div>
      </div>
      <Modal
        title="Error"
        open={openedErrorModal}
        onCancel={() => setOpenedErrorModal(false)}
        footer={() => {
          <div />;
        }}
      >
        <div className="publishPage-shareEmail-modal-container">
          <div className="publishPage-shareEmail-modal-label">
            {modalErrorMessage}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ShareEmailRightside;
