!(function () {
  var t = document.createElement("style");
  t.textContent = `
        body {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        button {
            display: none;
        }
        .button-container {
            position: fixed;
            top: 53%;
            left: 40px;
            transform: translateY(-50%) rotate(-90deg);
            transform-origin: left bottom;
            z-index: 1001;
            white-space: nowrap;
            padding: 15px 20px 10px 20px;
            background-color: transparent;
            border: none;
            cursor: pointer;
            font-family: 'Exo 2', sans-serif;
            font-size: 20px;
            font-weight: 500;
            letter-spacing: 1px;
            line-height: 1;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
            box-shadow: 0px 4px 8px -3px rgba(0,0,0,0.4);
        }
        .button-container:hover {
            opacity: .75;
            transition: all .3s ease-out;
        }
        .drawer-container {
            position: fixed;
            top: 0;
            height: 100%;
            background-color: #fff;
            box-shadow: 1px 0 10px rgba(0, 0, 0, 0.07);
            transition: left 0.3s ease;
            z-index: 1000;
        }
        .iframe-container {
            width: 100%;
            height: 100%;
            border: none;
        }
        .close-button-container, .close-button-container-mobile {
            display: none;
            justify-content: center;
            align-items: center;
            position: absolute;
            background: #f0f0f0;
            z-index: 1001;
            cursor: pointer;
        }
        .close-button-container {
            top: 20px;
            left: 40%;
            width: 32px;
            height: 32px;
            border-top-right-radius: 10px;
            border-bottom-right-radius: 10px;
        }
        .close-button-container-mobile {
            top: 10px;
            right: 20px;
            width: 24px;
            height: 24px;
            border-radius: 5px;
        }
        .close-button-container:hover,
        .close-button-container-mobile:hover {
            opacity: .75;
        }
        .close-button-icon {
            margin-top: -4px;
            margin-left: -4px;
        }
    `;
  document.head.appendChild(t);

  var userAgent = navigator.userAgent,
    width = window.innerWidth,
    deviceType =
      /Mobi|Android/i.test(userAgent) || width < 768
        ? "mobile"
        : /iPad|Tablet/i.test(userAgent) || (width >= 768 && width < 1024)
        ? "Tablet"
        : width >= 1024
        ? "desktop"
        : "unknown";

  var formContainer = document.getElementById("form-container");
  if (formContainer) {
    var form = formContainer.getAttribute("form");
    if (form) {
      var btn = formContainer.getAttribute("btn");
      if (btn) {
        var btnColors = btn.split(" ");
        var buttonContainer = document.createElement("div");
        buttonContainer.className = "button-container";
        buttonContainer.style.background = btnColors[0];
        buttonContainer.style.color = btnColors[1];
        buttonContainer.innerText = formContainer.getAttribute("btnTxt");
        document.body.appendChild(buttonContainer);
      }

      var drawerContainer = document.createElement("div");
      drawerContainer.className = "drawer-container";
      drawerContainer.style.width = deviceType === "mobile" ? "100%" : "40%";
      drawerContainer.style.left = deviceType === "mobile" ? "-100%" : "-40%";
      document.body.appendChild(drawerContainer);

      var closeButtonContainer = document.createElement("div");
      closeButtonContainer.className =
        deviceType === "mobile"
          ? "close-button-container-mobile"
          : "close-button-container";
      var closeButtonIcon = document.createElement("img");
      closeButtonIcon.className = "close-button-icon";
      closeButtonIcon.src =
        "data:image/svg+xml;base64," +
        btoa(`
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" id="close">
                    <path fill="#000" d="M7.05 7.05a1 1 0 0 0 0 1.414L10.586 12 7.05 15.536a1 1 0 1 0 1.414 1.414L12 13.414l3.536 3.536a1 1 0 0 0 1.414-1.414L13.414 12l3.536-3.536a1 1 0 0 0-1.414-1.414L12 10.586 8.464 7.05a1 1 0 0 0-1.414 0Z"></path>
                </svg>
            `);
      closeButtonContainer.appendChild(closeButtonIcon);
      document.body.appendChild(closeButtonContainer);

      buttonContainer.addEventListener("click", function () {
        if (drawerContainer.style.left === "0px") {
          closeButtonContainer.style.display = "none";
          buttonContainer.style.display = "flex";
        } else {
          drawerContainer.style.left = "0px";
          var iframe = document.createElement("iframe");
          iframe.src = form;
          iframe.className = "iframe-container";
          drawerContainer.appendChild(iframe);
          setTimeout(() => {
            closeButtonContainer.style.display = "flex";
            buttonContainer.style.display = "none";
          }, 300);
        }
      });

      closeButtonContainer.addEventListener("click", function () {
        drawerContainer.style.left = deviceType === "mobile" ? "-100%" : "-40%";
        closeButtonContainer.style.display = "none";
        buttonContainer.style.display = "flex";
      });

      drawerContainer.style.left = deviceType === "mobile" ? "-100%" : "-40%";
    }
  }
})();
