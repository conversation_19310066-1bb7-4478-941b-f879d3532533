import { useEffect, useState } from "react";
import "./PopupEmbedCode.css";
import PopupModal from "./PopupModal";

const PopupEmbedCode = ({
  form,
  theme,
  selectedPreview,
  isPopupProcess,
  setPopupProcess,
  popupButtonText,
  popupButtonBackground,
  popupButtonTextColor,
}) => {
  const [isPopupModalOpen, setPopupModalOpen] = useState(false);

  useEffect(() => {
    setPopupProcess(false);
  }, [isPopupProcess]);
  return (
    <>
      <div className="popupEmbedCode-container">
        <div
          onClick={() => setPopupModalOpen(true)}
          className="popupEmbedCode-button-container"
          style={{
            background: popupButtonBackground,
            color: popupButtonTextColor,
          }}
        >
          {popupButtonText}
        </div>
        <PopupModal
          isOpen={isPopupModalOpen}
          onClose={() => setPopupModalOpen(false)}
          form={form}
          theme={theme}
          isPreview={true}
          perspective={selectedPreview}
        />
      </div>
    </>
  );
};

export default PopupEmbedCode;
