import { useEffect } from "react";
import FForm from "../../../../../../../pages/fform/FForm";
import "./IFrameEmbedCode.css";

const IFrameEmbedCode = ({
  form,
  theme,
  selectedPreview,
  isIFrameProcess,
  setIFrameProcess,
  iFrameWidth,
  iFrameWidthType,
  iFrameHeight,
  iFrameHeightType,
}) => {
  useEffect(() => {
    setIFrameProcess(false);
  }, [isIFrameProcess]);
  return (
    <>
      <div style={{ display: "flex", justifyContent: "center" }}>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            overflowY: "auto",
            overflowX: "hidden",
            marginBottom: "30px",
            width: `${iFrameWidth}${iFrameWidthType === "pixel" ? "px" : "%"}`,
            height: `${iFrameHeight}${
              iFrameHeightType === "pixel" ? "px" : "%"
            }`,
          }}
        >
          <div className="iFrameEmbedCode-container">
            <FForm
              form={form}
              theme={theme}
              isPreview={true}
              isOpen={true}
              perspective={selectedPreview}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default IFrameEmbedCode;
