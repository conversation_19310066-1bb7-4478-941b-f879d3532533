.popoverEmbedCode-container {
    position: relative;
    height: calc(100% - 460px);   
}

.popoverEmbedCode-form-container {
    display: flex;
    justify-content: center;
    width: 400px;
    height: fit-content;
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;
    margin-bottom: 30px;
    position: absolute;
    bottom: 35px;
    right: 25px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 1px 4px;
    border-radius: 10px;
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.2s ease-in, transform 0.2s ease-in;
}

.popoverEmbedCode-form-container-hidden {
    opacity: 0; 
    transform: translateY(20px); 
    pointer-events: none;
}

.popoverEmbedCode-popover-container {
    position: absolute;
    bottom: 0;
    right: 50px;
    width: 55px;
    height: 55px;
    border-radius: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 26px;
    cursor: pointer;
}

.popoverEmbedCode-popover-container:hover {
    opacity: .8;
    transition: all .3s ease-in;
}


/* MOBILE */
.popoverEmbedCode-mobile-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    width: 100%;
    overflow-y: auto; 
    overflow-x: hidden;
    box-sizing: border-box; 
}

.popoverEmbedCode-mobile-form-container {
    /* display: flex; */
    flex-direction: column;
    justify-content: center;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    position: relative;
}

.popoverEmbedCode-mobile-form-container-hidden {
    display: none;
}

.popoverEmbedCode-mobile-close {
    display: flex;
    justify-content: flex-end;
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    font-size: 20;
    transition: opacity 0.2s ease-in;
    color: var(--black);
    font-size: 20px;
    cursor: pointer;
}

.popoverEmbedCode-mobile-popover-container {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 55px;
    height: 55px;
    border-radius: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 26px;
    cursor: pointer;
}

.popoverEmbedCode-mobile-popover-container-hidden {
    display: none;
}

.popoverEmbedCode-mobile-popover-container:hover {
    opacity: 0.8;
    transition: all 0.3s ease-in;
}

/* --- END MOBILE --- */