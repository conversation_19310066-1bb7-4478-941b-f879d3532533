import { useEffect, useState } from "react";
import FForm from "../../../../../../../pages/fform/FForm";
import "./PopoverEmbedCode.css";
import { IoMdClose } from "react-icons/io";
import { BiSolidMessageDetail } from "react-icons/bi";

const PopoverEmbedCode = ({
  form,
  theme,
  selectedPreview,
  isPopoverProcess,
  setPopoverProcess,
  popoverButtonBackground,
  popoverIconColor,
}) => {
  const [isDisplayForm, setDisplayForm] = useState(false);

  useEffect(() => {
    setPopoverProcess(false);
  }, [isPopoverProcess]);

  const togglePopover = () => {
    setDisplayForm((prev) => !prev);
  };

  if (selectedPreview === "mobile") {
    return (
      <div
        className={
          isDisplayForm ? `popoverEmbedCode-${selectedPreview}-container` : ""
        }
      >
        <div
          className={`popoverEmbedCode-${selectedPreview}-form-container ${
            isDisplayForm ? "" : "popoverEmbedCode-mobile-form-container-hidden"
          }`}
        >
          <div
            className={`popoverEmbedCode-${selectedPreview}-close`}
            onClick={() => setDisplayForm(false)}
          >
            <IoMdClose />
          </div>
          <FForm
            form={form}
            theme={theme}
            isPreview={true}
            isOpen={true}
            perspective={selectedPreview}
          />
        </div>

        <div
          onClick={togglePopover}
          className={`popoverEmbedCode-${selectedPreview}-popover-container ${
            !isDisplayForm
              ? ""
              : "popoverEmbedCode-mobile-popover-container-hidden"
          }`}
          style={{
            background: popoverButtonBackground,
            color: popoverIconColor,
          }}
        >
          {isDisplayForm ? <IoMdClose /> : <BiSolidMessageDetail />}
        </div>
      </div>
    );
  } else {
    return (
      <div className="popoverEmbedCode-container">
        <div
          className={`popoverEmbedCode-form-container ${
            isDisplayForm ? "" : "popoverEmbedCode-form-container-hidden"
          }`}
        >
          <FForm
            form={form}
            theme={theme}
            isPreview={true}
            isOpen={true}
            perspective={selectedPreview}
          />
        </div>

        <div
          onClick={togglePopover}
          className={`popoverEmbedCode-popover-container ${
            !isDisplayForm ? "" : "popoverEmbedCode-popover-container-hidden"
          }`}
          style={{
            background: popoverButtonBackground,
            color: popoverIconColor,
          }}
        >
          {isDisplayForm ? <IoMdClose /> : <BiSolidMessageDetail />}
        </div>
      </div>
    );
  }
};

export default PopoverEmbedCode;
