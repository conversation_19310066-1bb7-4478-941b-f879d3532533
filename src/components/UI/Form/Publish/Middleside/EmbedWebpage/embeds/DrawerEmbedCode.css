.button-container-left {
    position: absolute;
    left: 40px;
    min-height: 40px;
    transform: translateY(-50%) rotate(-90deg);
    transform-origin: left bottom;
    z-index: 1001;
    white-space: nowrap;
    padding: 15px 25px 10px 25px;
    cursor: pointer;
    font-family: 'Exo 2', sans-serif;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 1px;
    line-height: 1;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    box-shadow: 0px 4px 8px -3px rgba(0,0,0,0.4);
  }

  .button-container-right {
    position: absolute;
    right: 40px;
    min-height: 40px;
    transform: translateY(50%) rotate(-90deg);
    transform-origin: right top;
    z-index: 1001;
    white-space: nowrap;
    padding: 10px 20px 15px 20px;
    cursor: pointer;
    font-family: 'Exo 2', sans-serif;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 1px;
    line-height: 1;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    box-shadow: 0px 4px 8px -3px rgba(0, 0, 0, 0.4);
  }

  .button-container-left:hover {
    opacity: .75;
    transition: all .3s ease-out;
  }

  .button-container-right:hover {
    opacity: .75;
    transition: all .3s ease-out;
  }

  .drawer-container-left {
    position: absolute;
    top: 0;
    height: 100%;
    min-width: 300px;
    background-color: #fff;
    box-shadow: 1px 0 10px rgba(0, 0, 0, 0.07);
    transition: left 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
    display: grid;
  }

  .drawer-container-right {
    position: absolute;
    top: 0;
    height: 100%;
    min-width: 300px;
    background-color: #fff;
    box-shadow: 1px 0 10px rgba(0, 0, 0, 0.07);
    transition: right 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
    display: grid;
  }
  .iframe-container-left {
    width: 100%;
    height: 100%;
    border: none;
  }

  .iframe-container-right {
    width: 100%;
    height: 100%;
    border: none;
  }

  .close-button-container-left {
    position: absolute;
    top: 10px;
    right: 0;
    width: 32px;
    height: 32px;
    z-index: 1001;
    cursor: pointer;
    border-radius: 5px;
  }

  .close-button-container-right {
    position: absolute;
    top: 10px;
    left: 0;
    width: 32px;
    height: 32px;
    z-index: 1001;
    cursor: pointer;
    border-radius: 5px;
  }