import React, { useEffect, useState } from "react";
import "./DrawerEmbedCode.css";
import FForm from "../../../../../../../pages/fform/FForm";

const DrawerEmbedCode = ({
  form,
  theme,
  selectedPreview,
  isDrawerProcess,
  drawerButtonText,
  drawerButtonBackground,
  drawerButtonTextColor,
  drawerPosition,
}) => {
  const [drawerVisible, setDrawerVisible] = useState(false);

  useEffect(() => {
    setDrawerVisible(false);
  }, [isDrawerProcess]);

  const toggleDrawer = () => {
    setDrawerVisible(!drawerVisible);
  };

  const closeDrawer = () => {
    setDrawerVisible(false);
  };

  const buttonTopResize = () => {
    if (drawerButtonText.length < 3) {
      return "52%";
    } else if (drawerButtonText.length >= 3 && drawerButtonText.length <= 8) {
      return "56%";
    } else if (drawerButtonText.length > 8 && drawerButtonText.length <= 13) {
      return "59%";
    } else if (drawerButtonText.length > 13 && drawerButtonText.length <= 15) {
      return "61%";
    } else if (drawerButtonText.length > 15 && drawerButtonText.length <= 20) {
      return "65%";
    }
  };

  const buttonBottomResize = () => {
    if (drawerButtonText.length <= 3) {
      return "52%";
    } else if (drawerButtonText.length > 3 && drawerButtonText.length <= 8) {
      return "56%";
    } else if (drawerButtonText.length > 8 && drawerButtonText.length <= 13) {
      return "58%";
    } else if (drawerButtonText.length > 13 && drawerButtonText.length <= 15) {
      return "61%";
    } else if (drawerButtonText.length > 15 && drawerButtonText.length <= 20) {
      return "65%";
    }
  };

  if (drawerPosition === "left") {
    return (
      <>
        {!drawerVisible && (
          <div
            className="button-container-left"
            onClick={toggleDrawer}
            style={{
              background: drawerButtonBackground,
              color: drawerButtonTextColor,
              top: buttonTopResize(),
            }}
          >
            {drawerButtonText}
          </div>
        )}

        <div
          className="drawer-container-left"
          style={{
            left: drawerVisible
              ? "0"
              : selectedPreview !== "desktop"
              ? "-120%"
              : "-120%",
            width: selectedPreview !== "desktop" ? "100%" : "50%",
          }}
        >
          <FForm
            form={form}
            theme={theme}
            isPreview={true}
            isOpen={true}
            perspective="desktop"
          />
          <div className="close-button-container-left" onClick={closeDrawer}>
            <img
              className="close-button-icon"
              src={`data:image/svg+xml;base64,${btoa(`
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none">
                  <path fill="#000" d="M7.05 7.05a1 1 0 0 0 0 1.414L10.586 12 7.05 15.536a1 1 0 1 0 1.414 1.414L12 13.414l3.536 3.536a1 1 0 0 0 1.414-1.414L13.414 12l3.536-3.536a1 1 0 0 0-1.414-1.414L12 10.586 8.464 7.05a1 1 0 0 0-1.414 0Z"></path>
                </svg>
              `)}`}
              alt="close"
            />
          </div>
        </div>
      </>
    );
  } else {
    return (
      <div>
        {!drawerVisible && (
          <div
            className="button-container-right"
            onClick={toggleDrawer}
            style={{
              background: drawerButtonBackground,
              color: drawerButtonTextColor,
              bottom: buttonBottomResize(),
            }}
          >
            {drawerButtonText}
          </div>
        )}

        <div
          className="drawer-container-right"
          style={{
            right: drawerVisible
              ? "0"
              : selectedPreview !== "desktop"
              ? "-120%"
              : "-120%",
            width: selectedPreview !== "desktop" ? "100%" : "50%",
          }}
        >
          <FForm
            form={form}
            theme={theme}
            isPreview={true}
            isOpen={true}
            perspective="desktop"
          />
          <div className="close-button-container-right" onClick={closeDrawer}>
            <img
              className="close-button-icon"
              src={`data:image/svg+xml;base64,${btoa(`
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none">
                  <path fill="#000" d="M7.05 7.05a1 1 0 0 0 0 1.414L10.586 12 7.05 15.536a1 1 0 1 0 1.414 1.414L12 13.414l3.536 3.536a1 1 0 0 0 1.414-1.414L13.414 12l3.536-3.536a1 1 0 0 0-1.414-1.414L12 10.586 8.464 7.05a1 1 0 0 0-1.414 0Z"></path>
                </svg>
              `)}`}
              alt="close"
            />
          </div>
        </div>
      </div>
    );
  }
};

export default DrawerEmbedCode;
