import React from "react";
import "./PopupModal.css"; // Modal için stil dosyası
import FForm from "../../../../../../../pages/fform/FForm";

const PopupModal = ({
  isOpen,
  onClose,
  form,
  theme,
  isPreview,
  perspective,
}) => {
  if (!isOpen) return null;

  return (
    <div className="popupModal-overlay">
      <div className="popupModal-close">
        <img
          onClick={onClose}
          style={{ cursor: "pointer" }}
          src={`data:image/svg+xml;base64,${btoa(`
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none">
                  <path fill="#000" d="M7.05 7.05a1 1 0 0 0 0 1.414L10.586 12 7.05 15.536a1 1 0 1 0 1.414 1.414L12 13.414l3.536 3.536a1 1 0 0 0 1.414-1.414L13.414 12l3.536-3.536a1 1 0 0 0-1.414-1.414L12 10.586 8.464 7.05a1 1 0 0 0-1.414 0Z"></path>
                </svg>
              `)}`}
          alt="close"
        />
      </div>
      <div className="popupModal-content">
        <FForm
          form={form}
          theme={theme}
          isPreview={isPreview}
          isOpen={true}
          perspective="desktop"
        />
      </div>
    </div>
  );
};

export default PopupModal;
