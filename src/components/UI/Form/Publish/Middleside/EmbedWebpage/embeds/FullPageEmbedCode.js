import FForm from "../../../../../../../pages/fform/FForm";
import "./InPageEmbedCode.css";

const FullPageEmbedCode = ({ form, theme, selectedPreview }) => {
  /*
  useEffect(() => {
    setInPageProcess(false);
  }, [isInPageProcess]);
  */
  return (
    <>
      <div style={{ display: "flex", justifyContent: "center" }}>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            overflowY: "auto",
            overflowX: "hidden",
            marginBottom: "30px",
            width: "100%",
            height: "100%",
          }}
        >
          <div className="inPageEmbedCode-container">
            <FForm
              form={form}
              theme={theme}
              isPreview={true}
              isOpen={true}
              perspective={selectedPreview}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default FullPageEmbedCode;
