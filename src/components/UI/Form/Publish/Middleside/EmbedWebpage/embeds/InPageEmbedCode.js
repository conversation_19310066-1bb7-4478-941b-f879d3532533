import { useEffect } from "react";
import FForm from "../../../../../../../pages/fform/FForm";
import "./InPageEmbedCode.css";

const InPageEmbedCode = ({
  form,
  theme,
  selectedPreview,
  isInPageProcess,
  setInPageProcess,
  inPageWidth,
  inPageWidthType,
  inPageHeight,
  inPageHeightType,
}) => {
  useEffect(() => {
    setInPageProcess(false);
  }, [isInPageProcess]);
  return (
    <>
      <div style={{ display: "flex", justifyContent: "center" }}>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            overflowY: "auto",
            overflowX: "hidden",
            marginBottom: "30px",
            width: `${inPageWidth}${inPageWidthType === "pixel" ? "px" : "%"}`,
            height: `${inPageHeight}${
              inPageHeightType === "pixel" ? "px" : "%"
            }`,
          }}
        >
          <div className="inPageEmbedCode-container">
            <FForm
              form={form}
              theme={theme}
              isPreview={true}
              isOpen={true}
              perspective={selectedPreview}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default InPageEmbedCode;
