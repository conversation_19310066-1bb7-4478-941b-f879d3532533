import "./EmbedWebpage.css";
import { MdOutlineDesktopWindows } from "react-icons/md";
import { Modal, Popover, QRCode } from "antd";
import { HiMiniDevicePhoneMobile } from "react-icons/hi2";
import { FiTablet } from "react-icons/fi";
import { useEffect, useRef, useState, useCallback } from "react";
import { message } from "antd";
import { TbCodeDots } from "react-icons/tb";
import DrawerMode from "./modes/DrawerMode";
import InPageMode from "./modes/InPageMode";
import IFrameMode from "./modes/IFrameMode";
import PopupMode from "./modes/PopupMode";
import FullPageMode from "./modes/FullPageMode";
import PopoverMode from "./modes/PopoverMode";

const EmbedWebpage = ({
  form,
  theme,
  selectedPreview,
  setSelectedPreview,
  embedMode,
  isInPageProcess,
  setInPageProcess,
  inPageWidth,
  inPageWidthType,
  inPageHeight,
  inPageHeightType,
  isDrawerProcess,
  setDrawerProcess,
  drawerButtonText,
  drawerButtonBackground,
  drawerButtonTextColor,
  drawerPosition,
  isIFrameProcess,
  setIFrameProcess,
  iFrameWidth,
  iFrameWidthType,
  iFrameHeight,
  iFrameHeightType,
  isPopupProcess,
  setPopupProcess,
  popupButtonText,
  popupButtonBackground,
  popupButtonTextColor,
  isPopoverProcess,
  setPopoverProcess,
  popoverButtonBackground,
  popoverIconColor,
}) => {
  const [openedShowCodeModal, setOpenedShowCodeModal] = useState(false);
  const [openedQrModal, setOpenedQrModal] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();

  const qrRef = useRef();

  const formUrl = `http://localhost:3001/${form.id}`;

  const memoizedSetDrawerProcess = useCallback(
    () => setDrawerProcess(false),
    [setDrawerProcess]
  );
  const memoizedSetInPageProcess = useCallback(
    () => setInPageProcess(false),
    [setInPageProcess]
  );

  useEffect(() => {
    memoizedSetDrawerProcess();
    memoizedSetInPageProcess();
  }, [isInPageProcess, memoizedSetDrawerProcess, memoizedSetInPageProcess]);

  const getScreenSize = () => {
    if (selectedPreview === "desktop") {
      return "100%";
    } else if (selectedPreview === "tablet") {
      return "650px";
    } else if (selectedPreview === "mobile") {
      return "420px";
    } else {
      return "100%";
    }
  };

  const getEmbedCode = () => {
    if (embedMode === "inPage") {
      return `<div id="formiqo_ip" form="${formUrl}" width='${inPageWidth}${
        inPageWidthType === "pixel" ? "px" : "%"
      }' height='${inPageHeight}${
        inPageHeightType === "pixel" ? "px" : "%"
      }'></div><script defer src="InPageEmbed.min.js"></script>`;
    } else if (embedMode === "fullPage") {
      return `<div id="formiqo_fp" form="${formUrl}"></div><script defer src="FullPageEmbed.min.js"></script>`;
    } else if (embedMode === "iFrame") {
      return `<iframe src="${formUrl}" width='${iFrameWidth}${
        iFrameWidthType === "pixel" ? "px" : "%"
      }' height='${iFrameHeight}${
        iFrameHeightType === "pixel" ? "px" : "%"
      }' style='border: none; box-shadow: none;'></iframe>`;
    } else if (embedMode === "drawer") {
      if (drawerPosition === "left") {
        return `<div id="formiqo_d" form="${formUrl}" btn="${drawerButtonBackground} ${drawerButtonTextColor}" btnTxt="${drawerButtonText}"></div>
        <script defer src="LeftDrawerEmbed.min.js"></script>`;
      } else {
        return `<div id="formiqo_d" form="${formUrl}" btn="${drawerButtonBackground} ${drawerButtonTextColor}" btnTxt="${drawerButtonText}"></div>
        <script defer src="RightDrawerEmbed.min.js"></script>`;
      }
    } else if (embedMode === "popover") {
      return `<div id="formiqo_p" form="http://localhost:3001/Form-1729415005197-795" btnColor="${popoverButtonBackground}" iconColor="${popoverIconColor}"></div>
      <script defer src="PopoverEmbed.min.js"></script>`;
    }
  };

  const copyEmbedCode = () => {
    navigator.clipboard.writeText(getEmbedCode());
    messageApi.open({
      type: "success",
      content: "Link copied",
    });
  };

  const downloadQRCode = () => {
    if (qrRef.current) {
      const canvas = qrRef.current.querySelector("canvas");
      if (canvas) {
        const image = canvas.toDataURL("image/png");

        // QR kodunu indirmek için bir bağlantı oluştur
        const link = document.createElement("a");
        link.href = image;
        link.download = "My branded formiqo.png";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  };

  return (
    <>
      <div className="publishPage-embedWebpage-outer-container">
        {contextHolder}
        <div className="publishPage-embedWebpage-container">
          <div className="publishPage-embedWebpage-settings">
            <div
              className="publishPage-embedWebpage-settings-startEmbedding-container"
              onClick={() => setOpenedShowCodeModal(true)}
            >
              <div className="publishPage-embedWebpage-settings-startEmbedding">
                <div className="publishPage-embedWebpage-settings-startEmbedding-icon">
                  {" "}
                  <TbCodeDots />
                </div>
                <div className="publishPage-embedWebpage-settings-startEmbedding-title">
                  Show Embed Code
                </div>
              </div>
            </div>

            <div className="publishPage-embedWebpage-settings-divider"></div>
            <Popover
              placement="bottom"
              title="Desktop Preview"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div
                onClick={() => setSelectedPreview("desktop")}
                className={
                  selectedPreview === "desktop"
                    ? "publishPage-embedWebpage-settings-preview-item-selected"
                    : "publishPage-embedWebpage-settings-preview-item"
                }
              >
                <MdOutlineDesktopWindows />
              </div>
            </Popover>

            <Popover
              placement="bottom"
              title="Tablet Preview"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div
                onClick={() => setSelectedPreview("tablet")}
                className={
                  selectedPreview === "tablet"
                    ? "publishPage-embedWebpage-settings-preview-item-selected"
                    : "publishPage-embedWebpage-settings-preview-item"
                }
              >
                <FiTablet />
              </div>
            </Popover>

            <Popover
              placement="bottom"
              title="Mobile Preview"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div
                onClick={() => setSelectedPreview("mobile")}
                className={
                  selectedPreview === "mobile"
                    ? "publishPage-embedWebpage-settings-preview-item-selected"
                    : "publishPage-embedWebpage-settings-preview-item"
                }
              >
                <HiMiniDevicePhoneMobile />
              </div>
            </Popover>
          </div>
        </div>
        <div
          style={{
            width: "100%",
            display: "flex",
            justifyContent: "center",
            height: "calc(100% - 95px)",
            overflowY: "auto",
            overflowX: "hidden",
          }}
        >
          <div
            className="publishPage-embedWebpage-form-container"
            style={{ width: getScreenSize() }}
          >
            {embedMode === "inPage" && (
              <InPageMode
                form={form}
                theme={theme}
                selectedPreview={selectedPreview}
                isInPageProcess={isInPageProcess}
                setInPageProcess={setInPageProcess}
                inPageWidth={inPageWidth}
                inPageWidthType={inPageWidthType}
                inPageHeight={inPageHeight}
                inPageHeightType={inPageHeightType}
              />
            )}

            {embedMode === "fullPage" && (
              <FullPageMode
                form={form}
                theme={theme}
                selectedPreview={selectedPreview}
              />
            )}

            {embedMode === "iFrame" && (
              <IFrameMode
                form={form}
                theme={theme}
                selectedPreview={selectedPreview}
                isIFrameProcess={isIFrameProcess}
                setIFrameProcess={setIFrameProcess}
                iFrameWidth={iFrameWidth}
                iFrameWidthType={iFrameWidthType}
                iFrameHeight={iFrameHeight}
                iFrameHeightType={iFrameHeightType}
              />
            )}

            {embedMode === "drawer" && (
              <DrawerMode
                form={form}
                theme={theme}
                selectedPreview={selectedPreview}
                isDrawerProcess={isDrawerProcess}
                setDrawerProcess={setDrawerProcess}
                drawerButtonText={drawerButtonText}
                drawerButtonBackground={drawerButtonBackground}
                drawerButtonTextColor={drawerButtonTextColor}
                drawerPosition={drawerPosition}
              />
            )}

            {embedMode === "popup" && (
              <PopupMode
                form={form}
                theme={theme}
                selectedPreview={selectedPreview}
                isPopupProcess={isPopupProcess}
                setPopupProcess={setPopupProcess}
                popupButtonText={popupButtonText}
                popupButtonBackground={popupButtonBackground}
                popupButtonTextColor={popupButtonTextColor}
              />
            )}
            {embedMode === "popover" && (
              <PopoverMode
                form={form}
                theme={theme}
                selectedPreview={selectedPreview}
                isPopoverProcess={isPopoverProcess}
                setPopoverProcess={setPopoverProcess}
                popoverButtonBackground={popoverButtonBackground}
                popoverIconColor={popoverIconColor}
              />
            )}
          </div>
        </div>
      </div>

      <Modal
        title="Embed Code"
        open={openedShowCodeModal}
        onCancel={() => setOpenedShowCodeModal(false)}
        footer={() => {
          <div />;
        }}
      >
        <div className="publishPage-embedWebpage-modal-message-container">
          <div className="publishPage-embedWebpage-modal-message">
            Paste the code below into your website
          </div>
          <div
            style={{
              background: "var(--grey400)",
              border: "1px solid var(--grey200)",
              borderRadius: "5px",
              padding: "10px",
              fontSize: "12px",
            }}
          >
            {getEmbedCode()}
          </div>
          <div className="publishPage-embedWebpage-modal-copy-button-container">
            <div
              className="publishPage-embedWebpage-modal-copy-button"
              onClick={copyEmbedCode}
            >
              Copy Code
            </div>
          </div>
        </div>
      </Modal>

      <Modal
        title="Get the QR code"
        open={openedQrModal}
        onCancel={() => setOpenedQrModal(false)}
        footer={() => {
          <div />;
        }}
      >
        <div className="publishPage-embedWebpage-modal-message-container">
          <div className="publishPage-embedWebpage-modal-message">
            Scan the code to launch your formiqo. Works online and offline
            (though you’ll need a printer, obviously).
          </div>
          <div className="publishPage-embedWebpage-modal-qr" ref={qrRef}>
            <QRCode size={200} value="https://www.google.com" />
          </div>
          <div style={{ width: "100%" }}>
            <div className="publisPage-embedWebpage-download-button-container">
              <div
                className="publisPage-embedWebpage-download-button"
                onClick={downloadQRCode}
              >
                Download QR Code
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default EmbedWebpage;
