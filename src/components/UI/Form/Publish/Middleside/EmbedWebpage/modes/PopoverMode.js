import { BsFillArrowRightSquareFill } from "react-icons/bs";
import PopoverEmbedCode from "../embeds/PopoverEmbedCode";

const PopoverMode = ({
  form,
  theme,
  selectedPreview,
  isPopoverProcess,
  setPopoverProcess,
  popoverButtonBackground,
  popoverIconColor,
}) => {
  return (
    <>
      <div style={{ position: "relative", top: "0", width: "100%" }}>
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "70%" }}
        />
        <div style={{ margin: "90px 0" }} />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "70%" }}
        />

        <div style={{ margin: "90px 0" }} />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
      </div>

      <div
        style={{
          display: "flex",
          marginRight: "70px",
          alignItems: "center",
          position: "absolute",
          bottom: selectedPreview === "mobile" ? "25px" : "30px",
          right: selectedPreview === "mobile" ? "20px" : "80px",
        }}
      >
        <div
          style={{
            fontFamily: "'Exo 2', sans-serif",
            fontSize: selectedPreview === "mobile" ? "12px" : "18px",
            color: "var(--black)",
          }}
        >
          Hit the button to see your side tab in action
        </div>
        <BsFillArrowRightSquareFill
          style={{
            fontSize: selectedPreview === "mobile" ? "22px" : "30px",
            color: "var(--orange200)",
            marginLeft: selectedPreview === "mobile" ? "10px" : "10px",
          }}
        />
      </div>
      <PopoverEmbedCode
        form={form}
        theme={theme}
        selectedPreview={selectedPreview}
        isPopoverProcess={isPopoverProcess}
        setPopoverProcess={setPopoverProcess}
        popoverButtonBackground={popoverButtonBackground}
        popoverIconColor={popoverIconColor}
      />
    </>
  );
};

export default PopoverMode;
