import IFrameEmbedCode from "../embeds/IFrameEmbedCode";

const IFrameMode = ({
  form,
  theme,
  selectedPreview,
  isIFrameProcess,
  setIFrameProcess,
  iFrameWidth,
  iFrameWidthType,
  iFrameHeight,
  iFrameHeightType,
}) => {
  return (
    <>
      <div style={{ position: "relative", top: "0", width: "100%" }}>
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "70%" }}
        />
      </div>

      <IFrameEmbedCode
        form={form}
        theme={theme}
        selectedPreview={selectedPreview}
        isIFrameProcess={isIFrameProcess}
        setIFrameProcess={setIFrameProcess}
        iFrameWidth={iFrameWidth}
        iFrameWidthType={iFrameWidthType}
        iFrameHeight={iFrameHeight}
        iFrameHeightType={iFrameHeightType}
      />
    </>
  );
};

export default IFrameMode;
