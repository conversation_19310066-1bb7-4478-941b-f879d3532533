import {
  BsArrowLeftSquareFill,
  BsFillArrowRightSquareFill,
} from "react-icons/bs";
import DrawerEmbedCode from "../embeds/DrawerEmdedCode";

const DrawerMode = ({
  form,
  theme,
  selectedPreview,
  isDrawerProcess,
  setDrawerProcess,
  drawerButtonText,
  drawerButtonBackground,
  drawerButtonTextColor,
  drawerPosition,
}) => {
  return (
    <>
      <div style={{ position: "absolute", top: "0", width: "100%" }}>
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "75%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "70%" }}
        />
      </div>

      <div className="drawerMode-content" style={{ height: "100%" }}>
        <div
          style={{
            display: "flex",
            justifyContent:
              drawerPosition === "left" ? "flex-start" : "flex-end",
            alignItems: "center",
            width: "100%",
            height: "100%",
          }}
        >
          <DrawerEmbedCode
            form={form}
            theme={theme}
            selectedPreview={selectedPreview}
            isDrawerProcess={isDrawerProcess}
            setDrawerProcess={setDrawerProcess}
            drawerButtonText={drawerButtonText}
            drawerButtonBackground={drawerButtonBackground}
            drawerButtonTextColor={drawerButtonTextColor}
            drawerPosition={drawerPosition}
          />
          {drawerPosition === "left" && (
            <div
              style={{
                display: "flex",
                marginLeft: "100px",
                alignItems: "center",
              }}
            >
              <BsArrowLeftSquareFill
                style={{
                  fontSize: "30px",
                  color: "var(--orange200)",
                  marginRight: "20px",
                }}
              />
              <div
                style={{
                  fontFamily: "'Exo 2', sans-serif",
                  fontSize: "18px",
                  color: "var(--black)",
                }}
              >
                Hit the button to see your side tab in action
              </div>
            </div>
          )}

          {drawerPosition === "right" && (
            <div
              style={{
                display: "flex",
                marginRight: "100px",
                alignItems: "center",
              }}
            >
              <div
                style={{
                  fontFamily: "'Exo 2', sans-serif",
                  fontSize: "18px",
                  color: "var(--black)",
                }}
              >
                Hit the button to see your side tab in action
              </div>
              <BsFillArrowRightSquareFill
                style={{
                  fontSize: "30px",
                  color: "var(--orange200)",
                  marginLeft: "20px",
                }}
              />
            </div>
          )}
        </div>
      </div>

      <div style={{ position: "absolute", bottom: "0", width: "100%" }}>
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "75%" }}
        />
      </div>
    </>
  );
};

export default DrawerMode;
