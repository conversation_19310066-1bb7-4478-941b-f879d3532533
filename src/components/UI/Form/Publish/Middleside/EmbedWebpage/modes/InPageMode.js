import InPageEmbedCode from "../embeds/InPageEmbedCode";

const InPageMode = ({
  form,
  theme,
  selectedPreview,
  isInPageProcess,
  setInPageProcess,
  inPageWidth,
  inPageWidthType,
  inPageHeight,
  inPageHeightType,
}) => {
  return (
    <>
      <div style={{ position: "relative", top: "0", width: "100%" }}>
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "70%" }}
        />
      </div>

      <InPageEmbedCode
        form={form}
        theme={theme}
        selectedPreview={selectedPreview}
        isInPageProcess={isInPageProcess}
        setInPageProcess={setInPageProcess}
        inPageWidth={inPageWidth}
        inPageWidthType={inPageWidthType}
        inPageHeight={inPageHeight}
        inPageHeightType={inPageHeightType}
      />
    </>
  );
};

export default InPageMode;
