import { useState } from "react";
import PopupEmbedCode from "../embeds/PopupEmbedCode";
import "./PopupMode.css";
import { BsArrowLeftSquareFill, BsArrowUpSquareFill } from "react-icons/bs";

const PopupMode = ({
  form,
  theme,
  selectedPreview,
  isPopupProcess,
  setPopupProcess,
  popupButtonText,
  popupButtonBackground,
  popupButtonTextColor,
}) => {
  return (
    <>
      <div style={{ position: "relative", top: "0", width: "100%" }}>
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "75%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "70%" }}
        />
      </div>

      <PopupEmbedCode
        form={form}
        theme={theme}
        selectedPreview={selectedPreview}
        isPopupProcess={isPopupProcess}
        setPopupProcess={setPopupProcess}
        popupButtonText={popupButtonText}
        popupButtonBackground={popupButtonBackground}
        popupButtonTextColor={popupButtonTextColor}
      />
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignContent: "center",
          width: "100%",
          marginTop: "30px",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <BsArrowUpSquareFill
            style={{
              fontSize: "30px",
              color: "var(--orange200)",
              marginBottom: "20px",
            }}
          />
          <div
            style={{
              fontFamily: "'Exo 2', sans-serif",
              fontSize: "18px",
              color: "var(--black)",
            }}
          >
            Hit the button to see your side tab in action
          </div>
        </div>
      </div>

      <div style={{ position: "absolute", bottom: "0", width: "100%" }}>
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "80%" }}
        />
        <div
          className="publishPage-embedWebpage-form-line-bg"
          style={{ width: "75%" }}
        />
      </div>
    </>
  );
};

export default PopupMode;
