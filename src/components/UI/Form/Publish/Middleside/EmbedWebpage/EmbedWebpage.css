.publishPage-embedWebpage-outer-container {
  width: calc(100% - 600px);
}

.publishPage-embedWebpage-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: var(--grey400);
  padding: 10px 15px;
  margin-bottom: 15px;
  width: 100%;
  padding: 10px;
  height: 55px;
  /* box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;*/
  border: 1px solid var(--grey200);
}

.publishPage-embedWebpage {
  display: flex;
  justify-content: space-between;
}

.publishPage-embedWebpage-copyButton {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--white);
  border: 1px solid var(--black);
  border-bottom: 3px solid var(--black);
  border-radius: 30px;
  padding: 2px 12px;
  font-family: "Exo 2", sans-serif;
}

.publishPage-embedWebpage-copyButton:hover {
  margin-top: 2px;
  border: 1px solid var(--black);
  cursor: pointer;
}

.publishPage-embedWebpage-copyButton-icon {
  margin-right: 8px;
  font-size: 14px;
  color: var(--black);
}

.publishPage-embedWebpage-copyButton-title {
  font-size: 14px;
  color: var(--black);
}

.publishPage-embedWebpage-link {
  margin-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.publishPage-embedWebpage-link-input {
  padding: 2px 15px;
  border: 1px solid var(--grey100);
  width: 250px;
  border-radius: 30px;
  background-color: var(--white);
}

.publishPage-embedWebpage-settings {
  display: flex;
  align-items: center;
}

.publishPage-embedWebpage-settings-item {
  margin-right: 20px;
  font-size: 20px;
  cursor: pointer;
}

.publishPage-embedWebpage-settings-preview-item {
  margin-right: 20px;
  font-size: 13px;
  cursor: pointer;
  border: 1px solid var(--black);
  padding: 3px;
  background-color: var(--white);
  color: var(--black);
  border-radius: 5px;
}

.publishPage-embedWebpage-settings-preview-item:hover {
  background-color: var(--grey100);
  color: var(--white);
  border-color: var(--grey100);
}

.publishPage-embedWebpage-settings-preview-item-selected {
  margin-right: 20px;
  font-size: 13px;
  cursor: pointer;
  border: 1px solid var(--black);
  padding: 3px;
  background-color: var(--black);
  color: var(--white);
  border-radius: 5px;
}

.publishPage-embedWebpage-settings-divider {
  border-right: 1px solid var(--grey100);
  height: 30px;
  margin-right: 20px;
}

.publishPage-embedWebpage-form-container {
  position: relative;
  /* display: table; */
  justify-content: center;
  overflow-y: auto;
  width: 100%;
  height: 100%;
  border: 1px solid var(--grey200);
  /*box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;*/
  overflow-x: hidden;
  background-color: var(--white);
}

.publishPage-embedWebpage-form-line-bg {
  background-color: var(--grey400);
  height: 15px;
  margin: 20px 50px;
  border-radius: 3px;
}

.publishPage-embedWebpage-modal-message-container {
  display: flex;
  flex-direction: column;
}

.publishPage-embedWebpage-modal-message {
  margin-bottom: 20px;
  font-family: "Exo 2", sans-serif;
}

.publishPage-embedWebpage-modal-copy-button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.publishPage-embedWebpage-modal-copy-button {
  padding: 3px 10px;
  border: 1px solid var(--black);
  border-bottom: 3px solid var(--black);
  border-radius: 30px;
  background-color: var(--white);
  font-family: "Exo 2", sans-serif;
  cursor: pointer;
}

.publishPage-embedWebpage-modal-copy-button:hover {
  margin-top: 2px;
  border: 1px solid var(--black);
}

.publishPage-embedWebpage-modal-qr {
  display: flex;
  justify-content: center;
  align-items: center;
}

.publisPage-embedWebpage-download-button-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 50px;
}

.publisPage-embedWebpage-download-button {
  background-color: var(--black);
  color: var(--white);
  padding: 5px 16px;
  border-radius: 5px;
  cursor: pointer;
}

.publisPage-embedWebpage-download-button:hover {
  opacity: 0.8;
}

.publishPage-embedWebpage-settings-startEmbedding-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.publishPage-embedWebpage-settings-startEmbedding {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--white);
  color: var(--black);
  border: 1px solid var(--grey100);
  padding: 4px 15px;
  border-radius: 5px;
  cursor: pointer;
}

.publishPage-embedWebpage-settings-startEmbedding-icon {
  margin-right: 8px;
  font-size: 14px;
  color: var(--black);
}

.publishPage-embedWebpage-settings-startEmbedding-title {
  font-size: 14px;
  color: var(--black);
  font-family: "Exo 2", sans-serif;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.publishPage-embedWebpage-settings-startEmbedding:hover {
  opacity: 0.8;
  transition: 0.15s all;
}

.publishPage-embedWebpage-settings-saveButton-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.publishPage-embedWebpage-settings-saveButton {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--white);
  color: var(--black);
  border: 1px solid var(--black);
  border-bottom: 3px solid var(--black);
  padding: 4px 15px;
  border-radius: 30px;
  cursor: pointer;
}

.publishPage-embedWebpage-settings-saveButton-icon {
  margin-right: 8px;
  font-size: 14px;
  color: var(--black);
}

.publishPage-embedWebpage-settings-saveButton-title {
  font-size: 14px;
  color: var(--black);
  font-family: "Exo 2", sans-serif;
}

.publishPage-embedWebpage-settings-saveButton:hover {
  border: 1px solid var(--black);
  margin-top: 2px;
}
