import ShareLink from "./ShareLink/ShareLink";
import "./Middleside.css";
import ShareEmail from "./ShareEmail/ShareEmail";
import EmbedWebpage from "./EmbedWebpage/EmbedWebpage";

const Middleside = ({
  form,
  theme,
  selectedPreview,
  setSelectedPreview,
  selectedLeftsideMenuItem,
  emailSenderName,
  emailSenderEmail,
  emailToList,
  setEmailToList,
  setLoading,
  triggerAction,
  setTriggerAction,
  embedMode,
  isDrawerProcess,
  setDrawerProcess,
  drawerButtonText,
  drawerButtonBackground,
  drawerButtonTextColor,
  drawerPosition,
  isInPageProcess,
  setInPageProcess,
  inPageWidth,
  inPageWidthType,
  inPageHeight,
  inPageHeightType,
  isIFrameProcess,
  setIFrameProcess,
  iFrameWidth,
  iFrameWidthType,
  iFrameHeight,
  iFrameHeightType,
  isPopupProcess,
  setPopupProcess,
  popupButtonText,
  popupButtonBackground,
  popupButtonTextColor,
  isPopoverProcess,
  setPopoverProcess,
  popoverButtonBackground,
  popoverIconColor,
}) => {
  return (
    <>
      {selectedLeftsideMenuItem === "shareLink" && (
        <ShareLink
          form={form}
          theme={theme}
          selectedPreview={selectedPreview}
          setSelectedPreview={setSelectedPreview}
        />
      )}

      {selectedLeftsideMenuItem === "shareEmail" && (
        <ShareEmail
          form={form}
          theme={theme}
          selectedPreview={selectedPreview}
          setSelectedPreview={setSelectedPreview}
          emailSenderName={emailSenderName}
          emailSenderEmail={emailSenderEmail}
          emailToList={emailToList}
          setEmailToList={setEmailToList}
          setLoading={setLoading}
          triggerAction={triggerAction}
          setTriggerAction={setTriggerAction}
        />
      )}
      {selectedLeftsideMenuItem === "embedWebpage" && (
        <EmbedWebpage
          form={form}
          theme={theme}
          selectedPreview={selectedPreview}
          setSelectedPreview={setSelectedPreview}
          embedMode={embedMode}
          isInPageProcess={isInPageProcess}
          setInPageProcess={setInPageProcess}
          inPageWidth={inPageWidth}
          inPageWidthType={inPageWidthType}
          inPageHeight={inPageHeight}
          inPageHeightType={inPageHeightType}
          isDrawerProcess={isDrawerProcess}
          setDrawerProcess={setDrawerProcess}
          drawerButtonText={drawerButtonText}
          drawerButtonBackground={drawerButtonBackground}
          drawerButtonTextColor={drawerButtonTextColor}
          drawerPosition={drawerPosition}
          isIFrameProcess={isIFrameProcess}
          setIFrameProcess={setIFrameProcess}
          iFrameWidth={iFrameWidth}
          iFrameWidthType={iFrameWidthType}
          iFrameHeight={iFrameHeight}
          iFrameHeightType={iFrameHeightType}
          isPopupProcess={isPopupProcess}
          setPopupProcess={setPopupProcess}
          popupButtonText={popupButtonText}
          popupButtonBackground={popupButtonBackground}
          popupButtonTextColor={popupButtonTextColor}
          isPopoverProcess={isPopoverProcess}
          setPopoverProcess={setPopoverProcess}
          popoverButtonBackground={popoverButtonBackground}
          popoverIconColor={popoverIconColor}
        />
      )}
    </>
  );
};

export default Middleside;
