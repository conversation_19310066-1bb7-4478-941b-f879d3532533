.publishPage-shareLink-outer-container {
  width: calc(100% - 600px);
}

.publishPage-shareLink-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--grey400);
  padding: 10px 15px;
  margin-bottom: 15px;
  width: 100%;
  padding: 10px;
  height: 55px;
  /*box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;*/
  border: 1px solid var(--grey200);
}

.publishPage-shareLink {
  display: flex;
  justify-content: space-between;
}

.publishPage-shareLink-copyButton {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--white);
  border: 1px solid var(--grey100);
  border-radius: 5px;
  padding: 2px 12px;
  font-family: "Exo 2", sans-serif;
}

.publishPage-shareLink-copyButton:hover {
  opacity: 0.8;
  cursor: pointer;
}

.publishPage-shareLink-copyButton-icon {
  margin-right: 8px;
  font-size: 14px;
  color: var(--black);
}

.publishPage-shareLink-copyButton-title {
  font-size: 14px;
  color: var(--black);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.publishPage-shareLink-link {
  margin-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.publishPage-shareLink-link-input {
  padding: 2px 15px;
  border: 1px solid var(--grey100);
  width: 400px;
  border-radius: 5px;
  background-color: var(--grey500);
  @media screen and (width < 1380px) {
    width: 100%;
  }
}

.publishPage-shareLink-settings {
  display: flex;
  align-items: center;
}

.publishPage-shareLink-settings-item {
  margin-right: 20px;
  font-size: 20px;
  cursor: pointer;
}

.publishPage-shareLink-settings-preview-item {
  margin-right: 20px;
  font-size: 13px;
  cursor: pointer;
  border: 1px solid var(--black);
  padding: 3px;
  background-color: var(--white);
  color: var(--black);
  border-radius: 5px;
}

.publishPage-shareLink-settings-preview-item:hover {
  background-color: var(--grey100);
  color: var(--white);
  border-color: var(--grey100);
}

.publishPage-shareLink-settings-preview-item-selected {
  margin-right: 20px;
  font-size: 13px;
  cursor: pointer;
  border: 1px solid var(--black);
  padding: 3px;
  background-color: var(--black);
  color: var(--white);
  border-radius: 5px;
}

.publishPage-shareLink-settings-divider {
  border-right: 1px solid var(--grey100);
  height: 30px;
  margin-right: 20px;
}

.publishPage-shareLink-form-container {
  display: table;
  justify-content: center;
  overflow-y: auto;
  width: 100%;
  height: 100%;
  border: 1px solid var(--grey200);
  /*box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;*/
}

.publishPage-shareLink-modal-message-container {
  display: flex;
  flex-direction: column;
}

.publishPage-shareLink-modal-message {
  margin-bottom: 20px;
  font-family: "Exo 2", sans-serif;
}

.publishPage-shareLink-modal-qr {
  display: flex;
  justify-content: center;
  align-items: center;
}

.publisPage-shareLink-download-button-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 50px;
}

.publisPage-shareLink-download-button {
  background-color: var(--green400);
  color: var(--white);
  padding: 5px 16px;
  border-radius: 5px;
  cursor: pointer;
}

.publisPage-shareLink-download-button:hover {
  opacity: 0.8;
}
