import { FaRegCopy } from "react-icons/fa6";
import "./ShareLink.css";
import { MdOutlineDesktopWindows, MdOutlineEmail } from "react-icons/md";
import { Modal, Popover, QRCode } from "antd";
import { AiOutlineQrcode } from "react-icons/ai";
import { HiMiniDevicePhoneMobile } from "react-icons/hi2";
import { FiTablet } from "react-icons/fi";
import { useRef, useState } from "react";

import { message } from "antd";
import FForm from "../../../../../../pages/fform/FForm";

const ShareLink = ({ form, theme, selectedPreview, setSelectedPreview }) => {
  const FORM_APP_URL = process.env.REACT_APP_FORM_APP_URL;

  const formUrl = FORM_APP_URL + "/" + form.id;

  const [openedQrModal, setOpenedQrModal] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();

  const qrRef = useRef();

  const getScreenSize = () => {
    if (selectedPreview === "desktop") {
      return "100%";
    } else if (selectedPreview === "tablet") {
      return "650px";
    } else if (selectedPreview === "mobile") {
      return "420px";
    } else {
      return "100%";
    }
  };

  const copyShareUrl = () => {
    navigator.clipboard.writeText(formUrl);
    messageApi.open({
      type: "success",
      content: "Link copied",
    });
  };

  const downloadQRCode = () => {
    if (qrRef.current) {
      const canvas = qrRef.current.querySelector("canvas");
      if (canvas) {
        const image = canvas.toDataURL("image/png");

        // QR kodunu indirmek için bir bağlantı oluştur
        const link = document.createElement("a");
        link.href = image;
        link.download = "My branded formiqo.png";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  };

  return (
    <>
      <div className="publishPage-shareLink-outer-container">
        {contextHolder}
        <div className="publishPage-shareLink-container">
          <div className="publishPage-shareLink">
            <div
              className="publishPage-shareLink-copyButton"
              onClick={copyShareUrl}
            >
              <div className="publishPage-shareLink-copyButton-icon">
                <FaRegCopy />
              </div>
              <div className="publishPage-shareLink-copyButton-title">
                Copy link
              </div>
            </div>
            <div className="publishPage-shareLink-link">
              <input
                disabled
                className="publishPage-shareLink-link-input"
                value={formUrl}
              ></input>
            </div>
          </div>
          <div className="publishPage-shareLink-settings">
            <Popover
              placement="bottom"
              title="Email Link"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div className="publishPage-shareLink-settings-item">
                <MdOutlineEmail />
              </div>
            </Popover>

            <Popover
              placement="bottom"
              title="QR Code"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
              onClick={() => setOpenedQrModal(true)}
            >
              <div className="publishPage-shareLink-settings-item">
                <AiOutlineQrcode />
              </div>
            </Popover>

            <div className="publishPage-shareLink-settings-divider"></div>
            <Popover
              placement="bottom"
              title="Desktop Preview"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div
                onClick={() => setSelectedPreview("desktop")}
                className={
                  selectedPreview === "desktop"
                    ? "publishPage-shareLink-settings-preview-item-selected"
                    : "publishPage-shareLink-settings-preview-item"
                }
              >
                <MdOutlineDesktopWindows />
              </div>
            </Popover>

            <Popover
              placement="bottom"
              title="Tablet Preview"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div
                onClick={() => setSelectedPreview("tablet")}
                className={
                  selectedPreview === "tablet"
                    ? "publishPage-shareLink-settings-preview-item-selected"
                    : "publishPage-shareLink-settings-preview-item"
                }
              >
                <FiTablet />
              </div>
            </Popover>

            <Popover
              placement="bottom"
              title="Mobile Preview"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div
                onClick={() => setSelectedPreview("mobile")}
                className={
                  selectedPreview === "mobile"
                    ? "publishPage-shareLink-settings-preview-item-selected"
                    : "publishPage-shareLink-settings-preview-item"
                }
              >
                <HiMiniDevicePhoneMobile />
              </div>
            </Popover>
          </div>
        </div>
        <div
          style={{
            width: "100%",
            display: "flex",
            justifyContent: "center",
            height: "calc(100% - 95px)",
            overflowY: "auto",
          }}
        >
          <div
            className="publishPage-shareLink-form-container"
            style={{ width: getScreenSize() }}
          >
            <FForm
              form={form}
              theme={theme}
              isPreview={true}
              isOpen={true}
              perspective={selectedPreview}
            />
          </div>
        </div>
      </div>
      <Modal
        title="Get the QR code"
        open={openedQrModal}
        onCancel={() => setOpenedQrModal(false)}
        footer={() => {
          <div />;
        }}
      >
        <div className="publishPage-shareLink-modal-message-container">
          <div className="publishPage-shareLink-modal-message">
            Scan the code to launch your formiqo. Works online and offline
            (though you’ll need a printer, obviously).
          </div>
          <div className="publishPage-shareLink-modal-qr" ref={qrRef}>
            <QRCode size={200} value="https://www.google.com" />
          </div>
          <div style={{ width: "100%" }}>
            <div className="publisPage-shareLink-download-button-container">
              <div
                className="publisPage-shareLink-download-button"
                onClick={downloadQRCode}
              >
                Download QR Code
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ShareLink;
