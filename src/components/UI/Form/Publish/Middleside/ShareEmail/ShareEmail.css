.publishPage-shareEmail-outer-container {
  width: calc(100% - 600px);
}

.publishPage-shareEmail-container {
  display: flex;
  justify-content: end;
  align-items: center;
  background-color: var(--grey400);
  padding: 10px 15px;
  margin-bottom: 15px;
  width: 100%;
  padding: 10px;
  height: 55px;

  box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;
  border: 1px solid var(--grey100);
}

.publishPage-shareEmail-settings {
  display: flex;
  align-items: center;
}

.publishPage-shareEmail-settings-preview-item {
  margin-right: 20px;
  font-size: 13px;
  cursor: pointer;
  border: 1px solid var(--black);
  padding: 3px;
  background-color: var(--white);
  color: var(--black);
  border-radius: 5px;
}

.publishPage-shareEmail-settings-preview-item:hover {
  background-color: var(--grey100);
  color: var(--white);
  border-color: var(--grey100);
}

.publishPage-shareEmail-settings-preview-item-selected {
  margin-right: 20px;
  font-size: 13px;
  cursor: pointer;
  border: 1px solid var(--black);
  padding: 3px;
  background-color: var(--black);
  color: var(--white);
  border-radius: 5px;
}

.publishPage-shareEmail-email-container {
  display: table;
  justify-content: center;
  overflow-y: auto;
  width: 100%;
  height: 100%;
  border: 1px solid var(--grey200);
  /*box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;*/
  padding: 20px 50px;
  background-color: var(--white);
}

.publishPage-shareEmail-email-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-weight: 600;
  font-family: "Exo 2", sans-serif;
  margin-bottom: 20px;
  width: 100%;
}

.publishPage-shareEmail-email-item-label {
  margin-right: 15px;
  width: 100px;
  margin-bottom: 5px;
}

.publishPage-shareEmail-email-item-input-container {
  width: 100%;
}

.publishPage-shareEmail-email-item-input {
  height: 35px;
  border: 1px solid var(--grey200);
  width: 100%;
  padding: 5px 10px;
  font-weight: 500;
  font-size: 15px;
}

.publishPage-shareEmail-email-item-divider {
  padding: 10px 0;
  border-bottom: 1px solid var(--grey100);
  margin-bottom: 30px;
}

.quill {
  width: 100%;
}

.ql-editor {
  min-height: 300px;
  background-color: var(--white);
}

.ql-toolbar.ql-snow {
  background-color: var(--grey400);
}

.publishPage-shareEmail-email-sendButton-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.publishPage-shareEmail-email-sendButton {
  background-color: var(--green400);
  padding: 8px 20px;
  color: var(--white);
  border-radius: 5px;
  cursor: pointer;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
}

.publishPage-shareEmail-email-sendButton:hover {
  opacity: 0.8;
}

.publishPage-shareEmail-email-sendButton-disabled {
  background-color: var(--green300);
  padding: 8px 20px;
  color: var(--white);
  border-radius: 5px;
  cursor: pointer;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
}

.publishPage-shareEmail-toEmailList {
  border: 1px solid var(--grey200);
  padding: 5px;
  min-height: 35px;
  height: fit-content;
}

.publishPage-shareEmail-toEmail {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--grey500);
  border: 1px solid var(--grey200);
  margin-right: 10px;
  padding: 0 25px 2px 8px;
  border-radius: 30px;
  font-size: 13px;
  font-weight: 500;
  color: var(--black);
  margin-bottom: 8px;
  cursor: default;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.publishPage-shareEmail-toEmail-delete {
  display: none;
  justify-content: center;
  align-items: center;
  padding-left: 10px;
  margin-top: 1px;
  margin-left: 6px;
  margin-right: 2px;
  padding: 2px;
  font-size: 13px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.publishPage-shareEmail-toEmail:hover {
  padding-right: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.publishPage-shareEmail-toEmail:hover .publishPage-shareEmail-toEmail-delete {
  display: flex;
}

.publishPage-shareEmail-toEmail-delete:hover {
  background-color: var(--red100);
  color: var(--white);
  border-radius: 100%;
}
