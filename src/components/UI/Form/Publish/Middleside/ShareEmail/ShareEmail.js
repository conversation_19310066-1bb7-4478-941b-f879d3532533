import { Popover } from "antd";
import "./ShareEmail.css";

import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { useEffect, useState } from "react";
import { IoIosClose, IoMdClose } from "react-icons/io";
import { message } from "antd";
import { sendMailForForm } from "../../../../../../services/http";

const ShareEmail = ({
  form,
  theme,
  selectedPreview,
  setSelectedPreview,
  emailSenderName,
  emailSenderEmail,
  emailToList,
  setEmailToList,
  setLoading,
  triggerAction,
  setTriggerAction,
}) => {
  const [emailSubject, setEmailSubject] = useState("Formiqo Form");
  const [emailContent, setEmailContent] = useState("");

  const [messageApi, contextHolder] = message.useMessage();
  const [errorList, setErrorList] = useState([]);

  const onChangeEmailSubject = (e) => {
    setEmailSubject(e.target.value);
  };

  const onChangeEmailContent = (e) => {
    console.log(e);

    setEmailContent(e);
  };

  useEffect(() => {
    const emailContentDefaultValue = `
    <html>
        <body>
            <div>Hi, </div> 
            <br /> 
            <div>Please click on the link below to complete this form.</div>
            <div><a href="#">https://form.formiqo.com/form-123123145-123</a></div>
            <br /> 
            <div>Thank you!</div>
        </body>
    </html>`;
    setEmailContent(emailContentDefaultValue);
  }, [emailToList]);

  useEffect(() => {}, emailToList);

  const deleteEmailToList = (index) => {
    setTriggerAction(true);
    const tempEmailToList = emailToList;
    tempEmailToList.splice(index, 1);
    setEmailToList(tempEmailToList);

    setTimeout(() => {
      setTriggerAction(false);
      setEmailToList(tempEmailToList);
      messageApi.open({
        type: "success",
        content: "Deleted mail from TO",
      });
    }, 100);
  };

  const checkSendEmailError = () => {
    const tempErrorList = [];
    if (!emailSenderName || emailSenderName.length < 1) {
      tempErrorList.push("Please enter Email Sender name *");
    }
    if (!emailSenderEmail || emailSenderEmail.length < 1) {
      tempErrorList.push("Please enter Email Reply-TO email *");
    }
    if (!emailSubject || emailSubject.length < 0) {
      tempErrorList.push("Please enter Email Subject *");
    }
    if (emailToList.length === 0) {
      tempErrorList.push("Please add email for TO *");
    }
    setErrorList(tempErrorList);
  };

  const sendEmail = () => {
    checkSendEmailError();
    if (errorList.length > 0) {
      return;
    }
    setLoading(true);
    try {
      const body = {
        senderName: emailSenderName,
        senderEmail: emailSenderEmail,
        subject: emailSubject,
        toEmailList: emailToList,
        content: emailContent,
      };
      const response = sendMailForForm(body);
    } catch (err) {
      console.log("sending email for form error : ", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {contextHolder}
      <div className="publishPage-shareEmail-outer-container">
        {/*
        <div className="publishPage-shareEmail-container">
          <div className="publishPage-shareEmail-settings">
            <Popover
              placement="bottom"
              title="Desktop Preview"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div
                onClick={() => setSelectedPreview("desktop")}
                className={
                  selectedPreview === "desktop"
                    ? "publishPage-shareEmail-settings-preview-item-selected"
                    : "publishPage-shareEmail-settings-preview-item"
                }
              >
                <MdOutlineDesktopWindows />
              </div>
            </Popover>

            <Popover
              placement="bottom"
              title="Tablet Preview"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div
                onClick={() => setSelectedPreview("tablet")}
                className={
                  selectedPreview === "tablet"
                    ? "publishPage-shareEmail-settings-preview-item-selected"
                    : "publishPage-shareEmail-settings-preview-item"
                }
              >
                <FiTablet />
              </div>
            </Popover>

            <Popover
              placement="bottom"
              title="Mobile Preview"
              overlayInnerStyle={{
                height: "45px",
                textAlign: "center",
                padding: "10px 0 0 0",
              }}
            >
              <div
                onClick={() => setSelectedPreview("mobile")}
                className={
                  selectedPreview === "mobile"
                    ? "publishPage-shareEmail-settings-preview-item-selected"
                    : "publishPage-shareEmail-settings-preview-item"
                }
              >
                <HiMiniDevicePhoneMobile />
              </div>
            </Popover>
          </div>
        </div>
        */}
        <div
          style={{
            width: "100%",
            display: "flex",
            justifyContent: "center",
            height: "calc(100% - 25px)",
            overflowY: "auto",
          }}
        >
          <div className="publishPage-shareEmail-email-container">
            <div className="publishPage-shareEmail-email-item">
              <div className="publishPage-shareEmail-email-item-label">
                To:{" "}
              </div>
              <div className="publishPage-shareEmail-email-item-input-container">
                <div className="publishPage-shareEmail-toEmailList">
                  <div
                    style={{
                      display: "flex",
                      paddingTop: "2px",
                      alignItems: "center",
                      flexWrap: "wrap",
                    }}
                  >
                    {emailToList.map((email, index) => (
                      <div
                        key={index}
                        className="publishPage-shareEmail-toEmail"
                      >
                        {email}
                        <div
                          className="publishPage-shareEmail-toEmail-delete"
                          onClick={() => deleteEmailToList(index)}
                        >
                          <IoMdClose />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            <div className="publishPage-shareEmail-email-item">
              <div className="publishPage-shareEmail-email-item-label">
                Subject:{" "}
              </div>
              <div className="publishPage-shareEmail-email-item-input-container">
                <input
                  value={emailSubject}
                  className="publishPage-shareEmail-email-item-input"
                  onChange={onChangeEmailSubject}
                />
              </div>
            </div>
            <div className="publishPage-shareEmail-email-item-divider" />
            <div className="publishPage-shareEmail-email-item">
              <ReactQuill
                theme="snow"
                value={emailContent}
                onChange={onChangeEmailContent}
              />
            </div>
            <div className="publishPage-shareEmail-email-sendButton-container">
              <div style={{ display: "flex", flexDirection: "column" }}>
                {errorList?.map((error, index) => (
                  <div
                    key={index}
                    style={{ color: "var(--red100)", fontSize: "15px" }}
                  >
                    {error}
                  </div>
                ))}
              </div>
              <div
                className="publishPage-shareEmail-email-sendButton"
                onClick={sendEmail}
              >
                SEND EMAIL
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ShareEmail;
