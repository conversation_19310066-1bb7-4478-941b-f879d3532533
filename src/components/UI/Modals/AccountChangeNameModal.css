.ant-modal-title {
  font-size: 16px !important;
  font-family: "Exo 2", sans-serif !important;
  border-bottom: 1px solid #f1f1f1;
  padding-bottom: 10px;
}

.accountChangeName-container {
  display: flex;
  flex-direction: column;
}

.accountChangeName-message-container {
  padding-top: 10px;
  display: flex;
  align-items: center;
}

.accountChangeName-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28px;
  .ant-modal-title {
    font-size: 16px !important;
    font-family: "Exo 2", sans-serif !important;
    border-bottom: 1px solid #f1f1f1;
    padding-bottom: 10px;
  }

  .accountChangeName-message-container {
    padding-top: 10px;
    display: flex;
    align-items: center;
  }

  .accountChangeName-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28px;
    color: #000;
    margin-right: 24px;
  }

  .accountChangeName-message {
    font-family: "Exo 2", sans-serif !important;
    font-size: 14px;
    font-weight: 500;
  }
  color: #000;
  margin-right: 10px;
}

.accountChangeName-message {
  font-family: "Exo 2", sans-serif !important;
  font-size: 14px;
  font-weight: 500;
}

.accountChangeName-input-container {
  margin-top: 20px;
}

.accountChangeName-input-label {
  margin-bottom: 5px;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
}

.accountChangeName-input {
  width: 100%;
  padding: 5px 10px;
  border: 1px solid var(--grey200);
  border-radius: 3px;
  font-family: "Exo 2", sans-serif;
}

.accountChangeName-error-container {
  margin-top: 10px;
  height: 20px;
}

.accountChangeName-error {
  color: var(--red100);
  font-family: "Exo 2", sans-serif;
}
