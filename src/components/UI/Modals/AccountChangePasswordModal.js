import { Modal } from "antd";
import "./AccountChangePasswordModal.css";
import { useEffect, useRef, useState } from "react";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import ModalCancelButton from "../Buttons/ModalCancelButton/ModalCancelButton";
import Timer from "../Timer";
import { MdError } from "react-icons/md";

const AccountChangePasswordModal = ({
  open,
  handleClose,
  title,
  sendCode,
  step,
  sendConfirmMail,
  loading,
  accountUser,
  serviceError,
  setServiceError,
  timerStopped,
  setTimerStopped,
  timerRestart,
  setTimerRestart,
}) => {
  const [currentPassword, setCurrentPassword] = useState();
  const [password, setPassword] = useState();
  const [passwordRepeat, setPasswordRepeat] = useState();
  const [error, setError] = useState();

  const [confirmationCodeValues, setConfirmationCodeValues] = useState(
    Array(6).fill("")
  );
  const confirmationInputRef = useRef([]);

  useEffect(() => {
    if (step === 0) {
      setConfirmationCodeValues(Array(6).fill(""));
    }
  }, [step]);

  const onChangeCurrentPassword = (e) => {
    setServiceError(null);
    setError();
    const value = e.target.value;
    setCurrentPassword(value);
  };

  const onChangePassword = (e) => {
    setServiceError(null);
    setError();
    const value = e.target.value;
    setPassword(value);
  };

  const onChangePasswordRepeat = (e) => {
    setServiceError(null);
    setError();
    const value = e.target.value;
    setPasswordRepeat(value);
  };

  const handleChange = (e, index) => {
    setServiceError(null);
    const value = e.target.value;
    if (/^[0-9]$/.test(value)) {
      const newValues = [...confirmationCodeValues];
      newValues[index] = value;
      setConfirmationCodeValues(newValues);

      if (index < 5) {
        confirmationInputRef.current[index + 1].focus();
      }
    } else {
      const newValues = [...confirmationCodeValues];
      newValues[index] = "";
      setConfirmationCodeValues(newValues);
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace" && !confirmationCodeValues[index] && index > 0) {
      confirmationInputRef.current[index - 1].focus();
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const paste = e.clipboardData.getData("text").slice(0, 6);
    const newValues = [...confirmationCodeValues];
    for (let i = 0; i < paste.length; i++) {
      if (/^[0-9]$/.test(paste[i])) {
        newValues[i] = paste[i];
      }
    }
    setConfirmationCodeValues(newValues);
    confirmationInputRef.current[Math.min(paste.length - 1, 5)].focus();
  };

  const validate = () => {
    setServiceError();
    let err = "";
    if (accountUser && !accountUser.social) {
      if (!currentPassword || !password || !passwordRepeat) {
        err = "You must fill in all fields";
        setError(err);
        return;
      }
    } else {
      if (!password || !passwordRepeat) {
        err = "You must fill in all fields";
        setError(err);
        return;
      }
    }

    if (password !== passwordRepeat) {
      err = "Password and password repeat fields are not the same";
      setError(err);
      return;
    }

    if (accountUser && !accountUser.social && currentPassword.length < 8) {
      err = "Your current password must include at least 8 characters.";
      setError(err);
      return;
    }

    if (password.length < 8) {
      err = "Your password must include at least 8 characters.";
      setError(err);
      return;
    }
    return true;
  };

  const applyPasswordChangeRequest = async (isResend) => {
    console.log("ali");
    const isValid = validate();
    console.log("isValid : ", isValid);
    if (!isValid) {
      return;
    }
    await sendCode(currentPassword, password);
    setTimeout(() => {
      setTimerStopped(false);
    }, 2000);
    if (isResend) {
      setTimerStopped(true);
      setTimerRestart(true);
    }
  };

  const maskEmail = () => {
    if (!accountUser) {
      return "";
    }
    const [localPart, domain] = accountUser?.email.split("@");
    const maskedLocalPart = localPart[0] + "********";
    return `${maskedLocalPart}@${domain}`;
  };

  const isActiveConfirmButton = () => {
    if (
      confirmationCodeValues.every((value) => value !== "") &&
      !serviceError
    ) {
      return true;
    }
    return false;
  };

  return (
    <Modal
      width={500}
      title={title}
      open={open}
      onCancel={handleClose}
      maskClosable={false}
      footer={() => {
        <div />;
      }}
    >
      {step === 0 && (
        <>
          <div className="accountChangePassword-container">
            <div
              className="accountChangePassword-input-container"
              style={{ marginBottom: "20px" }}
            >
              {accountUser && !accountUser.social && (
                <>
                  <div className="accountChangePassword-input-label">
                    Your Current Password
                  </div>
                  <input
                    type="password"
                    onChange={onChangeCurrentPassword}
                    value={currentPassword}
                    className="accountChangePassword-input"
                    placeholder="Please enter email address"
                  />
                </>
              )}
            </div>

            <div className="accountChangePassword-input-container">
              <div className="accountChangePassword-input-label">
                New Password
              </div>
              <input
                type="password"
                onChange={onChangePassword}
                value={password}
                className="accountChangePassword-input"
                placeholder="Please enter email address"
              />
            </div>

            <div className="accountChangePassword-input-container">
              <div className="accountChangePassword-input-label">
                New Password Verify
              </div>
              <input
                type="password"
                onChange={onChangePasswordRepeat}
                value={passwordRepeat}
                className="accountChangePassword-input"
                placeholder="Please enter email address"
              />
            </div>

            <div
              className="accountChangePassword-error-container"
              style={{ height: "20px" }}
            >
              {error && error.length > 0 && (
                <div className="accountChangePassword-error">{error}</div>
              )}
              {serviceError && (
                <div className="accountChangePassword-sended-code-serviceError">
                  <MdError style={{ marginRight: "5px", fontSize: "16px" }} />
                  {serviceError}
                </div>
              )}
            </div>
          </div>

          <div
            style={{
              display: "flex",
              justifyContent: "flex-end",
              marginTop: "30px",
            }}
          >
            <div style={{ marginRight: "12px" }}>
              <ModalCancelButton title="Cancel" action={handleClose} />
            </div>
            <div>
              <ModalOkButton
                title="Apply"
                passive={error}
                action={applyPasswordChangeRequest}
                loading={loading}
                loadingTitle="Updating name ..."
              />
            </div>
          </div>
        </>
      )}

      {step === 1 && (
        <>
          <div className="accountChangePassword-sended-container">
            <div className="accountChangePassword-sended-header">
              Confirm it’s you to change your password
            </div>
            <div className="accountChangePassword-sended-explanation">
              Enter the six-digit code we sent to{" "}
              <span style={{ fontWeight: "600", letterSpacing: "1px" }}>
                {maskEmail()}
              </span>
            </div>
            {console.log(
              "confirmationCodeValues : ",
              confirmationCodeValues.length
            )}
            <div className="accountChangePassword-sended-code-container">
              <div className="accountChangePassword-sended-code-header">
                Confirmation Code
              </div>
              <div
                className="accountChangePassword-sended-input-container"
                onPaste={handlePaste}
                style={{ display: "flex", gap: "3px" }}
              >
                {confirmationCodeValues.map((value, index) => (
                  <input
                    key={index}
                    type="text"
                    maxLength="1"
                    value={value}
                    onChange={(e) => handleChange(e, index)}
                    onKeyDown={(e) => handleKeyDown(e, index)}
                    ref={(el) => (confirmationInputRef.current[index] = el)}
                    className="accountChangePassword-sended-input"
                  />
                ))}
              </div>
              {serviceError && (
                <div className="accountChangePassword-sended-code-serviceError">
                  <MdError style={{ marginRight: "5px", fontSize: "16px" }} />
                  {serviceError}
                </div>
              )}
              <div
                onClick={
                  isActiveConfirmButton()
                    ? () =>
                        sendConfirmMail(
                          confirmationCodeValues.join(""),
                          accountUser?.email,
                          "changePassword"
                        )
                    : () => {}
                }
                className={
                  isActiveConfirmButton()
                    ? "accountChangePassword-sended-code-confirmButton"
                    : "accountChangePassword-sended-code-confirmButton-passive"
                }
              >
                Confirm
              </div>

              <div className="accountChangePassword-sended-code-resend-container">
                <div className="accountChangePassword-sended-code-resend-timer">
                  <Timer
                    startTime={10}
                    setStopped={setTimerStopped}
                    restart={timerRestart}
                    setRestart={setTimerRestart}
                  />
                </div>
                <div
                  onClick={() =>
                    timerStopped ? applyPasswordChangeRequest(true) : {}
                  }
                  className="accountChangePassword-sended-code-resend-button"
                  style={{
                    color: !timerStopped ? "#717171" : "",
                    cursor: !timerStopped ? "not-allowed" : "",
                  }}
                >
                  Resend email
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </Modal>
  );
};

export default AccountChangePasswordModal;
