.ant-modal-title {
  font-size: 16px !important;
  font-family: "Exo 2", sans-serif !important;
  border-bottom: 1px solid #f1f1f1;
  padding-bottom: 10px;
}

.errorModalWithOneSelection-message-container {
  padding-top: 10px;
  display: flex;
  align-items: flex-start;
}

.errorModalWithOneSelection-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28px;
  color: #000;
  margin-right: 10px;
}

.errorModalWithOneSelection-message {
  font-family: "Exo 2", sans-serif !important;
  font-size: 14px;
  font-weight: 400;
}

.errorModalWithOneSelection-message2 {
  font-family: "Exo 2", sans-serif !important;
  font-size: 14px;
  font-weight: 500;
  color: red;
  padding: 10px 35px;
}

.errorModalWithOneSelection-selection-container {
  margin-top: 20px;
  margin-bottom: -10px;
  display: flex;
  justify-content: flex-end;
  padding-top: 15px;
}

.errorModalWithOneSelection-button-container {
  display: flex;
  justify-content: center;
  align-content: center;
  padding: 5px 10px;
  border: 1px solid var(--black);
  border-bottom: 5px solid var(--black);
  border-radius: 30px;
  cursor: pointer;
}

.errorModalWithOneSelection-button-container:hover {
  margin-top: 4px;
  border: 1px solid var(--black);
  transition: 0.15s all;
}

.errorModalWithOneSelection-cancel-button-container {
  display: flex;
  justify-content: center;
  align-content: center;
  padding: 5px 10px;
  background-color: var(--grey300);
  border: 1px solid var(--black);
  border-bottom: 5px solid var(--black);
  border-radius: 30px;
  cursor: pointer;
  margin-right: 10px;
}

.errorModalWithOneSelection-cancel-button-container:hover {
  margin-top: 4px;
  border: 1px solid var(--black);
}
