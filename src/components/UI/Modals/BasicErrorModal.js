import { Modal } from "antd";
import "./BasicErrorModal.css";
import { MdError } from "react-icons/md";

const BasicErrorModal = ({ open, handleClose, title, message }) => {
  return (
    <Modal
      title={title}
      open={open}
      onCancel={handleClose}
      footer={() => {
        <div />;
      }}
    >
      <div className="basicErrorModal-message-container">
        <div className="basicErrorModal-icon">
          <MdError />
        </div>
        <div className="basicErrorModal-message">{message}</div>
      </div>
    </Modal>
  );
};

export default BasicErrorModal;
