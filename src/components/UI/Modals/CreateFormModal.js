import { Modal } from "antd";
import "./CreateFormModal.css";
import { useState } from "react";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";

const CreateFormModal = ({
  open,
  handleClose,
  title,
  buttonAction,
  loading,
}) => {
  const [value, setValue] = useState();

  const onChangeValue = (e) => {
    setValue(e.target.value);
  };

  const createForm = () => {
    buttonAction(value);
  };

  return (
    <Modal
      title={title}
      open={open}
      onCancel={handleClose}
      footer={() => {
        <div />;
      }}
    >
      <div className="createFormModal-container">
        {/*
        <div className="createFormModal-message-container">
          <div className="createFormModal-icon">
            <MdInfo />
          </div>
          <div className="createFormModal-message">{message}</div>
        </div>
        */}
        <div className="createFormModal-input-container">
          <input
            onChange={onChangeValue}
            value={value}
            className="createWorkspaceModal-input"
            placeholder="Please enter name your form"
          />
        </div>
      </div>
      <div className="errorModalWithOneSelection-selection-container">
        <ModalOkButton
          title="Create a new form"
          passive={(value && value.length) < 3 || value === undefined}
          action={createForm}
          loading={loading}
          loadingTitle="Creating..."
        />
      </div>
    </Modal>
  );
};

export default CreateFormModal;
