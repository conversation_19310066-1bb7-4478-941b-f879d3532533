.previewModal-content {
    /*display: flex;*/
    display: table;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    height: 100%;
    overflow-y: auto;
}

.previewModal { 
    .ant-modal, .ant-modal-content {
        height: 100vh;
        width: 100vw;
        margin: 0;
        top: 0;
        border-radius: 0 !important;
        padding: 0 !important;
    }
}

.previewModal { 
    .ant-modal-body {
        /*height: calc(100vh - 110px);*/
        height: 100vh;
        overflow-y: auto;
    }
}

.previewModal-bottom-formiqo {
    display: flex;
    align-items: center;
    position: fixed;
    bottom: 5px;
    right: 15px;
    padding: 3px 15px;
    background-color: var(--white);
    border: 1px solid var(--black);
    border-bottom: 5px solid var(--black);
    border-radius: 30px;
    cursor: pointer;
}

.previewModal-bottom-formiqo-logo {
    width: 26px;
    height: 26px;
    margin-right: 10px;
}

.previewModal-bottom-formiqo-text {
    font-family: 'Exo 2', sans-serif;
    font-weight: 600;
    font-size: 15px;
}


.ant-modal-root .ant-modal-wrap {
    overflow: hidden !important;
}
