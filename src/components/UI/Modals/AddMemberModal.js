import { Modal } from "antd";
import "./AddMemberModal.css";
import { useState } from "react";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import BasicError from "../Error/BasicError";

const AddMemberModal = ({
  open,
  handleClose,
  title,
  buttonAction,
  loading,
  error,
  setError,
}) => {
  const [firstName, setFirstName] = useState();
  const [lastName, setLastName] = useState();
  const [email, setEmail] = useState();

  const onChangeFirstName = (e) => {
    setFirstName(e.target.value);
  };

  const onChangeLastName = (e) => {
    setLastName(e.target.value);
  };

  const onChangeEmail = (e) => {
    setEmail(e.target.value);
  };

  const close = () => {
    setError("");
    handleClose();
    setFirstName("");
    setLastName("");
    setEmail("");
  };

  const isValid = () => {
    if (
      firstName &&
      firstName.length > 3 &&
      lastName &&
      lastName.length > 3 &&
      email &&
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
        email
      )
    ) {
      return true;
    }
    return false;
  };

  const createMember = () => {
    const member = { firstName: firstName, lastName: lastName, email: email };
    buttonAction(member);
    setFirstName("");
    setLastName("");
    setEmail("");
  };

  return (
    <Modal
      title={title}
      open={open}
      onCancel={close}
      footer={() => {
        <div />;
      }}
    >
      <div className="addMemberModal-container">
        {error && error.length > 0 && <BasicError errorMessage={error} />}
        <div className="addMemberModal-input-container">
          <div className="addMemberModal-input-label">First name :</div>
          <input
            onChange={onChangeFirstName}
            value={firstName}
            className="addMemberModal-input"
            placeholder="Please enter your member first name"
          />
        </div>
        <div className="addMemberModal-input-container">
          <div className="addMemberModal-input-label">Last name :</div>
          <input
            onChange={onChangeLastName}
            value={lastName}
            className="addMemberModal-input"
            placeholder="Please enter your member last name"
          />
        </div>
        <div className="addMemberModal-input-container">
          <div className="addMemberModal-input-label">Email :</div>
          <input
            onChange={onChangeEmail}
            value={email}
            className="addMemberModal-input"
            placeholder="Please enter your member email address"
          />
        </div>
      </div>
      <div className="errorModalWithOneSelection-selection-container">
        <ModalOkButton
          title="Create a new member"
          passive={!isValid()}
          action={createMember}
          loading={loading}
          loadingTitle="Creating new member..."
        />
      </div>
    </Modal>
  );
};

export default AddMemberModal;
