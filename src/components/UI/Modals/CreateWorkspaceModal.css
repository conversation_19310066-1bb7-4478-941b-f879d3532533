.createWorkspaceModal-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  padding: 16px 0 0 0;
}

.createWorkspaceModal-input {
  height: 35px;
  border: 1px solid var(--grey100);
  width: 100%;
  border-radius: 5px;
  padding-left: 10px;
}

.createWorkspaceModal-input:hover {
  border-color: var(--grey100);
}

.createWorkspaceModal-input:focus {
  border-width: 1px !important;
  border-color: var(--grey200) !important;
}

.createWorkspaceModal-input:active {
  border: 1px solid red !important;
  border-width: 1px !important;
}

.createWorkspaceModal-button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 32px;
}

.ant-modal-title {
  font-family: "Exo 2", sans-serif;
  font-weight: 500 !important;
  color: rgba(0, 0, 0, 1) !important;
  opacity: 1 !important;
}

.ant-input-outlined {
  height: 40px;
  border-width: 1px;
  border-style: solid;
  /*border-color: var(--input-border) !important;*/
  font-family: "Exo 2", sans-serif;
  font-size: 15px;
  font-weight: 500;
  color: var(--black);
}

::placeholder {
  color: var(--grey100) !important;
  opacity: 1 !important; /* Firefox */
}

::-ms-input-placeholder {
  color: var(--grey100) !important;
  opacity: 1;
}

::-ms-input-placeholder {
  color: var(--grey100) !important;
}

.ant-input {
  border: 1px solid var(--grey100);
}

.ant-input:placeholder-shown {
  /*border-radius: 30px !important; */
}

.ant-input-outlined {
  /*border-radius: 30px !important;*/
}

.ant-input-outlined:focus-within {
  /*border-radius: 30px;*/
}
