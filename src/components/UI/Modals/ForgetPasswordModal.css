.forgetPassword-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  padding: 16px 0 0 0;
}

.forgetPassword-label {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 15px;
  margin-bottom: 5px;
}

.forgetPassword-input {
  height: 35px;
  border: 1px solid var(--grey100);
  width: 100%;
  border-radius: 3px;
  padding-left: 10px;
}

.forgetPassword-error-title {
  color: var(--red200);
}

.forgetPassword-input:hover {
  border-color: var(--grey100);
}

.forgetPassword-input:focus {
  border-width: 1px !important;
  border-color: var(--grey200) !important;
}

.forgetPassword-input:active {
  border: 1px solid red !important;
  border-width: 1px !important;
}

.forgetPassword-button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 32px;
}

.forgetPassword-sendedMail-container {
  padding: 20px 25px;
}

.forgetPassword-sendedMail-header {
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  margin: 10px 0 40px 0;
}

.forgetPassword-sendedMail-message {
  margin-bottom: 25px;
  font-size: 15px;
}

.forgetPassword-sendedMail-button {
  text-align: center;
  margin: 15px 0;
  background-color: var(--black);
  color: var(--white);
  padding: 10px;
  border-radius: 5px;
  font-size: 18px;
  font-weight: 500;
  width: 340px;
  cursor: pointer;
}

.forgetPassword-sendedMail-button:hover {
  opacity: 0.8;
  transition: 0.3s all;
}

a {
  color: var(--black) !important;
  text-decoration: underline;
  font-weight: 500;
}

.ant-modal-title {
  font-family: "Exo 2", sans-serif;
  font-weight: 500 !important;
  color: rgba(0, 0, 0, 1) !important;
  opacity: 1 !important;
}

.ant-input-outlined {
  height: 40px;
  border-width: 1px;
  border-style: solid;
  /*border-color: var(--input-border) !important;*/
  font-family: "Exo 2", sans-serif;
  font-size: 15px;
  font-weight: 500;
  color: var(--black);
}

::placeholder {
  color: var(--grey100) !important;
  opacity: 1 !important;
  /* Firefox */
}

::-ms-input-placeholder {
  color: var(--grey100) !important;
  opacity: 1;
}

::-ms-input-placeholder {
  color: var(--grey100) !important;
}

.ant-input {
  border: 1px solid var(--grey100);
}

.ant-input:placeholder-shown {
  /*border-radius: 30px !important; */
}

.ant-input-outlined {
  /*border-radius: 30px !important;*/
}

.ant-input-outlined:focus-within {
  /*border-radius: 30px;*/
}
