import { Modal } from "antd";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { TbGripVertical } from "react-icons/tb";
import { IoWarningOutline } from "react-icons/io5";
import { AiOutlinePlus } from "react-icons/ai";
import ModalCancelButton from "../Buttons/ModalCancelButton/ModalCancelButton";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import "./OptionSettingsModal.css";

const OptionSettingsModal = ({
  field,
  open,
  onOk,
  onCancel,
  loading,
  onChangeOption,
  handleDragAndDrop,
  errorMessage,
  addOption,
  deleteOption,
}) => {
  return (
    <Modal
      width={450}
      title="Select Menu Options"
      open={open}
      onOk={onOk}
      onCancel={onCancel}
      footer={() => {
        <div />;
      }}
    >
      <>
        <div style={{ marginTop: "20px" }}>
          <DragDropContext onDragEnd={handleDragAndDrop}>
            <Droppable droppableId="ROOT" type="group">
              {(provided) => (
                <div {...provided.droppableProps} ref={provided.innerRef}>
                  {field.options?.map((option, index) => (
                    <Draggable
                      draggableId={"optionDraggable " + option.id}
                      key={option.id}
                      index={index}
                    >
                      {(provided) => (
                        <div
                          {...provided.dragHandleProps}
                          {...provided.draggableProps}
                          ref={provided.innerRef}
                        >
                          <div className="optionSettingsModal-outer-container">
                            <div>
                              <div
                                className="optionSettingsModal-container"
                                key={index}
                              >
                                <input
                                  defaultValue={option.value}
                                  className="optionSettingsModal-optionInput"
                                  onChange={(e) => onChangeOption(e, option)}
                                />
                                <div
                                  className="optionSettingsModal-deleteOption"
                                  onClick={() => deleteOption(option)}
                                >
                                  Delete
                                </div>
                                {provided.placeholder}
                              </div>
                            </div>
                            <div className="optionSettingsModal-holder-container">
                              <TbGripVertical />
                            </div>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </div>
        {errorMessage && errorMessage.length > 0 && (
          <div className="optionSettingsModal-error">
            <IoWarningOutline
              style={{ marginRight: "5px", fontSize: "18px" }}
            />
            {errorMessage}
          </div>
        )}
        <div className="optionSettingsModal-addOption">
          <div className="optionSettingsModal-addOption-icon">
            <AiOutlinePlus />
          </div>

          <div
            className="optionSettingsModal-addOption-title"
            onClick={addOption}
          >
            Add Option
          </div>
        </div>

        <div className="optionSettingsModal-buttonContainer">
          <div style={{ marginRight: "12px" }}>
            <ModalCancelButton title="Cancel" action={onCancel} />
          </div>
          <div>
            <ModalOkButton
              title="Save Changes"
              action={onOk}
              loading={loading}
              loadingTitle="Saving options"
            />
          </div>
        </div>
      </>
    </Modal>
  );
};

export default OptionSettingsModal;
