.ant-modal-title {
  font-size: 16px !important;
  font-family: "Exo 2", sans-serif !important;
  border-bottom: 1px solid #f1f1f1;
  padding-bottom: 10px;
}

.accountChangePassword-container {
  display: flex;
  flex-direction: column;
}

.accountChangePassword-message-container {
  padding-top: 10px;
  display: flex;
  align-items: center;
}

.accountChangePassword-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28px;
  .ant-modal-title {
    font-size: 16px !important;
    font-family: "Exo 2", sans-serif !important;
    border-bottom: 1px solid #f1f1f1;
    padding-bottom: 10px;
  }

  .accountChangePassword-message-container {
    padding-top: 10px;
    display: flex;
    align-items: center;
  }

  .accountChangePassword-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28px;
    color: #000;
    margin-right: 24px;
  }

  .accountChangePassword-message {
    font-family: "Exo 2", sans-serif !important;
    font-size: 14px;
    font-weight: 500;
  }
  color: #000;
  margin-right: 10px;
}

.accountChangePassword-message {
  font-family: "Exo 2", sans-serif !important;
  font-size: 14px;
  font-weight: 500;
}

.accountChangePassword-input-container {
  margin-top: 20px;
}

.accountChangePassword-input-label {
  margin-bottom: 5px;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
}

.accountChangePassword-input {
  width: 100%;
  padding: 5px 10px;
  border: 1px solid var(--grey200);
  border-radius: 3px;
  font-family: "Exo 2", sans-serif;
}

.accountChangePassword-error-container {
  margin-top: 10px;
  height: 20px;
}

.accountChangePassword-error {
  color: var(--red100);
  font-family: "Exo 2", sans-serif;
}

.accountChangePassword-sended-container {
  padding: 10px 30px;
}

.accountChangePassword-sended-header {
  font-family: "Exo 2", sans-serif;
  font-weight: 600;
  font-size: 18px;
  margin: 30px 0 20px 0;
  text-align: center;
}

.accountChangePassword-sended-explanation {
  font-family: "Exo 2", sans-serif;
  font-weight: 400;
  font-size: 14px;
  text-align: center;
}

.accountChangePassword-sended-code-container {
  margin-top: 20px;
}

.accountChangePassword-sended-code-header {
  font-family: "Exo 2", sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}

.accountChangePassword-sended-code-confirmButton {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 10px;
  background-color: var(--black);
  color: var(--white);
  font-family: "Exo 2", sans-serif;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.7px;
  border-radius: 5px;
  cursor: pointer;
}

.accountChangePassword-sended-code-confirmButton-passive {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 10px;
  background-color: #919191;
  color: var(--white);
  font-family: "Exo 2", sans-serif;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.7px;
  border-radius: 5px;
  cursor: not-allowe;
}

.accountChangePassword-sended-code-confirmButton:hover {
  opacity: 0.8;
  transition: 0.3s all;
}

.accountChangePassword-sended-input-container {
  display: flex;

  align-items: center;
}

.accountChangePassword-sended-input {
  width: 50px;
  height: 50px;
  border: 1px solid var(--grey100);
  margin-right: 15px;
  margin-bottom: 20px;
  text-align: center;
  font-size: 28px;
  border-radius: 5px;
}

.accountChangePassword-sended-code-resend-container {
  display: flex;
  justify-content: space-between;
  margin-top: 25px;
  margin-bottom: 10px;
}

.accountChangePassword-sended-code-resend-timer {
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 16px;
}

.accountChangePassword-sended-code-resend-timer-icon {
  margin-right: 5px;
  font-size: 18px;
  color: var(--green400);
}

.accountChangePassword-sended-code-resend-button {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: var(--black);
  border-radius: 5px;
  cursor: pointer;
}

.accountChangePassword-sended-code-serviceError {
  display: flex;
  align-items: center;
  background-color: var(--red100);
  border-radius: 3px;
  padding: 5px 10px;
  color: var(--white);
  margin-bottom: 10px;
  font-family: "Exo 2", sans-serif;
  font-weight: 400;
  font-size: 13px;
  letter-spacing: 0.5px;
  color: var(--white);
}

.accountChangePassword-sended-code-resend-button:hover {
  opacity: 0.8;
  transition: 0.3s all;
}
