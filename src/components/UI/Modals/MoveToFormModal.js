import { Modal } from "antd";
import "./MoveToFormModal.css";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import ModalCancelButton from "../Buttons/ModalCancelButton/ModalCancelButton";
import { useEffect, useState } from "react";
import BasicError from "../Error/BasicError";

const MoveToFormModal = ({
  open,
  handleOk,
  handleClose,
  title,
  inputValue,
  buttonTitle,
  loading,
  error,
  setError,
  options,
}) => {
  const [value, setValue] = useState(
    options.filter((option) => option.id !== inputValue.id)[0]
  );

  const selectOptions = options
    .filter((o) => o.id !== inputValue.value)
    .map((option) => ({
      label: option.name,
      value: option.id,
    }));

  const onChangeValue = (e) => {
    e.preventDefault();
    setError(null);
    setValue(e.target.value);
  };

  const close = () => {
    setError("");
    setValue("");
    handleClose();
  };

  const move = () => {
    if (value.value) {
      handleOk(value.value);
    } else {
      handleOk(value);
    }
  };

  useEffect(() => {
    if (inputValue) {
      setValue(
        selectOptions.filter((option) => option.value !== inputValue.id)[0]
      );
    }
  }, [inputValue, setValue, selectOptions]);

  return (
    <Modal
      width={400}
      title={title}
      open={open}
      centered
      onCancel={() => close()}
      onOk={() => move()}
      footer={() => {
        <div />;
      }}
    >
      <div className="movetoFormModal-container">
        {error && error.length > 0 && <BasicError errorMessage={error} />}
        <div style={{ marginBottom: "10px" }}>
          Move from{" "}
          <strong>
            <u>{inputValue.label}</u>
          </strong>{" "}
          to{" "}
        </div>
        <select
          value={value ? value.value : ""}
          onChange={onChangeValue}
          style={{
            border: "1px solid var(--grey200)",
            height: "35px",
            paddingLeft: "10px",
          }}
        >
          {selectOptions.map((option) => (
            <option value={option.value} key={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        <div className="movetoFormModal-button-container">
          <div style={{ marginRight: "12px" }}>
            <ModalCancelButton title="Cancel" action={close} />
          </div>
          <div>
            <ModalOkButton
              title={buttonTitle}
              passive={false}
              action={move}
              loading={loading}
              loadingTitle="Moving..."
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default MoveToFormModal;
