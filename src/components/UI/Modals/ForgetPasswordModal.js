import { Modal } from "antd";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import ModalCancelButton from "../Buttons/ModalCancelButton/ModalCancelButton";
import { useState } from "react";
import "./ForgetPasswordModal.css";

const ForgetPasswordModal = ({
  open,
  handleClose,
  handleOk,
  loading,
  errorCode,
  isSendedForgetPasswordMail,
  setSendedForgetPasswordMail,
}) => {
  const [mail, setMail] = useState();
  const [error, setError] = useState();

  const sendMail = async () => {
    await handleOk(mail);
    setSendedForgetPasswordMail(true);
  };

  const onChangeValue = (e) => {
    const value = e.target.value;
    setMail(value);
    if (
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
        value
      )
    ) {
      setError(null);
    } else {
      setError("Enter a valid e-email address");
    }
  };
  return (
    <Modal
      width={450}
      title="Forgot Password?"
      open={open}
      onCancel={handleClose}
      footer={() => {
        <div />;
      }}
    >
      {!isSendedForgetPasswordMail ? (
        <>
          <div className="forgetPassword-container">
            <div className="forgetPassword-label">Enter your email address</div>
            <input
              placeholder="Enter email address"
              onChange={onChangeValue}
              value={mail}
              className="forgetPassword-input"
            />
            <div className="forgetPassword-error-container">
              <div className="forgetPassword-error-title">{error}</div>
            </div>
          </div>
          <div className="forgetPassword-button-container">
            <div style={{ marginRight: "12px" }}>
              <ModalCancelButton title="Cancel" action={handleClose} />
            </div>
            <div>
              <ModalOkButton
                title="Send Reset Mail"
                passive={!mail || mail.length < 3 || error}
                action={sendMail}
                loading={loading}
                loadingTitle="Sending..."
              />
            </div>
          </div>
        </>
      ) : (
        <>
          <div className="forgetPassword-sendedMail-container">
            {!errorCode && (
              <div className="forgetPassword-sendedMail-header">
                Check your email
              </div>
            )}
            {errorCode && errorCode === 404 ? (
              <div className="forgetPassword-sendedMail-message">
                This email address is not registered in our system.
              </div>
            ) : (
              <>
                <div className="forgetPassword-sendedMail-message">
                  Password reset instructions have been sent successfully via
                  email.
                </div>
                <div className="forgetPassword-sendedMail-message">
                  If you don’t get the email, please check your spam folder.
                </div>
                <div className="forgetPassword-sendedMail-message">
                  If you need more help, please{" "}
                  <a href="/contact">contact our support team.</a>
                </div>
              </>
            )}
          </div>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignContent: "center",
            }}
          >
            <div
              className="forgetPassword-sendedMail-button"
              onClick={() => {
                setMail();
                setSendedForgetPasswordMail(false);
                handleClose();
              }}
            >
              OK
            </div>
          </div>
        </>
      )}
    </Modal>
  );
};

export default ForgetPasswordModal;
