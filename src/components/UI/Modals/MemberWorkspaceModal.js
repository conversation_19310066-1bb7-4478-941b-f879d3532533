import { Modal, Switch } from "antd";
import "./MemberWorkspaceModal.css";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import BasicError from "../Error/BasicError";
import ModalCancelButton from "../Buttons/ModalCancelButton/ModalCancelButton";
import { useEffect, useState } from "react";

const MemberWorkspaceModal = ({
  open,
  handleClose,
  handleOk,
  selectedMember,
  setSelectedMember,
  workspaces,
  title,
  message,
  loading,
  error,
  setError,
}) => {
  const [selectedWorkspaces, setSelectedWorkspaces] = useState([]);
  const [originalSelectedMember, setOriginalSelectedMember] = useState();

  useEffect(() => {
    const tempSelectedMember = selectedMember;
    setOriginalSelectedMember(tempSelectedMember);
  }, [open, selectedMember]);

  useEffect(() => {
    const initialSelectedWorkspaces = workspaces.map((workspace) => {
      const memberWorkspace = selectedMember?.workspaces?.find(
        (sw) => sw.id === workspace.id
      );
      return {
        ...workspace,
        visible: memberWorkspace ? true : false,
      };
    });
    setSelectedWorkspaces(initialSelectedWorkspaces);
  }, [workspaces, selectedMember]);

  const close = () => {
    setError("");
    handleClose();
  };

  const onChangeWorkspace = (changedWorkspace) => {
    const updatedWorkspaces = selectedWorkspaces.map((workspace) => {
      if (workspace.id === changedWorkspace.id) {
        return {
          ...workspace,
          visible: !workspace.visible,
        };
      }
      return workspace;
    });

    const updatedMemberWorkspaces = updatedWorkspaces
      .filter((workspace) => workspace.visible)
      .map((workspace) => ({
        id: workspace.id,
        name: workspace.name,
        uniqueId: workspace.uniqueId,
        createdAt: workspace.createdAt,
        updatedAt: workspace.updatedAt,
        formCount: workspace.formCount,
        default: workspace.default,
      }));

    setSelectedWorkspaces(updatedWorkspaces);
    setSelectedMember({
      ...selectedMember,
      workspaces: updatedMemberWorkspaces,
    });
  };

  const applyChanges = async () => {
    const changeWorkspaces = selectedWorkspaces.filter(
      (workspace) => workspace.visible
    );
    handleOk(changeWorkspaces);
    handleClose();
  };

  const areWorkspacesEqual = () => {
    if (
      originalSelectedMember &&
      originalSelectedMember.workspaces &&
      selectedWorkspaces &&
      selectedWorkspaces.length > 0
    ) {
      const memberWorkspaceIds = originalSelectedMember.workspaces.map(
        (workspace) => workspace.id
      );
      const selectedWorkspaceIds = selectedWorkspaces
        .filter((workspace) => workspace.visible)
        .map((workspace) => workspace.id);

      const areArraysEqual =
        selectedWorkspaceIds.length === memberWorkspaceIds.length &&
        memberWorkspaceIds.every(
          (value, index) => value === selectedWorkspaceIds[index]
        );
      return areArraysEqual;
    }
  };

  return (
    <Modal width={420} title={title} open={open} onCancel={close} footer={null}>
      <div className="memberWorkspaceModal-container">
        {error && error.length > 0 && <BasicError errorMessage={error} />}
        <div className="memberWorkspaceModal-input-container">{message}</div>
        <div className="memberWorkspaceModal-workspaceList-container">
          {workspaces &&
            workspaces.length > 0 &&
            workspaces.map((workspace, index) => (
              <div className="memberWorkspaceModal-workspace" key={index}>
                <div className="memberWorkspaceModal-workspace-name">
                  {workspace.name}
                </div>
                <div className="memberWorkspaceModal-workspace-switch">
                  <Switch
                    checked={selectedWorkspaces.some(
                      (sw) => sw.id === workspace.id && sw.visible
                    )}
                    size="small"
                    onChange={() => onChangeWorkspace(workspace)}
                  />
                </div>
              </div>
            ))}
        </div>
      </div>
      <div style={{ display: "flex", justifyContent: "flex-end" }}>
        <div style={{ marginRight: "12px" }}>
          <ModalCancelButton title="Cancel" action={close} />
        </div>
        <div>
          <ModalOkButton
            title="Apply"
            passive={areWorkspacesEqual()}
            action={applyChanges}
            loading={loading}
            loadingTitle="Saving changes ..."
          />
        </div>
      </div>
    </Modal>
  );
};

export default MemberWorkspaceModal;
