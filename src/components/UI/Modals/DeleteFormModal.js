import { Modal } from "antd";
import "./DeleteFormModal.css";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import ModalCancelButton from "../Buttons/ModalCancelButton/ModalCancelButton";
import { useEffect, useState } from "react";
import BasicError from "../Error/BasicError";

const DeleteModal = ({
  open,
  handleOk,
  handleClose,
  title,
  message,
  message2,
  message3,
  inputValue,
  buttonTitle,
  loading,
  error,
  setError,
}) => {
  const [value, setValue] = useState(inputValue);

  const close = () => {
    setError("");
    setValue("");
    handleClose();
  };

  const renameForm = () => {
    // setValue("");
    handleOk(value);
  };

  useEffect(() => {
    if (inputValue) {
      setValue(inputValue);
    }
  }, [inputValue, setValue]);

  return (
    <Modal
      width={500}
      title={title}
      open={open}
      centered
      onCancel={() => close()}
      onOk={() => renameForm()}
      footer={() => {
        <div />;
      }}
    >
      <div className="deleteFormModal-container">
        {error && error.length > 0 && <BasicError errorMessage={error} />}
        <div>{message}</div>
        <div style={{ color: "red", fontWeight: "500" }}>{message2}</div>
        <div style={{ marginTop: "10px" }}>{message3}</div>

        <div className="deleteFormModal-button-container">
          <div style={{ marginRight: "12px" }}>
            <ModalCancelButton title="Cancel" action={close} />
          </div>
          <div>
            <ModalOkButton
              title={buttonTitle}
              action={renameForm}
              loading={loading}
              loadingTitle="Deleting..."
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteModal;
