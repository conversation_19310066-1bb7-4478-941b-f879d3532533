import { Modal } from "antd";
import "./DownloadResponseModal.css";
import ModalCancelButton from "../Buttons/ModalCancelButton/ModalCancelButton";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import BasicError from "../Error/BasicError";
import * as XLSX from "xlsx";
import { useState } from "react";
import { format } from "date-fns";
import JSZip from "jszip";
import jsPDF from "jspdf";
import { saveAs } from "file-saver";
import "jspdf-autotable";
import SmallLoading from "../Loading/SmallLoading";

const DownloadResponseModal = ({
  open,
  handleOk,
  handleClose,
  title,
  buttonTitle,
  allResponses,
  filteredResponses,
  selectedRows,
  isActiveFiltered,
  isActiveSelected,
  loading,
  error,
}) => {
  const [exportData, setExportData] = useState("all");
  const [exportType, setExportType] = useState("pdf");
  const [loadingDownload, setLoadingDownload] = useState(false);

  const onChangeExportData = (e) => {
    const val = e.target.value;
    setExportData(val);
  };

  const onChangeExportType = (e) => {
    const val = e.target.value;
    setExportType(val);
  };

  const exportResponses = () => {
    const data = getResponseData();
    if (exportType === "pdf") {
      exportToPdf(data);
    }
    if (exportType === "csv") {
      exportToCsv(data);
    }
    if (exportType === "xlsx") {
      exportToXlsx(data);
    }
  };

  const getResponseData = () => {
    switch (exportData) {
      case "all":
        return allResponses;
      case "filtered":
        return filteredResponses;
      case "selected":
        const selectedResponses = allResponses.filter((response) =>
          selectedRows.includes(response.id)
        );
        return selectedResponses;
      default:
        return;
    }
  };

  const exportToCsv = (data) => {
    setLoadingDownload(true);

    const googleAuthenticate = data[0].googleUser
      ? `${data[0].googleUser.name}, ${data[0].googleUser.email}`
      : "";

    console.log("data[0] : ", data[0]);
    console.log("data[0].googleUser : ", data[0].googleUser);

    let headerRow;
    if (data[0].googleUser) {
      headerRow = [
        "Created Date",
        "Google Authenticate",
        ...data[0].fieldResponses.map((response) => response.title),
      ];
    } else {
      headerRow = [
        "Created Date",
        ...data[0].fieldResponses.map((response) => response.title),
      ];
    }

    const rows = data.map((entry) => {
      const createdDate = format(
        new Date(entry.createdDate),
        "MMM dd, yyyy HH:MM:SS"
      );

      const responses = entry.fieldResponses.map((response) => {
        if (
          response.type === "FileUploadField" ||
          response.type === "ImageUploadField" ||
          response.type === "VideoUploadField"
        ) {
          return response.files.map((file) => file.originalName).join(", ");
        }
        return response.value.join(", ") || "";
      });

      return [createdDate, googleAuthenticate, ...responses];
    });

    const csvRows = [headerRow, ...rows];

    const csvContent = csvRows
      .map((row) => row.map((cell) => `"${cell}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", "responses.csv");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    setTimeout(() => {
      setLoadingDownload(false);
      handleClose();
    }, 700);
  };

  const exportToXlsx = (data) => {
    setLoadingDownload(true);

    let headerRow;
    if (data[0].googleUser) {
      headerRow = [
        "Created Date",
        "Google Authenticate",
        ...data[0].fieldResponses.map((response) => response.title),
      ];
    } else {
      headerRow = [
        "Created Date",
        ...data[0].fieldResponses.map((response) => response.title),
      ];
    }

    const rows = data.map((entry) => {
      const createdDate = entry.createdDate;
      const responses = entry.fieldResponses.map((response) => {
        if (
          response.type === "FileUploadField" ||
          response.type === "ImageUploadField" ||
          response.type === "VideoUploadField"
        ) {
          return response.files.map((file) => file.originalName).join(", ");
        }
        return response.value.join(", ") || "";
      });
      return [
        format(new Date(createdDate), "MMM dd, yyyy HH:MM"),

        ...responses,
      ];
    });

    const ws = XLSX.utils.aoa_to_sheet([headerRow, ...rows]);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Form Responses");
    XLSX.writeFile(wb, "responses.xlsx");

    setTimeout(() => {
      setLoadingDownload(false);
      handleClose();
    }, 700);
  };

  const exportToPdf = (data) => {
    setLoadingDownload(true);
    const zip = new JSZip();

    if (data[0].googleUser) {
      const googleAuthenticateAlreadyAdded = data[0].fieldResponses.some(
        (item) => item.title === "Google Authenticate"
      );

      if (!googleAuthenticateAlreadyAdded) {
        const googleAuthenticateField = {
          id: 320,
          pageId: null,
          dataIndex: "googleAuthenticate",
          fieldId: "googleAuthenticate",
          title: "Google Authenticate",
          type: "GoogleAuthenticateField",
          value: [data[0].googleUser.name, data[0].googleUser.email],
          showedTable: false,
          files: [],
        };
        data[0].fieldResponses.unshift(googleAuthenticateField);
      }
    }

    data.forEach((entry, index) => {
      const doc = new jsPDF();

      const tableData = entry.fieldResponses.map((response) => {
        let title = response.title;
        let val = response.value.join("\n");

        if (
          response.type === "FileUploadField" ||
          response.type === "ImageUploadField" ||
          response.type === "VideoUploadField"
        ) {
          const fileNames = response.files
            .map((file) => file.originalName)
            .join("\n");
          val = fileNames;
        }

        return [title, val];
      });

      const columns = [
        `Date: ${format(new Date(entry.createdDate), "MMM dd, yyyy HH:MM")}`,
        "",
      ];

      doc.autoTable(columns, tableData, {
        startY: 20,
        theme: "grid",
        headStyles: {
          fillColor: [255, 255, 255],
          textColor: 0,
          fontSize: 12,
          halign: "left",
        },
        bodyStyles: {
          textColor: 0,
          fillColor: [245, 245, 245],
          fontSize: 11,
        },
        alternateRowStyles: {
          fillColor: [255, 255, 255],
        },
        columnStyles: {
          0: { cellWidth: 60 },
          1: { cellWidth: "auto" },
        },
        margin: { top: 30, left: 10, right: 10 },
      });

      zip.file(`form_response_${entry.id}.pdf`, doc.output("blob"));
    });

    zip.generateAsync({ type: "blob" }).then(function (content) {
      saveAs(content, "responses.zip");
    });

    setTimeout(() => {
      setLoadingDownload(false);
      handleClose();
    }, 700);
  };

  return (
    <Modal
      width={400}
      title={title}
      open={open}
      centered
      onCancel={() => handleClose()}
      onOk={() => handleOk()}
      footer={() => {
        <div />;
      }}
    >
      {loadingDownload ? (
        <div className="downloadResponseModal-loading">
          <div style={{ width: "30px" }}>
            <SmallLoading />
          </div>
          <div className="downloadResponseModal-loading-title">
            Preparing files for download...
          </div>
        </div>
      ) : (
        <div className="downloadResponseModal-container">
          {error && error.length > 0 && <BasicError errorMessage={error} />}

          <div className="downloadResponseModal-content-container">
            <div className="downloadResponseModal-responses">
              <div className="form-control" onChange={onChangeExportData}>
                <div style={{ display: "flex" }}>
                  <input
                    value="all"
                    type="radio"
                    name="response"
                    className="radio checked:#000"
                    defaultChecked
                    style={{ marginRight: "15px" }}
                  />

                  <div className="downloadResponseModal-radio-item">
                    <div className="downloadResponseModal-radio-item-title">
                      All Responses{" "}
                      <span
                        style={{
                          fontWeight: "400",
                          fontSize: "13px",
                          opacity: ".7",
                        }}
                      >
                        {"("}
                        {allResponses?.length} responses
                        {")"}
                      </span>
                    </div>
                    <div className="downloadResponseModal-radio-item-description">
                      All of your responses
                    </div>
                  </div>
                </div>

                <div style={{ display: "flex" }}>
                  <input
                    value="filtered"
                    type="radio"
                    name="response"
                    disabled={!isActiveFiltered}
                    className="radio checked:#000"
                    style={{ marginRight: "15px" }}
                  />

                  <div className="downloadResponseModal-radio-item">
                    <div
                      className="downloadResponseModal-radio-item-title"
                      style={{ opacity: !isActiveSelected ? ".5" : "" }}
                    >
                      Filtered responses{" "}
                      <span
                        style={{
                          fontWeight: "400",
                          fontSize: "13px",
                          opacity: ".7",
                        }}
                      >
                        {"("}
                        {filteredResponses?.length} responses
                        {")"}
                      </span>
                    </div>
                    <div
                      className="downloadResponseModal-radio-item-description"
                      style={{ opacity: !isActiveFiltered ? ".5" : "" }}
                    >
                      All the responses visible after appliying filters
                    </div>
                  </div>
                </div>

                <div style={{ display: "flex" }}>
                  <input
                    value="selected"
                    type="radio"
                    name="response"
                    disabled={!isActiveSelected}
                    className="radio checked:#000"
                    style={{ marginRight: "15px" }}
                  />

                  <div
                    className="downloadResponseModal-radio-item"
                    style={{ margin: "0" }}
                  >
                    <div
                      className="downloadResponseModal-radio-item-title"
                      style={{ opacity: !isActiveSelected ? ".5" : "" }}
                    >
                      Selected responses{" "}
                      <span
                        style={{
                          fontWeight: "400",
                          fontSize: "13px",
                          opacity: ".7",
                        }}
                      >
                        {"("}
                        {selectedRows?.length}
                        {")"}
                      </span>
                    </div>
                    <div
                      className="downloadResponseModal-radio-item-description"
                      disabled={!isActiveSelected}
                      style={{ opacity: !isActiveFiltered ? ".5" : "" }}
                    >
                      All responses from the selected rows
                    </div>
                  </div>
                </div>
              </div>
              <div className="downloadResponseModal-divider"></div>

              <div className="form-control" onChange={onChangeExportType}>
                <div style={{ display: "flex" }}>
                  <input
                    value="pdf"
                    type="radio"
                    name="file"
                    className="radio checked:#000"
                    defaultChecked
                    style={{ marginRight: "15px" }}
                  />

                  <div className="downloadResponseModal-radio-item">
                    <div className="downloadResponseModal-radio-item-title">
                      PDF file
                    </div>
                  </div>
                </div>

                <div style={{ display: "flex" }}>
                  <input
                    value="csv"
                    type="radio"
                    name="file"
                    className="radio checked:#000"
                    style={{ marginRight: "15px" }}
                  />

                  <div className="downloadResponseModal-radio-item">
                    <div className="downloadResponseModal-radio-item-title">
                      CSV file
                    </div>
                  </div>
                </div>

                <div style={{ display: "flex" }}>
                  <input
                    value="xlsx"
                    type="radio"
                    name="file"
                    className="radio checked:#000"
                    style={{ marginRight: "15px" }}
                  />

                  <div className="downloadResponseModal-radio-item">
                    <div className="downloadResponseModal-radio-item-title">
                      XLSX file
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="downloadResponseModal-button-container">
            <div style={{ marginRight: "12px" }}>
              <ModalCancelButton title="Cancel" action={handleClose} />
            </div>
            <div>
              <ModalOkButton
                title={buttonTitle}
                action={exportResponses}
                loading={loading}
                loadingTitle="Deleting..."
              />
            </div>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default DownloadResponseModal;
