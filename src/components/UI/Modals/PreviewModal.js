import { Modal } from "antd";
import "./PreviewModal.css";
import { MdOutlineDesktopWindows } from "react-icons/md";
import { FiTablet } from "react-icons/fi";
import { HiMiniDevicePhoneMobile } from "react-icons/hi2";

const PreviewModal = ({
  children,
  open,
  handleClose,
  perspective,
  setPerspective,
}) => {
  const getScreenSize = () => {
    if (perspective === "desktop") {
      return "100%";
    } else if (perspective === "tablet") {
      return "700px";
    } else if (perspective === "mobile") {
      return "430px";
    } else {
      return "100%";
    }
  };

  return (
    <>
      <Modal
        wrapClassName="previewModal"
        open={open}
        onCancel={() => handleClose(false)}
        footer={() => {
          <div />;
        }}
      >
        <div className="fform-perspective-outer-container">
          <div className="fform-perspective-container">
            <div
              className={
                perspective === "desktop"
                  ? "fform-option-preview-icon-selected"
                  : "fform-option-preview-icon"
              }
              style={{ marginRight: "20px" }}
              onClick={() => setPerspective("desktop")}
            >
              <MdOutlineDesktopWindows />
            </div>
            <div
              className={
                perspective === "tablet"
                  ? "fform-option-preview-icon-selected"
                  : "fform-option-preview-icon"
              }
              style={{ marginRight: "20px" }}
              onClick={() => setPerspective("tablet")}
            >
              <FiTablet />
            </div>
            <div
              className={
                perspective === "mobile"
                  ? "fform-option-preview-icon-selected"
                  : "fform-option-preview-icon"
              }
              onClick={() => setPerspective("mobile")}
            >
              <HiMiniDevicePhoneMobile />
            </div>
          </div>
        </div>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            height: "100%",
          }}
        >
          <div
            style={{
              width: "100%",
              display: "flex",
              justifyContent: "center",
              height: "calc(100% - 30px)",
            }}
          >
            <div
              className="previewModal-content"
              style={{ width: getScreenSize() }}
            >
              {children}
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default PreviewModal;
