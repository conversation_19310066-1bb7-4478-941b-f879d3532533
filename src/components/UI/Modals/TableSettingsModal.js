import { Modal, Switch } from "antd";
import "./TableSettingsModal.css";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import ModalCancelButton from "../Buttons/ModalCancelButton/ModalCancelButton";
import { useEffect, useState } from "react";
import BasicError from "../Error/BasicError";
import { getIconByType } from "../../../pages/form/Result/HeaderUtil";
import { TbGripVertical } from "react-icons/tb";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { updateFormHeaders } from "../../../services/http";

const TableSettingsModal = ({
  open,
  handleOk,
  handleClose,
  title,
  headerList,
  setHeaders,
  inputValue,
  buttonTitle,
  loading,
  error,
  setError,
}) => {
  const [tempHeaderList, setTempHeaderList] = useState([]);

  useEffect(() => {
    setTempHeaderList(headerList);
  }, [headerList]);

  const close = () => {
    setError("");
    handleClose();
  };

  const handleDragAndDrop = (results) => {
    const { source, destination, type } = results;

    if (!destination) return;

    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    )
      return;

    if (type === "group") {
      let tempHeaders = [...tempHeaderList];
      const createdDateIndex = tempHeaders.findIndex(
        (header) => header.dataIndex === "createdDate"
      );

      const createdDateHeader = tempHeaders.splice(createdDateIndex, 1)[0];
      const headerSourceIndex = source.index;
      const headerDestinatonIndex = destination.index;

      const [removedHeader] = tempHeaders.splice(headerSourceIndex, 1);
      tempHeaders.splice(headerDestinatonIndex, 0, removedHeader);
      tempHeaders = [createdDateHeader, ...tempHeaders];
      tempHeaders.forEach((element, index) => {
        element.order = index;
      });

      setTempHeaderList(tempHeaders);
    }
  };

  const onChangeHeader = (changedHeader) => {
    const updatedHeaders = tempHeaderList.map((header) => {
      if (header.id === changedHeader.id) {
        return {
          ...header,
          visible: !header.visible,
        };
      }
      return header;
    });

    setTempHeaderList(updatedHeaders);
  };

  const resetTable = async (uploadedHeaderList) => {
    const updatedList = uploadedHeaderList
      .map((header) => ({
        ...header,
        visible: true,
        order: Number(header.key),
      }))
      .sort((a, b) => a.order - b.order);
    setTempHeaderList(updatedList);
    try {
      const response = await updateFormHeaders(updatedList);
      if (response.status === 200) {
        setTempHeaderList(response.data);
        setHeaders(response.data);
      }
    } catch (err) {
      console.log("Updating Headers error : ", err);
    } finally {
    }
  };

  const applyChanges = async () => {
    await handleOk(tempHeaderList);
    handleClose();
  };

  useEffect(() => {
    isHeaderListChanged();
  }, [tempHeaderList, isHeaderListChanged]);

  const isHeaderListChanged = () => {
    if (
      headerList &&
      headerList.length > 0 &&
      tempHeaderList &&
      tempHeaderList.length > 0
    ) {
      for (let i = 0; i < headerList.length; i++) {
        if (
          headerList[i].key !== tempHeaderList[i].key ||
          headerList[i].order !== tempHeaderList[i].order ||
          headerList[i].visible !== tempHeaderList[i].visible
        ) {
          return false;
        }
      }
    }
    return true;
  };

  useEffect(() => {
    isHeaderListOriginal();
  }, [tempHeaderList]);

  const isHeaderListOriginal = () => {
    const value = tempHeaderList.every(
      (header) => header.visible === true && Number(header.key) === header.order
    );

    return value;
  };

  return (
    <Modal
      width={370}
      title={title}
      open={open}
      centered
      onCancel={() => close()}
      onOk={() => handleOk()}
      footer={() => {
        <div />;
      }}
    >
      <div className="tableSettingsModal-container">
        {error && error.length > 0 && <BasicError errorMessage={error} />}
        <div className="tableSettingsModal-header-list">
          <DragDropContext onDragEnd={handleDragAndDrop}>
            <Droppable droppableId="ROOT" type="group">
              {(provided) => (
                <div style={{ position: "relative" }}>
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {tempHeaderList
                      ?.filter((header) => header.dataIndex !== "createdDate")
                      .map((header, index) => (
                        <Draggable
                          draggableId={"optionDraggable " + header.id}
                          key={header.id}
                          index={index}
                        >
                          {(provided) => (
                            <div
                              {...provided.dragHandleProps}
                              {...provided.draggableProps}
                              ref={provided.innerRef}
                            >
                              <div className="tableSettingsModal-header-item">
                                <div className="tableSettingsModal-header-item-inner-container">
                                  <div className="tableSettingsModal-header-item-holder-icon">
                                    <TbGripVertical />
                                  </div>
                                  <div className="tableSettingsModal-header-item-icon">
                                    {" "}
                                    {getIconByType(header.type)}
                                  </div>
                                  <div className="tableSettingsModal-header-item-title">
                                    {header.title}
                                  </div>
                                </div>
                                <div>
                                  <Switch
                                    value={header.visible}
                                    onChange={() => onChangeHeader(header)}
                                    size="small"
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                  </div>
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </div>
        <div className="tableSettingsModal-button-container">
          {!isHeaderListOriginal() ? (
            <div
              className="tableSettingsModal-button-reset"
              onClick={() => resetTable(tempHeaderList)}
            >
              Reset table settings
            </div>
          ) : (
            <div />
          )}
          <div style={{ display: "flex" }}>
            <div style={{ marginRight: "12px" }}>
              <ModalCancelButton title="Cancel" action={close} />
            </div>
            <div>
              <ModalOkButton
                title={buttonTitle}
                passive={isHeaderListChanged()}
                action={applyChanges}
                loading={loading}
                loadingTitle="Deleting..."
              />
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default TableSettingsModal;
