.optionSettingsModal-outer-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.optionSettingsModal-container {
    display: flex;
    align-items: center;   
}

.optionSettingsModal-holder-container {
    visibility: hidden;
    color: var(--grey100);
    padding-right: 20px;
    font-size: 18px;
}

.optionSettingsModal-outer-container:hover .optionSettingsModal-holder-container {
    visibility: visible;
}

.optionSettingsModal-buttonContainer {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
    margin-bottom: -10px;
}

.optionSettingsModal-optionInput {
    border: 1px solid var(--grey200);
    padding: 3px 0 3px 10px;
    width: 250px;
}

.optionSettingsModal-deleteOption {
    color: var(--red100);
    font-size: 12px;
    margin-left: 10px;
    cursor: pointer;
}

.optionSettingsModal-deleteOption:hover {
    opacity: .8;
}

.optionSettingsModal-error {
    display: flex;
    align-items: center;
    font-family: "Exo 2", sans-serif;
    color: var(--red100);
    font-size: 13px;
}

.optionSettingsModal-addOption {
    display: flex;
    align-items: center;
    margin-top: 25px;
    cursor: pointer;
    width: fit-content;
}

.optionSettingsModal-addOption:hover {
    opacity: .8;
}

.optionSettingsModal-addOption-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 5px;
    font-size: 14px;
    color: var(--black);
}

.optionSettingsModal-addOption-title {
    font-size: 14px;
    font-family: 'Exo 2', sans-serif;
    font-weight: 500;
    width: 100%;
}

