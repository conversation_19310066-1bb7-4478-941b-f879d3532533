import { Modal } from "antd";
import "./AccountChangeNameModal.css";
import { useEffect, useState } from "react";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import ModalCancelButton from "../Buttons/ModalCancelButton/ModalCancelButton";

const AccountChangeNameModal = ({
  open,
  handleClose,
  title,
  handleOk,
  loading,
  accountUser,
}) => {
  const [firstName, setFirstName] = useState();
  const [lastName, setLastName] = useState();
  const [error, setError] = useState();

  useEffect(() => {
    if (accountUser) {
      setFirstName(accountUser.firstName);
      setLastName(accountUser.lastName);
    }
  }, [accountUser]);

  const onChangeFirstName = (e) => {
    setError();
    const value = e.target.value;
    setFirstName(value);
  };

  const onChangeLastName = (e) => {
    setError();
    const value = e.target.value;
    setLastName(value);
  };

  const validateInput = () => {
    let err = "";
    if (firstName.length < 3 || firstName.length > 64) {
      err = `First name must be between 3 and 64 characters.`;
      setError(err);
      return false;
    }
    if (!/^[a-zA-ZçÇğĞıİöÖşŞüÜ\s]+$/.test(firstName)) {
      err = `First name can only contain letters and spaces.`;
      setError(err);
      return false;
    }
    if (lastName.length < 3 || lastName.length > 64) {
      err = `Last name must be between 3 and 64 characters.`;
      setError(err);
      return false;
    }
    if (!/^[a-zA-ZçÇğĞıİöÖşŞüÜ\s]+$/.test(lastName)) {
      err = `Last name can only contain letters and spaces.`;
      setError(err);
      return false;
    }
    setError("");
    return true;
  };

  const apply = async () => {
    const isValid = validateInput();
    if (!isValid) {
      console.log("error:", error);
      return;
    }

    const changedUser = { ...accountUser };
    changedUser.firstName = firstName
      .trim()
      .toLowerCase()
      .replace(/\s+/g, " ")
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
    changedUser.lastName = lastName
      .trim()
      .toLowerCase()
      .replace(/\s+/g, " ")
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");

    handleOk(changedUser);
  };

  return (
    <Modal
      width={450}
      title={title}
      open={open}
      onCancel={handleClose}
      footer={() => {
        <div />;
      }}
    >
      <div className="accountChangeName-container">
        {/*
        <div className="accountChangeName-message-container">
          <div className="accountChangeName-icon">
            <MdInfo />
          </div>
          <div className="accountChangeName-message">{message}</div>
        </div>
        */}
        <div className="accountChangeName-input-container">
          <div className="accountChangeName-input-label">Your first name</div>
          <input
            onChange={onChangeFirstName}
            value={firstName}
            className="accountChangeName-input"
            placeholder="Please enter first name"
          />
        </div>

        <div className="accountChangeName-input-container">
          <div className="accountChangeName-input-label">Your last name</div>
          <input
            onChange={onChangeLastName}
            value={lastName}
            className="accountChangeName-input"
            placeholder="Please enter last name"
          />
        </div>
        <div
          className="accountChangeName-error-container"
          style={{ height: "20px" }}
        >
          {error && error.length > 0 && (
            <div className="accountChangeName-error">{error}</div>
          )}
        </div>
      </div>
      <div
        style={{
          display: "flex",
          justifyContent: "flex-end",
          marginTop: "30px",
        }}
      >
        <div style={{ marginRight: "12px" }}>
          <ModalCancelButton title="Cancel" action={handleClose} />
        </div>
        <div>
          <ModalOkButton
            title="Apply"
            passive={error}
            action={apply}
            loading={loading}
            loadingTitle="Updating name ..."
          />
        </div>
      </div>
    </Modal>
  );
};

export default AccountChangeNameModal;
