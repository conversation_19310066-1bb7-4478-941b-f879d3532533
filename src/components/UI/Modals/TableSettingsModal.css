.tableSettingsModal-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  padding: 16px 0 0 0;
}

.tableSettingsModal-input {
  height: 35px;
  border: 1px solid var(--grey100);
  width: 100%;
  border-radius: 30px;
  padding-left: 10px;
}

.tableSettingsModal-input:hover {
  border-color: var(--grey100);
}

.tableSettingsModal-input:focus {
  border-width: 1px !important;
  border-color: var(--grey200) !important;
}

.tableSettingsModal-input:active {
  border: 1px solid red !important;
  border-width: 1px !important;
}

.tableSettingsModal-header-list {
  overflow-y: auto;
}

.tableSettingsModal-header-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  cursor: grab;
}

.tableSettingsModal-header-item-inner-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.tableSettingsModal-header-item-holder-icon {
  font-size: 16px;
  margin-right: 5px;
  color: #616161;
}

.tableSettingsModal-header-item-icon {
  font-size: 16px;
  padding: 3px;
  margin-right: 10px;
  border: 1px solid var(--grey200);
  border-radius: 5px;
}

.tableSettingsModal-header-item-title {
}

.tableSettingsModal-button-container {
  display: flex;
  justify-content: space-between;
  margin-top: 32px;
}

.tableSettingsModal-button-reset {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #416067;
  cursor: pointer;
}

.ant-modal-title {
  font-family: "Exo 2", sans-serif;
  font-weight: 500 !important;
  color: rgba(0, 0, 0, 1) !important;
  opacity: 1 !important;
}

.ant-input-outlined {
  height: 40px;
  border-width: 1px;
  border-style: solid;
  /*border-color: var(--input-border) !important;*/
  font-family: "Exo 2", sans-serif;
  font-size: 15px;
  font-weight: 500;
  color: var(--black);
}

::placeholder {
  color: var(--grey100) !important;
  opacity: 1 !important; /* Firefox */
}

::-ms-input-placeholder {
  color: var(--grey100) !important;
  opacity: 1;
}

::-ms-input-placeholder {
  color: var(--grey100) !important;
}

.ant-input {
  border: 1px solid var(--grey100);
}

.ant-input:placeholder-shown {
  /*border-radius: 30px !important; */
}

.ant-input-outlined {
  /*border-radius: 30px !important;*/
}

.ant-input-outlined:focus-within {
  /*border-radius: 30px;*/
}
