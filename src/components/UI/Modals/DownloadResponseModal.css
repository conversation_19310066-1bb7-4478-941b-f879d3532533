.downloadResponseModal-loading {
  display: flex;
  align-items: center;
  padding: 20px 0;
}

.downloadResponseModal-loading-title {
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
  margin-left: 15px;
}

.downloadResponseModal-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  padding: 16px 0 0 0;
}

.downloadResponseModal-input {
  height: 35px;
  border: 1px solid var(--grey100);
  width: 100%;
  border-radius: 30px;
  padding-left: 10px;
}

.downloadResponseModal-input:hover {
  border-color: var(--grey100);
}

.downloadResponseModal-input:focus {
  border-width: 1px !important;
  border-color: var(--grey200) !important;
}

.downloadResponseModal-input:active {
  border: 1px solid red !important;
  border-width: 1px !important;
}

.downloadResponseModal-button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 32px;
}

.downloadResponseModal-radio-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  margin-bottom: 20px;
}

.downloadResponseModal-radio-item-title {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
}

.downloadResponseModal-radio-item-description {
  font-family: "Exo 2", sans-serif;
  font-size: 12px;
}

.downloadResponseModal-divider {
  border-bottom: 1px solid var(--grey200);

  margin: 25px 0;
}
.ant-modal-title {
  font-family: "Exo 2", sans-serif;
  font-weight: 500 !important;
  color: rgba(0, 0, 0, 1) !important;
  opacity: 1 !important;
}

.ant-input-outlined {
  height: 40px;
  border-width: 1px;
  border-style: solid;
  /*border-color: var(--input-border) !important;*/
  font-family: "Exo 2", sans-serif;
  font-size: 15px;
  font-weight: 500;
  color: var(--black);
}

::placeholder {
  color: var(--grey100) !important;
  opacity: 1 !important; /* Firefox */
}

::-ms-input-placeholder {
  color: var(--grey100) !important;
  opacity: 1;
}

::-ms-input-placeholder {
  color: var(--grey100) !important;
}

.ant-input {
  border: 1px solid var(--grey100);
}

.ant-input:placeholder-shown {
  /*border-radius: 30px !important; */
}

.ant-input-outlined {
  /*border-radius: 30px !important;*/
}

.ant-input-outlined:focus-within {
  /*border-radius: 30px;*/
}
