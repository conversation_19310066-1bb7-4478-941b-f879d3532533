import { Modal } from "antd";
import "./RenameWorkspaceModal.css";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import ModalCancelButton from "../Buttons/ModalCancelButton/ModalCancelButton";
import { useEffect, useState } from "react";
import BasicError from "../Error/BasicError";

const RenameWorkspaceModal = ({
  open,
  handleOk,
  handleClose,
  title,
  inputValue,
  buttonTitle,
  loading,
  error,
  setError,
}) => {
  const [value, setValue] = useState(inputValue);

  const onChangeValue = (e) => {
    setError(null);
    setValue(e.target.value);
  };

  const close = () => {
    setError("");
    setValue("");
    handleClose();
  };

  const rename = () => {
    // setValue("");
    handleOk(value);
  };

  useEffect(() => {
    if (inputValue) {
      setValue(inputValue);
    }
  }, [inputValue, setValue]);

  return (
    <Modal
      width={400}
      title={title}
      open={open}
      centered
      onCancel={() => close()}
      onOk={() => rename()}
      footer={() => {
        <div />;
      }}
    >
      <div className="renameWorkspaceModal-container">
        {error && error.length > 0 && <BasicError errorMessage={error} />}
        <input
          onChange={onChangeValue}
          value={value}
          className="renameWorkspaceModal-input"
        />
        <div className="renameWorkspaceModal-button-container">
          <div style={{ marginRight: "12px" }}>
            <ModalCancelButton title="Cancel" action={close} />
          </div>
          <div>
            <ModalOkButton
              title={buttonTitle}
              passive={(value && value.length) < 3}
              action={rename}
              loading={loading}
              loadingTitle="Renaming..."
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default RenameWorkspaceModal;
