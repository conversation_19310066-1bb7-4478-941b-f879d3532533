import { Modal } from "antd";
import "./CreateWorkspaceModal.css";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import ModalCancelButton from "../Buttons/ModalCancelButton/ModalCancelButton";
import { useState } from "react";
import BasicError from "../Error/BasicError";

const CerateWorkspaceModal = ({
  open,
  handleOk,
  handleClose,
  title,
  loading,
  error,
  setError,
}) => {
  const [value, setValue] = useState("");

  const onChangeValue = (e) => {
    setError(null);
    setValue(e.target.value);
  };

  const close = () => {
    setError("");
    setValue("");
    handleClose();
  };

  const saveWorkspace = () => {
    setValue("");
    handleOk(value);
    handleClose();
  };

  return (
    <Modal
      width={400}
      title={title}
      open={open}
      centered
      onCancel={() => close()}
      onOk={() => saveWorkspace()}
      footer={() => {
        <div />;
      }}
    >
      <div className="createWorkspaceModal-container">
        {error && error.length > 0 && <BasicError errorMessage={error} />}
        <input
          onChange={onChangeValue}
          value={value}
          className="createWorkspaceModal-input"
          placeholder="Name your workspace"
        />
        <div className="createWorkspaceModal-button-container">
          <div style={{ marginRight: "12px" }}>
            <ModalCancelButton title="Cancel" action={close} />
          </div>
          <div>
            <ModalOkButton
              title="Create workspace"
              passive={(value && value.length) < 3}
              action={saveWorkspace}
              loading={loading}
              loadingTitle="Creating..."
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default CerateWorkspaceModal;
