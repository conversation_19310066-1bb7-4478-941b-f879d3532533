import { Modal } from "antd";
import "./CopyToFormModal.css";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import ModalCancelButton from "../Buttons/ModalCancelButton/ModalCancelButton";
import { useState } from "react";
import BasicError from "../Error/BasicError";

const CopyToFormModal = ({
  open,
  handleOk,
  handleClose,
  selectedWorkspace,
  title,
  buttonTitle,
  loading,
  error,
  setError,
  options,
}) => {
  const [value, setValue] = useState(options[0]?.id);

  const onChangeValue = (e) => {
    setValue(e.target.value);
  };

  const close = () => {
    setError("");
    setValue("");
    handleClose();
  };

  const copy = () => {
    handleOk(value);
  };

  return (
    <Modal
      width={400}
      title={title}
      open={open}
      centered
      onCancel={() => close()}
      onOk={() => copy()}
      footer={() => {
        <div />;
      }}
    >
      <div className="copytoFormModal-container">
        {error && error.length > 0 && <BasicError errorMessage={error} />}
        <div style={{ marginBottom: "10px" }}>
          Copy from{" "}
          <strong>
            <u>{selectedWorkspace.name}</u>
          </strong>{" "}
          to{" "}
        </div>
        <select
          value={value}
          onChange={onChangeValue}
          style={{
            border: "1px solid var(--grey200)",
            height: "35px",
            paddingLeft: "10px",
          }}
        >
          {options.map((option, index) => (
            <option value={option.id} key={index}>
              {option.name}
            </option>
          ))}
        </select>

        <div className="copytoFormModal-button-container">
          <div style={{ marginRight: "12px" }}>
            <ModalCancelButton title="Cancel" action={close} />
          </div>
          <div>
            <ModalOkButton
              title={buttonTitle}
              passive={false}
              action={copy}
              loading={loading}
              loadingTitle="Copying..."
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default CopyToFormModal;
