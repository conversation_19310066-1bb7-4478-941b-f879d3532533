import { Modal } from "antd";
import { MdError } from "react-icons/md";

import "./ErrorModalWithOneSelection.css";

const ErrorModalWithSelection = ({
  open,
  handleClose,
  title,
  message,
  message2,
  selectionDescription,
  buttonTitle,
  buttonAction,
}) => {
  return (
    <Modal
      wrapClassName="errorModalWithOneSelection"
      title={title}
      open={open}
      onCancel={handleClose}
      footer={() => {
        <div />;
      }}
    >
      <div className="errorModalWithOneSelection-container">
        <div className="errorModalWithOneSelection-message-container">
          <div className="errorModalWithOneSelection-icon">
            <MdError />
          </div>
          <div className="errorModalWithOneSelection-message">{message}</div>
        </div>
        {message2 && (
          <div className="errorModalWithOneSelection-message2">{message2}</div>
        )}
        <div className="errorModalWithOneSelection-selection-container">
          <div
            className="errorModalWithOneSelection-cancel-button-container"
            onClick={handleClose}
          >
            <div className="errorModalWithOneSelection-cancel-button-title">
              Cancel
            </div>
          </div>
          <div
            className="errorModalWithOneSelection-button-container"
            onClick={buttonAction}
          >
            <div className="errorModalWithOneSelection-button-title">
              {buttonTitle}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ErrorModalWithSelection;
