import { Modal } from "antd";
import "./BasicModal.css";
import { MdInfo } from "react-icons/md";

const BasicModal = ({ open, handleClose, title, message }) => {
  return (
    <Modal
      title={title}
      open={open}
      onCancel={handleClose}
      footer={() => {
        <div />;
      }}
    >
      <div className="basicModal-message-container">
        <div className="basicModal-icon">
          <MdInfo />
        </div>
        <div className="basicModal-message">{message}</div>
      </div>
    </Modal>
  );
};

export default BasicModal;
