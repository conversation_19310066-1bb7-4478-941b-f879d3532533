import { Modal } from "antd";
import "./AccountChangeEmailModal.css";
import { useEffect, useRef, useState } from "react";
import ModalOkButton from "../Buttons/ModalOkButton/ModalOkButton";
import ModalCancelButton from "../Buttons/ModalCancelButton/ModalCancelButton";
import Timer from "../Timer";
import { MdError } from "react-icons/md";

const AccountChangeEmailModal = ({
  open,
  handleClose,
  title,
  sendCode,
  step,
  sendConfirmMail,
  changeEmail,
  loading,
  setLoading,
  accountUser,
  serviceError,
  setServiceError,
  timerStopped,
  setTimerStopped,
  timerRestart,
  setTimerRestart,
}) => {
  const [email, setEmail] = useState();
  const [error, setError] = useState();

  const [confirmationCodeValues, setConfirmationCodeValues] = useState(
    Array(6).fill("")
  );
  const confirmationInputRef = useRef([]);

  useEffect(() => {
    if (accountUser) {
      setEmail(accountUser.email);
      setError(null);
    }
  }, [accountUser]);

  useEffect(() => {
    if (step === 0) {
      setConfirmationCodeValues(Array(6).fill(""));
      if (accountUser && accountUser.email) setEmail(accountUser.email);
    }
  }, [step]);

  const onChangeEmail = (e) => {
    setServiceError(null);
    setError();
    const value = e.target.value;
    setEmail(value);
  };

  const handleChange = (e, index) => {
    setServiceError(null);
    const value = e.target.value;
    if (/^[0-9]$/.test(value)) {
      const newValues = [...confirmationCodeValues];
      newValues[index] = value;
      setConfirmationCodeValues(newValues);

      if (index < 5) {
        confirmationInputRef.current[index + 1].focus();
      }
    } else {
      const newValues = [...confirmationCodeValues];
      newValues[index] = "";
      setConfirmationCodeValues(newValues);
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace" && !confirmationCodeValues[index] && index > 0) {
      confirmationInputRef.current[index - 1].focus();
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const paste = e.clipboardData.getData("text").slice(0, 6);
    const newValues = [...confirmationCodeValues];
    for (let i = 0; i < paste.length; i++) {
      if (/^[0-9]$/.test(paste[i])) {
        newValues[i] = paste[i];
      }
    }
    setConfirmationCodeValues(newValues);
    confirmationInputRef.current[Math.min(paste.length - 1, 5)].focus();
  };

  const validateInput = () => {
    let err = "";

    if (
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
        email
      )
    ) {
      setError(null);
      return true;
    } else {
      err = "Enter a valid email address";
      setError(err);
      return false;
    }
  };

  const applyEmailChangeRequest = async (isResend) => {
    const isValid = validateInput();
    if (!isValid) {
      return;
    }

    await sendCode(email);
    setTimeout(() => {
      setTimerStopped(false);
    }, 2000);
    if (isResend) {
      setTimerStopped(true);
      setTimerRestart(true);
    }
  };

  const maskEmail = () => {
    if (!accountUser) {
      return "";
    }
    const [localPart, domain] = accountUser?.email.split("@");
    const maskedLocalPart = localPart[0] + "********";
    return `${maskedLocalPart}@${domain}`;
  };

  const isActiveConfirmButton = () => {
    if (
      confirmationCodeValues.every((value) => value !== "") &&
      !serviceError
    ) {
      return true;
    }
    return false;
  };

  return (
    <Modal
      width={500}
      title={title}
      open={open}
      onCancel={handleClose}
      maskClosable={false}
      footer={() => {
        <div />;
      }}
    >
      {step === 0 && (
        <>
          <div className="accountChangeEmail-container">
            <div className="accountChangeEmail-input-container">
              <div
                className="accountChangeEmail-input-label"
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "30px",
                }}
              >
                Current email address:{" "}
                <div
                  style={{
                    marginLeft: "10px",
                    fontSize: "15px",
                    fontWeight: "500",
                  }}
                >
                  {accountUser?.email}
                </div>
              </div>
              <div className="accountChangeEmail-input-label">
                New email address
              </div>
              <input
                onChange={onChangeEmail}
                value={email}
                className="accountChangeEmail-input"
                placeholder="Please enter email address"
              />
            </div>

            <div
              className="accountChangeEmail-error-container"
              style={{ height: "20px" }}
            >
              {error && error.length > 0 && (
                <div className="accountChangeEmail-error">{error}</div>
              )}
              {serviceError && (
                <div className="accountChangeEmail-sended-code-serviceError">
                  <MdError style={{ marginRight: "5px", fontSize: "16px" }} />
                  {serviceError}
                </div>
              )}
            </div>
          </div>

          <div
            style={{
              display: "flex",
              justifyContent: "flex-end",
              marginTop: "30px",
            }}
          >
            <div style={{ marginRight: "12px" }}>
              <ModalCancelButton title="Cancel" action={handleClose} />
            </div>
            <div>
              <ModalOkButton
                title="Apply"
                passive={error || (email && email === accountUser?.email)}
                action={applyEmailChangeRequest}
                loading={loading}
                loadingTitle="Updating name ..."
              />
            </div>
          </div>
        </>
      )}

      {step === 1 && (
        <>
          <div className="accountChangeEmail-sended-container">
            <div className="accountChangeEmail-sended-header">
              Confirm it’s you to change your email
            </div>
            <div className="accountChangeEmail-sended-explanation">
              Enter the six-digit code we sent to{" "}
              <span style={{ fontWeight: "600", letterSpacing: "1px" }}>
                {maskEmail()}
              </span>
            </div>
            {console.log(
              "confirmationCodeValues : ",
              confirmationCodeValues.length
            )}
            <div className="accountChangeEmail-sended-code-container">
              <div className="accountChangeEmail-sended-code-header">
                Confirmation Code
              </div>
              <div
                className="accountChangeEmail-sended-input-container"
                onPaste={handlePaste}
                style={{ display: "flex", gap: "3px" }}
              >
                {confirmationCodeValues.map((value, index) => (
                  <input
                    key={index}
                    type="text"
                    maxLength="1"
                    value={value}
                    onChange={(e) => handleChange(e, index)}
                    onKeyDown={(e) => handleKeyDown(e, index)}
                    ref={(el) => (confirmationInputRef.current[index] = el)}
                    className="accountChangeEmail-sended-input"
                  />
                ))}
              </div>
              {serviceError && (
                <div className="accountChangeEmail-sended-code-serviceError">
                  <MdError style={{ marginRight: "5px", fontSize: "16px" }} />
                  {serviceError}
                </div>
              )}
              <div
                onClick={
                  isActiveConfirmButton()
                    ? () =>
                        sendConfirmMail(
                          confirmationCodeValues.join(""),
                          email,
                          "changeEmail"
                        )
                    : () => {}
                }
                className={
                  isActiveConfirmButton()
                    ? "accountChangeEmail-sended-code-confirmButton"
                    : "accountChangeEmail-sended-code-confirmButton-passive"
                }
              >
                Confirm
              </div>

              <div className="accountChangeEmail-sended-code-resend-container">
                <div className="accountChangeEmail-sended-code-resend-timer">
                  <Timer
                    startTime={10}
                    setStopped={setTimerStopped}
                    restart={timerRestart}
                    setRestart={setTimerRestart}
                  />
                </div>
                <div
                  onClick={() =>
                    timerStopped ? applyEmailChangeRequest(true) : {}
                  }
                  className="accountChangeEmail-sended-code-resend-button"
                  style={{
                    color: !timerStopped ? "#717171" : "",
                    cursor: !timerStopped ? "not-allowed" : "",
                  }}
                >
                  Resend email
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </Modal>
  );
};

export default AccountChangeEmailModal;
