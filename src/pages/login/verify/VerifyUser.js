import { useNavigate, useParams } from "react-router-dom";
import "./VerifyUser.css";
import { useEffect, useState } from "react";
import SmallLoading from "../../../components/UI/Loading/SmallLoading";
import { verifyUser } from "../../../services/http";
import logo from "../../../assets/images/logo/logo_64x64.png";
import SmallDarkButton from "../../../components/UI/Buttons/SmallDarkButton/SmallDarkButton";

const VerifyUser = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState();
  const { verifyToken } = useParams();
  const navigate = useNavigate();

  const userVerify = async () => {
    setLoading(true);
    try {
      const response = await verifyUser(verifyToken);
      if (response.status === 200) {
        setTimeout(() => {
          navigate("/login");
        }, 10000);
      }
    } catch (err) {
      setError(true);
      if (err.response.status === 404) {
        setError("User not found!");
        navigate("/notfound");
      }
      if (err.response.status === 409) {
        setError("Email already comfirmed!");
      }
      if (err.response.status === 410) {
        setError("Verify token expired!");
      }
      console.log("verifying error : ", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    async function verify() {
      await userVerify();
    }
    verify();
  }, [userVerify]);

  const goToLogin = () => {
    navigate("/login");
  };

  if (loading) {
    return <SmallLoading />;
  }

  return (
    <div className="veriyUser-container">
      <div className="verifyUser-logo-container">
        <img src={logo} alt="logo" />
      </div>
      <div className="verifyUser-description-container">
        {error && <div className="verifyUser-description-error">{error}</div>}
        {!error && (
          <div className="verifyUser-description">
            <div style={{ marginBottom: "20px" }}>User verified</div>
            <div style={{ display: "flex" }}>
              <span>Please go to</span>
              <SmallDarkButton title="Sign in" onClick={goToLogin} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VerifyUser;
