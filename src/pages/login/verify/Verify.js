import logo from "../../../assets/images/logo/logo_64x64.png";
import "./Verify.css";
import { useEffect, useState } from "react";
import { useAuth } from "../../../context/AuthContext";
import { useNavigate } from "react-router-dom";
import { resendVerify } from "../../../services/http";
import SmallLoading from "../../../components/UI/Loading/SmallLoading";
import BasicModal from "../../../components/UI/Modals/BasicModal";
import BasicErrorModal from "../../../components/UI/Modals/BasicErrorModal";
import SmallDarkButton from "../../../components/UI/Buttons/SmallDarkButton/SmallDarkButton";

const Verify = () => {
  const [verifyCount, setVerifyCount] = useState(20);
  const [clickCount, setClickCount] = useState(1);
  const [loading, setLoading] = useState(false);
  const [openSendedVerifyModal, setSendedVerifyModal] = useState(false);
  const [errorModal, setErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState();

  const auth = useAuth();

  const navigate = useNavigate();

  useEffect(() => {
    const timer = setTimeout(() => {
      setVerifyCount((v) => (v <= 0 ? 0 : verifyCount - 1));
    }, 1000);
    return () => {
      clearTimeout(timer);
    };
  }, [verifyCount]);

  const verifyLink = async () => {
    setLoading(true);
    setClickCount((click) => click + 1);
    const willAddedCount = clickCount * 5;
    setVerifyCount(willAddedCount + 20);
    try {
      const response = await resendVerify(auth.registeredUser);
      if (response && response.status === 200) {
        setSendedVerifyModal(true);
      }
    } catch (err) {
      if (err.response.status === 404) {
        setErrorMessage(
          "User not found by email : ",
          auth.registeredUser.email
        );
      }
      console.log("Sending verify email error : ", err);
      setErrorModal(true);
    } finally {
      setLoading(false);
    }
    setLoading(false);
  };

  const goToLogin = () => {
    navigate("/login");
  };

  if (loading) {
    return <SmallLoading />;
  }

  if (!auth.registeredUser || auth.registeredUser === undefined) {
    navigate("/notFound");
  }
  if (auth.registeredUser) {
    return (
      <div className="verify-container">
        <div className="verify-logo-container">
          <img src={logo} alt="logo" />
        </div>
        <div className="verify-inner-container">
          <div className="verify-content-container">
            <div className="verify-content-title">
              Verify your email address
            </div>
            <div className="verify-content-description">
              We've sent an email to{" "}
              <span style={{ fontWeight: "600" }}>
                {auth.registeredUser.email}
              </span>{" "}
              to verify your email address and activate your accout. <br />
              The link in the email will expire in 24 hours. <br />
            </div>

            {verifyCount > 0 ? (
              <div className="verify-content-description2">
                <span className="countdown verify-countdown-container">
                  <span style={{ "--value": verifyCount }}> </span>
                </span>
                If you did not receive an email.
              </div>
            ) : (
              <div className="verify-content-description2">
                {verifyCount > 0}
                <button
                  onClick={verifyLink}
                  style={{
                    color: "var(--black)",
                    background: "none",
                    border: "none",
                    padding: 0,
                    cursor: "pointer",
                    textDecoration: "underline",
                  }}
                >
                  Click here If you did not receive an email.
                </button>
              </div>
            )}
            <div className="verify-signIn-container">
              <SmallDarkButton title="Sign in" onClick={goToLogin} />
            </div>
          </div>
        </div>
        <BasicModal
          open={openSendedVerifyModal}
          handleClose={() => setSendedVerifyModal(false)}
          title="We sent a verify email"
          message={
            "A verification email was sent to " +
            `${auth.registeredUser.email}` +
            " email address."
          }
        />
        <BasicErrorModal
          open={errorModal}
          handleClose={() => setErrorModal(false)}
          title="Error"
          message={
            errorMessage
              ? errorMessage
              : "An unexpected error occurred while sending the verify email."
          }
        />
      </div>
    );
  }
};

export default Verify;
