.resetPassword-container {
}

.resetPassword-logo-container {
  width: 100%;
  padding: 5px 20px;
  background-color: var(--white);
  border-bottom: 1px solid var(--grey200);
  cursor: pointer;
}

.resetPassword-body-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 150px);
}

.resetPassword-body-header {
  text-align: center;
  margin-bottom: 60px;
}

.resetPassword-body-header-title {
  font-family: "Exo 2", sans-serif;
  font-size: 30px;
  font-weight: 600;
  margin-bottom: 10px;
}

.resetPassword-body-header-description {
  font-family: "Exo 2", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

.resetPassword-form-input-container {
  margin-bottom: 20px;
  width: 400px;
}

.resetPassword-form-input-label {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 17px;
}

.resetPassword-form-error {
  margin-top: -20px;
  color: var(--red100);
  height: 20px;
}

.resetPassword-form-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px;
  border: 1px solid var(--black);
  background-color: var(--black);
  color: var(--white);
  margin-top: 30px;
  border-radius: 3px;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 18px;
  cursor: pointer;
}

.resetPassword-form-button-passive {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px;
  border: 1px solid var(--grey100);
  background-color: var(--grey200);
  color: var(--black);
  margin-top: 30px;
  border-radius: 3px;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 18px;
  cursor: pointer;
}

.resetPassword-form-button:hover {
  opacity: 0.8;
  transition: 0.3s all;
}
