import "./ResetPassword.css";
import logo from "../../../assets/images/logo/logo-white-title-50.png";
import { Input } from "antd";
import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import {
  checkForgotPasswordEmail,
  resetPassword,
} from "../../../services/http";
import { MdWarning } from "react-icons/md";

const ResetPassword = () => {
  const [serviceError, setServiceError] = useState();
  const [password, setPassword] = useState("");
  const [verifyPassword, setVerifyPassword] = useState("");
  const [error, setError] = useState("");
  const [isResetPasswordSuccess, setResetPasswordSuccess] = useState(false);

  const navigate = useNavigate();

  const extractEmailFromUrl = () => {
    const url = window.location.href;
    const prefix = "resetPassword/";
    const startIndex = url.indexOf(prefix) + prefix.length;
    const endIndex = url.indexOf("/", startIndex);
    return url.substring(startIndex, endIndex);
  };
  const checkResetPasswordToken = useCallback(async () => {
    try {
      const email = extractEmailFromUrl();
      await checkForgotPasswordEmail(email);
    } catch (err) {
      console.log("Checking reset password token error : ", err);
      if (err.response.status === 406) {
        setServiceError(406); // Expired url
      }
    }
  }, [checkForgotPasswordEmail, extractEmailFromUrl]);

  useEffect(() => {
    async function checkUrl() {
      await checkResetPasswordToken();
    }
    checkUrl();
  }, [checkResetPasswordToken]);

  const checkError = () => {
    if (password.trim().length < 8) {
      setError("Your password must include at least 8 characters.");
    } else if (
      password &&
      verifyPassword &&
      password.trim() !== verifyPassword.trim()
    ) {
      setError("Passwords did not match.");
    } else {
      setError(null);
    }
  };

  useEffect(() => {
    checkError();
  }, [password, verifyPassword, checkError]);

  const onChangePassword = (e) => {
    setVerifyPassword("");
    const value = e.target.value;
    setPassword(value);
  };

  const onChangeVerifyPassword = (e) => {
    const value = e.target.value;
    setVerifyPassword(value);
  };

  const handleResetPassword = async () => {
    const email = extractEmailFromUrl();
    try {
      const response = await resetPassword(email, password);
      if (response.status === 200) {
        setResetPasswordSuccess(true);
      }
    } catch (err) {
      console.log("Reset password error : ", err);
    } finally {
    }
  };

  const isButtonActive = () => {
    if (!password || !verifyPassword) {
      return false;
    }
    if (password.length < 8 || password !== verifyPassword) {
      return false;
    }
    return true;
  };

  if (serviceError === 406) {
    return (
      <div className="resetPassword-container">
        <div className="resetPassword-logo-container">
          <img
            src={logo}
            onClick={() => navigate("/login")}
            className="resetPassword-logo"
            alt="Logo"
          />
        </div>
        <div className="resetPassword-body-container">
          <div
            className="resetPassword-body-header"
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <MdWarning
              style={{
                fontSize: "72px",
                color: "orange",
                marginBottom: "20px",
              }}
            />
            <div className="resetPassword-body-header-title">
              Your password reset request has expired. <br />
              <div
                className="resetPassword-body-header-description"
                style={{ marginTop: "15px" }}
              >
                {" "}
                You can create a new password reset request from the <br />
                <div
                  style={{
                    marginTop: "15px",
                  }}
                >
                  <u
                    style={{ cursor: "pointer", fontWeight: "600" }}
                    onClick={() => navigate("/login")}
                  >
                    forgot password
                  </u>
                  &nbsp; section.
                </div>{" "}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isResetPasswordSuccess) {
    return (
      <div className="resetPassword-container">
        <div className="resetPassword-logo-container">
          <img
            src={logo}
            onClick={() => navigate("/login")}
            className="resetPassword-logo"
            alt="Logo"
          />
        </div>
        <div className="resetPassword-body-container">
          <div
            className="resetPassword-body-header-title"
            style={{ marginBottom: "30px" }}
          >
            Reset Password
          </div>
          <div
            className="resetPassword-body-header-description"
            style={{ textAlign: "center" }}
          >
            Your new password is saved. <br />
            You can now continue using your account.
          </div>
          <div
            style={{ width: "300px" }}
            onClick={() => navigate("/login")}
            className={"resetPassword-form-button"}
          >
            OK
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="resetPassword-container">
      <div className="resetPassword-logo-container">
        <img
          src={logo}
          onClick={() => navigate("/login")}
          className="resetPassword-logo"
          alt="Logo"
        />
      </div>
      <div className="resetPassword-body-container">
        <div className="resetPassword-body-header">
          <div className="resetPassword-body-header-title">Reset Password</div>
          <div className="resetPassword-body-header-description">
            You can reset your password using this password
          </div>
        </div>
        <div className="resetPassword-form">
          <div className="resetPassword-form-input-container">
            <div className="resetPassword-form-input-label">New Password</div>
            <Input.Password value={password} onChange={onChangePassword} />
          </div>
          <div className="resetPassword-form-input-container">
            <div className="resetPassword-form-input-label">
              Verify Password
            </div>
            <Input.Password
              value={verifyPassword}
              onChange={onChangeVerifyPassword}
            />
          </div>
          <div className="resetPassword-form-error">{error}</div>
          <div
            onClick={handleResetPassword}
            className={
              isButtonActive()
                ? "resetPassword-form-button"
                : "resetPassword-form-button-passive"
            }
          >
            Create a New Password
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
