import { Button, Form, Input } from "antd";
import logo from "../../../assets/images/logo/logo_64x64.png";
import "./ForgotPassword.css";
import { useState } from "react";
import { resetPassword } from "../../../services/http";
import SmallLoading from "../../../components/UI/Loading/SmallLoading";
import { MdError } from "react-icons/md";
import { useNavigate } from "react-router-dom";
import { IoIosCheckmarkCircle } from "react-icons/io";

const ForgotPassword = () => {
  const [loading, setLoading] = useState(false);
  const [errorCode, setErrorCode] = useState();
  const [responseSuccess, setResponseSuccess] = useState(false);

  const navigate = useNavigate();

  const onFinish = async (values) => {
    setLoading(true);
    try {
      const response = await resetPassword(values.email);
      if (response.status === 200) {
        setResponseSuccess(true);
      }
    } catch (err) {
      console.log("Forgot password error : ", err);
      if (err.response.status === 404) {
        setErrorCode(404);
      } else {
        setErrorCode(1);
      }
    } finally {
      setLoading(false);
    }
  };

  const gotoSignUp = () => {
    navigate("/login");
  };

  const getError = () => {
    if (errorCode === 404) {
      return (
        <div className="forgotPassword-error-wrapper">
          <div className="forgotPassword-error-container">
            <div className="forgotPassword-error-icon-container">
              <MdError />
            </div>
            <div className="forgotPassword-error-description">
              Email address not found. If you have not registered before, you
              can register from the{" "}
              <a href="#" onClick={gotoSignUp}>
                sign up
              </a>{" "}
              section or send a password reset email to your email address
              registered in the system.
            </div>
          </div>
        </div>
      );
    }
  };

  if (loading) {
    return <SmallLoading />;
  }

  return (
    <div className="forgotPassword-wrapper">
      <div className="forgotPassword-container">
        <div className="forgotPassword-logo-container">
          <img src={logo} alt="logo" />
        </div>
        <div className="forgotPassword-explanation-container">
          <div className="forgotPassword-explanation">
            Type the address linked to your account and we'll send you password
            new password. They might end up in your spam folder, so please check
            there as well.
          </div>
        </div>

        {getError()}
        {responseSuccess && (
          <div className="forgotPassword-success-wrapper">
            <div className="forgotPassword-success-container">
              <div className="forgotPassword-success-icon-container">
                <IoIosCheckmarkCircle />
              </div>
              <div className="forgotPassword-success-description">
                We have sent your new password to your e-mail address. please
                check your e-mail address. You can go to the{" "}
                <a href="#" onClick={gotoSignUp}>
                  sign in page
                </a>
              </div>
            </div>
          </div>
        )}
        <Form
          className="forgotPassword-form-container"
          layout="vertical"
          autoComplete="off"
          onFinish={onFinish}
        >
          <Form.Item
            label="Email"
            name="email"
            rules={[
              { required: true, message: "Please enter valid email address" },
              { type: "email", message: "Please enter valid email address" },
            ]}
          >
            <Input placeholder="<EMAIL>" className="form-input" />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              size="large"
              className="forgotPassword-form-login-button"
            >
              Reset Password
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default ForgotPassword;
