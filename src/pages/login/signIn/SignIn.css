.signIn-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.signIn-container {
  align-items: center;
  position: relative;
  background: var(--white);
  padding: 12px;
  border: 1px solid var(--grey300);
  border-radius: 5px;
}

.signIn-logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.signIn-welcome-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  margin-top: 10px;
  padding: 0 15px;
}

.signIn-welcome-title {
  color: var(--text-dark);
  text-align: center;
  font-size: 28px;
  font-weight: 500;
  font-family: "Exo 2", sans-serif;
  margin-bottom: 10px;
}

.signIn-welcome-explanation {
  color: var(--text-dark);
  text-align: center;
  font-size: 20px;
  font-weight: 400;
  font-family: "Exo 2", sans-serif;
}

.signIn-form-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 375px;
  padding: 30px 15px;
}

.signIn-form-login-button {
  background-color: var(--green100);
  width: 100%;
  color: var(--white);
  border-radius: 3px;
  height: 46px;
  font-weight: 500;
}

.signIn-form-login-button:hover {
  background-color: var(--green100);
  opacity: 0.7 !important;
}

.ant-input-affix-outlined:hover {
  border-color: var(--bg-dark) !important;
}

.signIn-signUp-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  width: 100%;
  margin-top: 20px;
  color: var(--text-dark);
}

.signIn-signUp-button {
  padding: 0 5px;
  border-bottom: 2px solid var(--black);
  opacity: 1;
  color: var(--black);
  cursor: pointer;
  margin-left: 5px;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
}

.signIn-signUp-button:hover {
  opacity: 0.7;
  transition: 0.3s all ease-in-out;
}

.signIn-forget-password {
  display: flex;
  justify-content: flex-end;
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  color: var(--text-dark);
}

.signIn-error-wrapper {
  display: flex;
  justify-content: center;
}

.signIn-error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 375px;
  margin: 20px 0 -10px 0;
  font-size: 13px;
  font-family: "Exo 2", sans-serif;
  border: 1px solid var(--grey300);
}

.signIn-error-icon-container {
  margin-right: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fb4f4f;
  color: var(--white);
  height: 70px;
  padding: 0 10px;
  font-size: 20px;
}

.signIn-error-description {
  padding: 10px 5px 10px 0;
}

.signIn-notVerified-verify-countdown-container {
  border: 1px solid var(--grey300);
  padding: 2px 5px;
  text-align: center;
}

.ant-btn {
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}

.ant-btn:hover {
  background-color: var(--green100) !important;
  opacity: 0.7;
  transition: 0.3s all ease-in-out;
}

.ant-input-affix-wrapper > input.ant-input {
  height: 25px !important;
}

.ant-input-outlined {
  height: 40px !important;
}
