import "./SignIn.css";
import logo from "../../../assets/images/logo/logo-white-title-50.png";
import { Button, Form, Input } from "antd";
import OrDivider from "../../../components/UI/OrDivider/OrDivider";
import SocialButton from "../../../components/UI/Buttons/SocialButton/SocialButton";
import "../../../css/CustomAntd.css";
import { useEffect, useState } from "react";
import SmallLoading from "../../../components/UI/Loading/SmallLoading";
import {
  authenticateGoogleUser,
  authenticateUser,
  forgotPasswordSendMail,
  resendVerify,
} from "../../../services/http";
import { MdError } from "react-icons/md";
import { useAuth } from "../../../context/AuthContext";
import { useNavigate } from "react-router-dom";
import { useMainContext } from "../../../context/MainContext";
import styled from "styled-components";
import { useGoogleLogin } from "@react-oauth/google";
import ForgetPasswordModal from "../../../components/UI/Modals/ForgetPasswordModal";

const CustomButton = styled(Button)`
  &.custom-button {
    background-color: ${(props) => props.backgroundColor} !important;
    color: ${(props) => "#FFF"} !important;
    &:hover {
      opacity: 0.8 !important;
    }
  }
`;

const SignIn = ({ setActiveForm }) => {
  const [loading, setLoading] = useState(false);
  const [errorCode, setErrorCode] = useState();
  const [forgotPasswordErrorCode, setForgotPasswordErrorCode] = useState();
  const [verifyCount, setVerifCount] = useState(20);
  const [verifyClickCount, setVerifyClickCount] = useState(1);
  const [authRequest, setAuthRequest] = useState();
  const [forgetPasswordModal, setForgetPasswordModal] = useState(false);
  const [isSendedForgetPasswordMail, setSendedForgetPasswordMail] =
    useState(false);

  const auth = useAuth();
  const mainContext = useMainContext();

  const navigate = useNavigate();

  useEffect(() => {
    if (verifyClickCount > 0) {
      const timer = setTimeout(() => {
        setVerifCount((prevCount) => (prevCount <= 0 ? 0 : prevCount - 1));
      }, 1000);

      return () => {
        clearTimeout(timer);
      };
    }
  }, [verifyCount, verifyClickCount]);

  const onFinish = async (values) => {
    setLoading(true);
    setAuthRequest(values);
    try {
      const response = await authenticateUser(values);
      if (response) {
        const userRole = response.data.role;
        const userWorkspaces = response.data.workspaces;
        if (
          userRole === "USER" &&
          (!userWorkspaces || userWorkspaces.length === 0)
        ) {
          console.log("Redirecting to notWorkspace");
          navigate("/notAuthorization");

          return;
        }
        auth.onLoginSuccess(response.data);
        mainContext.saveOrganization(response.data.organization);

        if (
          userRole === "USER" &&
          userWorkspaces &&
          userWorkspaces.length > 0
        ) {
          mainContext.saveWorkspace(userWorkspaces[0]);
        } else {
          mainContext.saveWorkspace(
            response.data.organization.defaultWorkspace
          );
        }

        navigate(
          `/workspace/${response.data.organization.defaultWorkspace.uniqueId}`
        );
      }
    } catch (err) {
      console.log("err : ", err);
      if (err.response?.data?.errorCode === 401) {
        setErrorCode(401);
      }
      if (err.response?.data?.errorCode === 404) {
        setErrorCode(404);
      }
      if (err.response?.data?.errorCode === 406) {
        setErrorCode(406);
      }
    } finally {
      setLoading(false);
    }
  };

  const verify = async () => {
    setLoading(true);
    try {
      const response = await resendVerify(authRequest);
      if (response.status === 200) {
        setVerifyClickCount((click) => click + 1);
        const willAddedCount = verifyClickCount * 5;
        setVerifCount(willAddedCount + 20);
      }
    } catch (err) {
      console.log("Verifying error : ", err);
    } finally {
      setLoading(false);
    }
  };

  const sendForgotPasswordMail = async (email) => {
    try {
      setLoading(true);
      await forgotPasswordSendMail(email);
    } catch (err) {
      console.log("Sending forgot password mail error : ", err);
      if (err.response.status === 404) {
        setForgotPasswordErrorCode(404); // email not found
      }
    } finally {
      setLoading(false);
      setSendedForgetPasswordMail(false);
    }
  };

  const getError = () => {
    if (errorCode === 401) {
      return (
        <div className="signIn-error-wrapper">
          <div className="signIn-error-container">
            <div
              className="signIn-error-icon-container"
              style={{ height: "80px" }}
            >
              <MdError />
            </div>
            <div className="signIn-error-description">
              The account has not been verified. Please confirm your account
              from the URL sent to your e-mail.{" "}
              {verifyClickCount > 1 && verifyCount > 0 ? (
                <span className="countdown signIn-notVerified-verify-countdown-container">
                  <span style={{ "--value": verifyCount }}> </span>
                </span>
              ) : (
                <a
                  href="#"
                  onClick={verify}
                  style={{
                    color: "var(--black)",
                    borderBottom: "1px solid var(--black",
                  }}
                >
                  Click again for the confirmation email.
                </a>
              )}
            </div>
          </div>
        </div>
      );
    }

    if (errorCode === 404) {
      return (
        <div className="signIn-error-wrapper">
          <div className="signIn-error-container">
            <div className="signIn-error-icon-container">
              <MdError />
            </div>
            <div className="signIn-error-description">
              That Formiqo account doesn't exist. Enter different account or
              please{" "}
              <a
                href="#"
                onClick={() => setActiveForm("signUp")}
                style={{
                  color: "var(--black)",
                }}
              >
                Sign up.
              </a>
            </div>
          </div>
        </div>
      );
    }

    if (errorCode === 406) {
      return (
        <div className="signIn-error-wrapper">
          <div className="signIn-error-container">
            <div className="signIn-error-icon-container">
              <MdError />
            </div>
            <div className="signIn-error-description">
              Your login info is not right. Try again, or{" "}
              <a
                href="#"
                style={{
                  color: "var(--black)",
                }}
                onClick={() => setForgetPasswordModal(true)}
              >
                reset your password
              </a>
            </div>
          </div>
        </div>
      );
    }
  };

  const googleLogin = useGoogleLogin({
    onSuccess: async (response) => {
      const accessToken = response.access_token;
      try {
        setLoading(true);
        const res = await fetch(
          "https://www.googleapis.com/oauth2/v3/userinfo",
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );

        const userInfo = await res.json();
        if (userInfo) {
          const name = userInfo.name.split(" ");
          const request = {
            email: userInfo.email,
            firstName: name[0],
            lastName: name[1],
          };

          const response = await authenticateGoogleUser(request);
          if (response.status === 200) {
            auth.onLoginSuccess(response.data);
            mainContext.saveOrganization(response.data.organization);
            const userRole = response.data.role;
            const userWorkspaces = response.data.workspaces;
            if (
              userRole === "USER" &&
              userWorkspaces &&
              userWorkspaces.length > 0
            ) {
              mainContext.saveWorkspace(userWorkspaces[0]);
            } else {
              mainContext.saveWorkspace(
                response.data.organization.defaultWorkspace
              );
            }

            navigate(
              `/workspace/${response.data.organization.defaultWorkspace.uniqueId}`
            );
          }
        }
      } catch (error) {
        console.error("Getting google user information error :", error);
      } finally {
        setLoading(false);
      }
    },
    onError: () => {
      console.log("Google Login Failed");
    },
  });

  if (loading) {
    return <SmallLoading />;
  }

  return (
    <>
      <div className="signIn-wrapper">
        <div className="signIn-container">
          <div className="signIn-logo-container">
            <img src={logo} alt="logo" />
          </div>

          <div className="signIn-welcome-container">
            <div className="signIn-welcome-title">Welcome back</div>
            <div className="signIn-welcome-explanation">
              Please use your email address to log in
            </div>
          </div>

          {getError()}

          <Form
            className="signIn-form-container"
            layout="vertical"
            autoComplete="off"
            onFinish={onFinish}
          >
            <Form.Item
              label="Email"
              name="email"
              rules={[
                { required: true, message: "Please enter valid email address" },
                { type: "email", message: "Please enter valid email address" },
              ]}
            >
              <Input placeholder="<EMAIL>" className="form-input" />
            </Form.Item>
            <Form.Item
              label="Password"
              name="password"
              rules={[
                () => ({
                  validator(_, value) {
                    if (value && value.length > 7) {
                      return Promise.resolve();
                    }
                    return Promise.reject("At least 8 characters");
                  },
                }),
              ]}
            >
              <Input.Password placeholder="at least 8 characters" />
            </Form.Item>
            <Form.Item>
              <CustomButton
                type="primary"
                htmlType="submit"
                size="large"
                className="custom-button signIn-form-login-button"
                backgroundColor="#000"
              >
                Sign in with email
              </CustomButton>
            </Form.Item>
            <div
              className="signIn-forget-password"
              onClick={() => setForgetPasswordModal(true)}
            >
              Forgot password?
            </div>
            <OrDivider />

            <SocialButton
              social="google"
              title="Sign in with Google"
              login={googleLogin}
            />
            <div className="signIn-signUp-container">
              <div>Don't have an account yet?</div>
              <div
                className="signIn-signUp-button"
                onClick={() => setActiveForm("signUp")}
              >
                Sign up
              </div>
            </div>
          </Form>
        </div>
      </div>
      <ForgetPasswordModal
        open={forgetPasswordModal}
        handleClose={() => setForgetPasswordModal(false)}
        handleOk={sendForgotPasswordMail}
        loading={loading}
        errorCode={forgotPasswordErrorCode}
        isSendedForgetPasswordMail={isSendedForgetPasswordMail}
        setSendedForgetPasswordMail={setSendedForgetPasswordMail}
      />
    </>
  );
};

export default SignIn;
