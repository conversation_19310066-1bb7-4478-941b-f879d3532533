import "./SignUp.css";
import logo from "../../../assets/images/logo/logo-white-title-50.png";
import { Button, Form, Input } from "antd";
import OrDivider from "../../../components/UI/OrDivider/OrDivider";
import SocialButton from "../../../components/UI/Buttons/SocialButton/SocialButton";
import "../../../css/CustomAntd.css";
import { useEffect, useState } from "react";
import BackButton from "../../../components/UI/Buttons/BackButton/BackButton";
import BasicErrorModal from "../../../components/UI/Modals/BasicErrorModal";
import SmallLoading from "../../../components/UI/Loading/SmallLoading";
import { authenticateGoogleUser, registerUser } from "../../../services/http";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../../context/AuthContext";
import styled from "styled-components";
import { useGoogleLogin } from "@react-oauth/google";
import { useMainContext } from "../../../context/MainContext";

const CustomButton = styled(Button)`
  &.custom-button {
    background-color: ${(props) => "#000"} !important;
    color: ${(props) => "#FFF"} !important;
    &:hover {
      opacity: 0.8 !important;
    }
  }
`;

let errorVar = null;

const SignUp = ({ setActiveForm }) => {
  const [emailSignUp, setEmailSignUp] = useState(false);
  const [errorModal, setErrorModal] = useState(false);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [sendedVerifyEmail, setSendedVerifyEmail] = useState(false);

  const auth = useAuth();
  const mainContext = useMainContext();

  const navigate = useNavigate();

  const [form] = Form.useForm();

  useEffect(() => {
    setErrorModal(false);
    setError(null);
  }, []);

  useEffect(() => {
    if (sendedVerifyEmail) {
      navigate("/login/verify");
    }
  }, [sendedVerifyEmail, navigate]);

  const onChangeEmailSignUp = () => {
    setErrorModal(false);
    setError(null);
    errorVar = null;
    setEmailSignUp(!emailSignUp);
  };

  const closeErrorModal = () => {
    setErrorModal(false);
    setError(null);
    errorVar = null;
    form.resetFields();
  };

  const errorCheck = (values) => {
    const { email, password } = values;

    if (email.length < 8) {
      setErrorModal(true);
      setError("Please enter valid email address, Exm: <EMAIL>");
      errorVar = "Please enter valid email address, Exm: <EMAIL>";
      setLoading(false);
      return;
    }
    if (password.length < 8) {
      setErrorModal(true);
      setError("Please enter valid password At least 8 characters");
      errorVar = "Please enter valid password At least 8 characters";
      setLoading(false);
      return;
    }
  };

  const getRequest = (values) => {
    const { email, password } = values;
    return { email, password };
  };

  const onFinish = async (values) => {
    setLoading(true);
    errorCheck(values);
    if (!error && !errorVar) {
      try {
        const request = getRequest(values);
        const response = await registerUser(request);
        if (response.status === 200) {
          console.log("response.data : ", response.data);
          setSendedVerifyEmail(true);
          auth.setRegisteredUser(response.data);
          return;
        }
      } catch (err) {
        console.log("Error register user : ", err);
        if (err.response.status && err.response.status === 409) {
          setErrorModal(true);
          setError("This email has been used.");
        }
      } finally {
        setLoading(false);
      }
    }
    setLoading(false);
  };

  const googleLogin = useGoogleLogin({
    onSuccess: async (response) => {
      const accessToken = response.access_token;
      try {
        setLoading(true);
        const res = await fetch(
          "https://www.googleapis.com/oauth2/v3/userinfo",
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );

        const userInfo = await res.json();
        if (userInfo) {
          const name = userInfo.name.split(" ");
          const request = {
            email: userInfo.email,
            firstName: name[0],
            lastName: name[1],
          };

          const response = await authenticateGoogleUser(request);
          if (response.status === 200) {
            auth.onLoginSuccess(response.data);
            mainContext.saveOrganization(response.data.organization);
            const userRole = response.data.role;
            const userWorkspaces = response.data.workspaces;
            if (
              userRole === "USER" &&
              userWorkspaces &&
              userWorkspaces.length > 0
            ) {
              mainContext.saveWorkspace(userWorkspaces[0]);
            } else {
              mainContext.saveWorkspace(
                response.data.organization.defaultWorkspace
              );
            }

            navigate(
              `/workspace/${response.data.organization.defaultWorkspace.uniqueId}`
            );
          }
        }
      } catch (error) {
        console.error("Getting google user information error :", error);
      } finally {
        setLoading(false);
      }
    },
    onError: () => {
      console.log("Google Login Failed");
    },
  });

  const onFinishFailed = (errorInfo) => {};

  if (loading) {
    return <SmallLoading />;
  }

  return (
    <div className="signUp-wrapper">
      <div className="signUp-container">
        <div className="signUp-logo-container">
          <img src={logo} alt="logo" />
        </div>
        <div className="signUp-welcome-container">
          <div className="signUp-welcome-explanation">
            Get better data with forms, surveys, quizzes & more.
          </div>
        </div>
        <Form
          form={form}
          className="signIn-form-container"
          layout="vertical"
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          {emailSignUp && (
            <div>
              <Form.Item
                label="Email"
                name="email"
                rules={[
                  {
                    required: true,
                    message: "Please enter valid email address",
                  },
                  {
                    type: "email",
                    message: "Please enter valid email address",
                  },
                ]}
              >
                <Input
                  placeholder="<EMAIL>"
                  className="form-input"
                />
              </Form.Item>
              <Form.Item
                label="Password"
                name="password"
                rules={[
                  { required: true, message: "Please enter valid password!" },
                  () => ({
                    validator(_, value) {
                      if (value && value.length > 8) {
                        return Promise.resolve();
                      }
                      return Promise.reject("At least 8 characters");
                    },
                  }),
                ]}
              >
                <Input.Password
                  placeholder="At least 8 characters"
                  className="form-input"
                />
              </Form.Item>
              <Form.Item>
                <div
                  style={{
                    display: "flex",
                    alignItems: "flex-start",
                    margin: "10px 0",
                    padding: "5px",
                  }}
                >
                  <div
                    style={{
                      fontSize: "13px",
                      fontFamily: "Inter",
                      opacity: "0.9",
                    }}
                  >
                    By continuing, you agree to Formiqo's <u>Terms of Use</u>{" "}
                    <br />
                    Read <u>our Privacy Policy.</u>
                  </div>
                </div>
              </Form.Item>
            </div>
          )}

          <div
            style={{
              display: "flex",
              width: "100%",
              justifyContent: "space-between",
            }}
          >
            {emailSignUp && (
              <div style={{ marginRight: "20px" }}>
                <BackButton onClick={onChangeEmailSignUp} />
              </div>
            )}
            {emailSignUp ? (
              <Form.Item style={{ width: "100%" }}>
                <CustomButton
                  type="primary"
                  htmlType="submit"
                  size="large"
                  className="custom-button signUp-form-login-button"
                >
                  Sign up with email
                </CustomButton>
              </Form.Item>
            ) : (
              <Form.Item style={{ width: "100%" }}>
                <CustomButton
                  type="primary"
                  htmlType="submit"
                  size="large"
                  className="custom-button signUp-form-login-button"
                  onClick={onChangeEmailSignUp}
                >
                  Sign up with email
                </CustomButton>
              </Form.Item>
            )}
          </div>

          {!emailSignUp && (
            <div>
              <OrDivider />
              <SocialButton
                social="google"
                title="Sign up with Google"
                login={googleLogin}
              />
              <div className="signIn-signUp-container">
                <div>Already have an account?</div>
                <div
                  className="signIn-signUp-button"
                  onClick={() => setActiveForm("signIn")}
                >
                  Sign in
                </div>
              </div>
            </div>
          )}
        </Form>
        <BasicErrorModal
          open={errorModal}
          handleClose={closeErrorModal}
          title="An error occurred while sign up"
          message={error}
        />
      </div>
    </div>
  );
};

export default SignUp;
