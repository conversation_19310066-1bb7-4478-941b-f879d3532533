import { useState } from "react";
import SignUp from "./signUp/SignUp";
import SignIn from "./signIn/SignIn";
import { useLocation } from "react-router-dom";

const Login = () => {
  const location = useLocation();
  const activeTab =
    location.state && location.state.activeTab
      ? location.state.activeTab
      : "signIn";
  const [activeForm, setActiveForm] = useState(activeTab);
  return (
    <>
      {activeForm === "signIn" && <SignIn setActiveForm={setActiveForm} />}
      {activeForm === "signUp" && <SignUp setActiveForm={setActiveForm} />}
    </>
  );
};
export default Login;
