import { AiOutlinePlus } from "react-icons/ai";
import Navbar from "../../../components/UI/Navbar/Navbar";
import "./SelectForm.css";
import { HiOutlineTemplate } from "react-icons/hi";
import { RiCheckboxMultipleBlankLine } from "react-icons/ri";
import { useNavigate } from "react-router-dom";
import { MdClose } from "react-icons/md";
import { useMainContext } from "../../../context/MainContext";
import { useState } from "react";
import { initializeForm } from "../../../services/http";
import { generateFormId, generatePageId, pagesWithRef } from "../FormUtil";
import SmallLoading from "../../../components/UI/Loading/SmallLoading";

const SelectForm = () => {
  const [creatingFormLoading, setCreatingFormLoading] = useState(false);

  const navigation = useNavigate();

  const mainContext = useMainContext();

  const createForm = async (type) => {
    setCreatingFormLoading(true);
    try {
      setCreatingFormLoading(true);
      const initialPage = {
        id: generatePageId(),
        order: 0,
        items: [],
      };
      const request = {
        id: generateFormId(),
        name: "My Form",
        type: type,
        workspace: mainContext.selectedWorkspace,
        pages: [initialPage],
      };
      const response = await initializeForm(request);
      if (response.status === 200) {
        let createdForm = response.data;
        const addedRefsToPages = pagesWithRef(createdForm.pages);
        createdForm.pages = addedRefsToPages;

        navigation(
          `/workspace/${mainContext.selectedWorkspace?.uniqueId}/form/${response.data.id}`,
          {
            state: { formId: createdForm.id },
          }
        );
      }
    } catch (err) {
      console.log("Initializing form error : ", err);
    } finally {
      setCreatingFormLoading(false);
    }
  };

  if (creatingFormLoading) {
    return <SmallLoading />;
  }

  return (
    <>
      <Navbar />
      <div className="selectForm-container">
        <div
          style={{
            width: "100%",
            display: "flex",
            justifyContent: "flex-end",
            marginRight: "-45px",
          }}
        >
          <div
            className="selectForm-back-button"
            onClick={() => navigation("/")}
          >
            <div className="selectForm-back-button-icon">
              <MdClose />
            </div>
          </div>
        </div>
        <div className="selectForm-title">Create a Form</div>
        <div className="selectForm-explanation">
          Create a form to start gathering data
        </div>
        <div className="selectForm-formTypes">
          <div
            className="selectForm-formTypes-blank"
            onClick={() => {
              createForm("STANDART");
            }}
            /*
            onClick={() =>
              navigation(
                `/workspace/${mainContext.selectedWorkspace?.uniqueId}/form/new-standart-form`
              )
            }
              */
          >
            <div className="selectForm-formTypes-blank-icon">
              <AiOutlinePlus />
            </div>
            <div className="selectForm-formTypes-blank-title">
              Start from standart form
            </div>
            <div className="selectForm-formTypes-blank-explanation">
              A blank slate is all you need
            </div>
          </div>

          <div className="selectForm-formTypes-step">
            <div className="selectForm-formTypes-step-icon">
              <RiCheckboxMultipleBlankLine />
            </div>
            <div className="selectForm-formTypes-step-title">
              Start from blank step by step form
            </div>
            <div className="selectForm-formTypes-step-explanation">
              A blank slate is all you need
            </div>
          </div>

          <div className="selectForm-formTypes-template">
            <div className="selectForm-formTypes-template-icon">
              <HiOutlineTemplate />
            </div>
            <div className="selectForm-formTypes-template-title">
              Use template
            </div>
            <div className="selectForm-formTypes-template-explanation">
              Choose from 10.000 premade forms
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SelectForm;
