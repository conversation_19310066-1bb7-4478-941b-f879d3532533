.selectForm-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--white);
  margin-top: 60px;
  height: calc(100% - 60px);
  padding: 50px;
  background-color: var(--bg-light);
}

.selectForm-back-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  border-radius: 30px;
  background-color: var(--red200);
  /*position: absolute;*/
  cursor: pointer;
}

.selectForm-back-button:hover {
  /*
    margin-top: 2px;
    border:1px solid var(--black);
    transition: 0.15s all;
    */
  opacity: 0.8;
}

.selectForm-back-button-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  color: var(--white);
}

.selectForm-back-button-title {
  font-weight: 600;
  font-family: "Exo 2", sans-serif;
}

.selectForm-formTypes {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.selectForm-title {
  font-size: 28px;
  font-family: "Exo 2", sans-serif;
  font-weight: 600;
  color: var(--text-dark);
  padding: 30px 0 15px 0;
}

.selectForm-explanation {
  font-size: 18px;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  color: var(--text-dark);
  padding-bottom: 50px;
}

.selectForm-formTypes-blank {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 300px;
  height: 300px;
  background-color: var(--white);
  border: 1px solid var(--bg-light);
  border-radius: 30px;
  background-color: white;
  margin-right: 50px;
  cursor: pointer;
  position: relative;
  border: 1px solid var(--black);
}

.selectForm-formTypes-blank:hover {
  margin-top: -30px;
  border-bottom: 15px solid var(--black);
  transition: 0.15s all;
}

.selectForm-formTypes-blank-icon {
  margin-bottom: 30px;
  font-size: 64px;
  color: #53b5a3;
  position: relative;
}

.selectForm-formTypes-blank-title {
  color: var(--black);
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 10px;
  position: relative;
}

.selectForm-formTypes-blank-explanation {
  color: var(--black);
  font-size: 16px;
  font-weight: 400;
  position: relative;
}

.selectForm-formTypes-step {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 300px;
  height: 300px;
  background-color: var(--white);
  border: 1px solid var(--bg-light);
  border-radius: 30px;
  background-color: white;
  margin-right: 50px;
  /*cursor: pointer;*/
  position: relative;
  border: 1px solid var(--black);
  opacity: 0.6;
}

/*
.selectForm-formTypes-step:hover {
  margin-top: -30px;
  border-bottom: 15px solid var(--black);
  transition: 0.15s all;
}
  */

.selectForm-formTypes-step-icon {
  margin-bottom: 30px;
  font-size: 64px;
  color: #5b51ea;
  position: relative;
}

.selectForm-formTypes-step-title {
  color: var(--black);
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
  position: relative;
}

.selectForm-formTypes-step-explanation {
  color: var(--black);
  font-size: 16px;
  font-weight: 400;
  position: relative;
}

.selectForm-formTypes-template {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 300px;
  height: 300px;
  background-color: var(--white);
  border: 1px solid var(--black);
  border-radius: 30px;
  background-color: white;
  margin-right: 50px;
  /*cursor: pointer;*/
  box-sizing: border-box;
  position: relative;
  opacity: 0.6;
}

/*
.selectForm-formTypes-template:hover {
    border-bottom: 15px solid var(--black);
    margin-top: -30px;
    transition: 0.15s all;
}
    */

.selectForm-formTypes-template-icon {
  margin-bottom: 30px;
  font-size: 64px;
  color: #eb8033;
  position: relative;
}

.selectForm-formTypes-template-title {
  color: var(--black);
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 10px;
  position: relative;
}

.selectForm-formTypes-template-explanation {
  color: var(--black);
  font-size: 16px;
  font-weight: 400;
  position: relative;
}
