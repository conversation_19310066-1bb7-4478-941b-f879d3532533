import "./StandartForm.css";
import { message } from "antd";
import { useEffect, useRef, useState } from "react";
import Navbar from "../../../../components/UI/Navbar/Navbar";
import FieldToolbox from "../../../../components/UI/Form/FieldToolbox/FieldToolbox";
import FormCanvas from "../../../../components/UI/Form/FormCanvas/FormCanvas";
import FormSettings from "../../../../components/UI/Form/FormSettings/FormSettings";
import FormPublishSettings from "../../../../components/UI/Form/FormPublishSettings/FormPublishSettings";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import WelcomePage from "../../../../components/UI/Form/WelcomePage/WelcomePage";
import ErrorModalWithSelection from "../../../../components/UI/Modals/ErrorModalWithOneSelection";
import { MdFirstPage, MdLastPage } from "react-icons/md";
import ThankyouPage from "../../../../components/UI/Form/ThankyouPage/ThankyouPage";
import PreviewModal from "../../../../components/UI/Modals/PreviewModal";
import FForm from "../../../fform/FForm";
import CreateFormModal from "../../../../components/UI/Modals/CreateFormModal";
import {
  deleteExternalPageById,
  deleteFieldById,
  deletePageById,
  getFormById,
  getThemeById,
  publishForm,
  renameFormById,
  saveForm,
  updateOptions,
} from "../../../../services/http";
import { useMainContext } from "../../../../context/MainContext";
import { useLocation, useNavigate } from "react-router-dom";
import {
  deepClone,
  deepEqualItemsOnly,
  equalPages,
  generateFiedId,
  generatePageId,
  hasUnpublishedItem,
  hasUnpublishedPage,
  pagesWithoutRef,
  pagesWithRef,
} from "../../FormUtil";
import FormTopMenu from "../../../../components/UI/Menu/FormTopMenu";
import RenameFormModal from "../../../../components/UI/Modals/RenameFormModal";

const StandartForm = () => {
  const url = window.location.href;
  const urlParts = url.split("/");
  const id = urlParts[urlParts.length - 1];

  const location = useLocation();
  const formId = location.state?.formId ? location.state?.formId : id;
  const form = location.state && location.state.form ? location.state.form : {};

  const [selectedHeaderOption, setSelectedHeaderOption] = useState(1);
  const [isOpenToolbox, setOpenToolbox] = useState(true);
  const [selectedSettingOption, setSelectedSettingOption] = useState("design");

  const [themeList, setThemeList] = useState([]);

  const [themeId, setThemeId] = useState();
  const [font, setFont] = useState();
  const [pageBackgroundImage, setPageBackgroundImage] = useState();
  const [pageBackgroundColor, setPageBackgroundColor] = useState("#FFFFFF");
  const [backgroundImage, setBackgroundImage] = useState();
  const [backgroundColor, setBackgroundColor] = useState("#FFFFFF");
  const [questionColor, setQuestionColor] = useState("#000000");
  const [answerColor, setAnswerColor] = useState("#000000");
  const [buttonColor, setButtonColor] = useState("#000000");
  const [buttonTextColor, setButtonTextColor] = useState("#FFFFFF");
  const [rounded, setRounded] = useState(1);

  const [selectedTheme, setSelectedTheme] = useState(null);

  const [standartForm, setStandartForm] = useState(form);
  const [originalForm, setOriginalForm] = useState(standartForm);
  const [usedCaptcha, setUsedCaptcha] = useState(false);
  const [usedGoogleAuthenticate, setUsedGoogleAuthenticate] = useState(false);
  const [usedWeb3, setUsedWeb3] = useState(false);
  const [usedWelcomePage, setUsedWelcomePage] = useState(false);
  const [welcomePageItem, setWelcomePageItem] = useState();
  const [usedThankyouPage, setUsedThankyouPage] = useState(false);
  const [thankyouPageItem, setThankyouPageItem] = useState();
  const [pages, setPages] = useState([]);
  const [selectedPage, setSelectedPage] = useState();
  const [draggingPage, setDraggingPage] = useState();

  const [selectedField, setSelectedField] = useState("");

  const [deletePageModal, setDeletePageModal] = useState(false);
  const [deletingPage, setDeletingPage] = useState();

  const [deleteFieldModal, setDeleteFieldModal] = useState(false);
  const [deletingField, setDeletingField] = useState();

  const [deleteWelcomePageModal, setDeleteWelcomePageModal] = useState(false);
  const [deleteThankyouPageModal, setDeleteThankyouPageModal] = useState(false);

  const [openedPreviewModal, setOpenedPreviewModal] = useState(false);

  const [creatingFormLoading, setCreatingFormLoading] = useState(false);
  const [formName, setFormName] = useState("My Form");
  const [isOpenedRenameModal, setOpenedRenameModal] = useState();
  const [renameError, setRenameError] = useState();

  const [openedCreateFormModal, setOpenedCreateFormModal] = useState(false);

  const [perspective, setPerspective] = useState("desktop");

  const [loading, setLoading] = useState(false);

  const [messageApi, contextHolder] = message.useMessage();

  const pageBottomRef = useRef();
  const welcomePageRef = useRef();
  const thankyouPageRef = useRef();

  const mainContext = useMainContext();

  const navigate = useNavigate();

  useEffect(() => {
    if (!originalForm.id && standartForm.id) {
      setOriginalForm(Object.assign(standartForm));
    }
  }, [standartForm]);

  useEffect(() => {
    if (formId) {
      async function getEditingFormById() {
        await findFormById();
      }
      getEditingFormById();
    } else {
      setStandartForm(form);
      setPages(form.pages);

      if (
        !selectedPage &&
        (selectedField.type !== "WelcomePageField" ||
          selectedField.type !== "ThankyouPageField")
      ) {
        setSelectedPage(form.pages[0]);
      }
    }
  }, []);

  useEffect(() => {
    async function getEditingFormById() {
      await findFormById();
    }
    getEditingFormById();
  }, [setStandartForm]);

  const findFormById = async () => {
    try {
      setLoading(true);
      let id;
      console.log("formId : ", formId);
      if (formId) {
        id = formId;
      } else {
        id = form.id;
      }
      const response = await getFormById(id);
      if (response.status === 200) {
        if (response.data.themeId) {
          await findSelectedThemeById(response.data.themeId);
        }
        setFormName(response.data.name);
        if (response.data.captcha) {
          setUsedCaptcha(true);
        }
        if (response.data.googleAuthenticate) {
          setUsedGoogleAuthenticate(true);
        }
        if (response.data.web3) {
          setUsedWeb3(true);
        }

        if (response.data.welcomePage) {
          setUsedWelcomePage(true);
          setWelcomePageItem(response.data.welcomePageItem);
        }
        if (response.data.thankyouPage) {
          setUsedThankyouPage(true);
          setThankyouPageItem(response.data.thankyouPageItem);
        }
        if (response.data.pages && response.data.pages.length > 0) {
          const tempPages = response.data.pages;
          const addedRefsToPages = pagesWithRef(tempPages);
          setPages(addedRefsToPages);
          setSelectedPage(addedRefsToPages[addedRefsToPages.length - 1]);
        }
        setStandartForm(response.data);
      }
    } catch (err) {
      console.log("getting form by id error : ", err);
      if (err.response.status === 403) {
        navigate("/notFound");
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!originalForm) {
      setOriginalForm(deepClone(standartForm));
    }
  }, [standartForm]);

  const findSelectedThemeById = async (id) => {
    try {
      setLoading(true);
      const response = await getThemeById(id);
      if (response.status === 200) {
        setSelectedTheme(response.data);
      }
    } catch (err) {
      console.log("getting selected theme by id error : ", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    isActivePublishButton();
  }, [standartForm]);

  const container = {
    width: isOpenToolbox ? "calc(100% - 650px)" : "calc(100% - 542px)",
    marginLeft: isOpenToolbox ? "30px" : "200px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    marginTop: "90px",
  };

  const saveChanges = async (tempForm) => {
    try {
      setLoading(true);
      tempForm.workspace = mainContext.selectedWorkspace;
      if (selectedTheme && selectedTheme.id) {
        tempForm.theme = selectedTheme;
      }
      tempForm.pages = pagesWithoutRef(tempForm.pages);
      const response = await saveForm(tempForm);

      if (response.status === 200) {
        const savedForm = response.data;
        const addedRefToPages = pagesWithRef(savedForm.pages);
        const updatedForm = { ...savedForm, pages: addedRefToPages };
        setPages(addedRefToPages);
        // setStandartForm(updatedForm);

        setSelectedPage(
          selectedPage
            ? selectedPage
            : addedRefToPages[addedRefToPages.length - 1]
        );
      }

      return response;
    } catch (err) {
      console.log("Saving changes error : ", err);
    } finally {
      setLoading(false);
    }
  };

  const saveOptions = async (options) => {
    try {
      const response = await updateOptions(options);
      if (response.status === 200) {
        const tempField = selectedField;
        tempField.options = response.data;
        const tempForm = { ...standartForm };
        const tempPage = { ...selectedPage };
        tempPage.items = [...selectedPage.items];
        tempPage.items.splice(tempField.order, 1, { ...tempField });
        tempForm.pages.splice(tempPage.order, 1, { ...tempPage });
        setStandartForm(tempForm);
      }
    } catch (err) {
      console.log("Updating options error : ", err);
    } finally {
    }
  };

  const addNewPage = async () => {
    const pageId = generatePageId();

    const page = {
      id: pageId,
      formId: standartForm.id,
      order: pages.length,
      items: [],
    };

    const tempForm = { ...standartForm };
    tempForm.workspace = mainContext.selectedWorkspace;
    tempForm.pages.push(page);
    tempForm.pages = pagesWithoutRef(tempForm.pages);

    const response = await saveChanges(tempForm);

    if (response.status === 200) {
      const addedRefToPages = pagesWithRef(tempForm.pages);
      setPages(addedRefToPages);
      const pageWithRef = addedRefToPages[addedRefToPages.length - 1];
      setSelectedPage(pageWithRef);
      setTimeout(() => {
        pageWithRef?.ref?.current?.scrollIntoView({
          behavior: "smooth",
        });
      }, 100);
    }
  };

  const selectPage = (page) => {
    setSelectedPage(page);
    /*
    if (selectedPage && selectedPage.id === page.id) {
      setSelectedPage(null);
    } else {
      setSelectedPage(page);
    }

    */
  };

  const handleDragAndDrop = async (results) => {
    setDraggingPage();
    const { source, destination, type } = results;

    if (!destination) return;

    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    )
      return;

    if (type === "group") {
      const reorderedPages = [...pages];

      const storeSourceIndex = source.index;
      const storeDestinatonIndex = destination.index;

      const [removedStore] = reorderedPages.splice(storeSourceIndex, 1);
      reorderedPages.splice(storeDestinatonIndex, 0, removedStore);

      reorderedPages.forEach((element, index) => {
        element.order = index;
      });

      const tempForm = standartForm;
      tempForm.pages = reorderedPages;
      await saveChanges(tempForm);
      const addedRefToPages = pagesWithRef(reorderedPages);

      const page = addedRefToPages.find(
        (p, index) => storeDestinatonIndex === index
      );
      setSelectedPage(page);
      return setPages(addedRefToPages);
    }

    const itemSourceIndex = source.index;
    const itemDestinationIndex = destination.index;

    const pageSourceIndex = pages.findIndex(
      (page) => page.id === source.droppableId
    );
    const pageDestinationIndex = pages.findIndex(
      (page) => page.id === destination.droppableId
    );
    const newSourceItems = [...pages[pageSourceIndex].items];
    const newDestinationItems =
      source.droppableId !== destination.droppableId
        ? [...(pages[pageDestinationIndex].items || [])]
        : newSourceItems;

    const [deletedItem] = newSourceItems.splice(itemSourceIndex, 1);
    newDestinationItems.splice(itemDestinationIndex, 0, deletedItem);

    const newPages = [...pages];

    newPages[pageSourceIndex] = {
      ...pages[pageSourceIndex],
      items: newSourceItems,
    };
    newPages[pageDestinationIndex] = {
      ...pages[pageDestinationIndex],
      items: newDestinationItems,
    };

    newPages.forEach((page) => {
      page.items?.forEach((item, index) => {
        item.pageId = page.id;
        item.order = index;
      });
    });
    const page = pages.find((p) => p.id === destination.droppableId);
    const tempForm = standartForm;
    tempForm.pages = newPages;
    await saveChanges(tempForm);
    const addedRefsToPages = pagesWithRef(tempForm.pages);
    const tempSelectedPage = addedRefsToPages.find((p) => p.id === page.id);
    setSelectedPage(tempSelectedPage);
    const tempSelectedField = tempSelectedPage.items.find(
      (field, index) => index === itemDestinationIndex
    );
    setSelectedField(tempSelectedField);

    setPages(addedRefsToPages);
  };

  const handleDragAndDropStart = (result) => {
    if (!result.draggableId.includes("Item")) {
      const page = pages.find((page) => page.id === result.draggableId);
      setSelectedPage(page);
      setDraggingPage(page);
    } else {
      const selectedItem = selectedPage.items.find(
        (item) => item.id === result.draggableId
      );
      setSelectedField(selectedItem);
    }
  };

  const gotoPage = (page) => {
    setSelectedField();
    setSelectedPage(page);
    page.ref?.current?.scrollIntoView({ block: "end", behavior: "smooth" });
  };

  const gotoWelcomePage = () => {
    selectField(standartForm.welcomePageItem);
    welcomePageRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const gotoThankyouPage = () => {
    selectField(standartForm.thankyouPageItem);
    thankyouPageRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const selectField = (field) => {
    setSelectedField(field);
    setSelectedSettingOption("");
    setTimeout(() => {
      setSelectedSettingOption("content");
    }, 100);
  };

  const editField = (field) => {
    if (
      field.type !== "WelcomePageField" &&
      field.type !== "ThankyouPageField"
    ) {
      const currentPage = pages.find((p) => p.id === field.pageId);
      const currentItem = currentPage.items.find((i) => i.id === field.id);
      currentPage.items.splice(currentItem.order, 1, field);
      setPages(pages);
      setTimeout(() => {
        setSelectedField(field);
      }, 100);
    } else {
      setTimeout(() => {
        setSelectedField(field);
      }, 100);
    }
  };

  const isDeletedCaptchaToPage = (page) => {
    page?.items?.forEach((field) => {
      if (field.type === "CaptchaField") {
        setUsedCaptcha(false);
      }
    });
  };

  const deletePage = async () => {
    try {
      setLoading(true);
      const response = await deletePageById(deletingPage.id);
      if (response.status === 200) {
        isDeletedCaptchaToPage(deletingPage);
        const tempPages = pages;
        tempPages.splice(deletingPage.order, 1);
        tempPages.forEach((page) => {
          if (page.order > deletingPage.order) {
            page.order = page.order - 1;
          }
        });
        const tempForm = standartForm;
        const addedRefsToPage = pagesWithRef(pages);
        tempForm.pages = addedRefsToPage;
        setPages(addedRefsToPage);
        setSelectedPage(addedRefsToPage[addedRefsToPage.length - 1]);
        setStandartForm(tempForm);
        setTimeout(() => {
          messageApi.open({
            type: "error",
            content: "Page is deleted.",
          });
        }, 100);
        setDeletingPage(null);
        setDeletePageModal(false);
      }
    } catch (err) {
      console.log("Deleting page error : ", err);
    } finally {
      setLoading(false);
    }
  };

  const openDeletePageModal = (page) => {
    setDeletingPage(page);
    setDeletePageModal(true);
  };

  const openDeleteFieldModal = async () => {
    if (selectedField.published) {
      setDeletingField(selectedField);
      setDeleteFieldModal(true);
    } else {
      await deleteField();
    }
  };

  const deleteField = async () => {
    const fields = [...selectedPage.items];
    try {
      setLoading(true);
      const response = await deleteFieldById(selectedField.id);
      if (response.status === 200) {
        const updatedFields = fields.filter(
          (field) => field.id !== selectedField.id
        );
        updatedFields.forEach((field) => {
          if (field.order > selectedField.order) {
            field.order = field.order - 1;
          }
        });
        const updatedPage = { ...selectedPage, items: updatedFields };
        setSelectedPage(updatedPage);
        const tempPages = [...pages];
        tempPages.splice(selectedPage.order, 1, updatedPage);
        setPages(tempPages);

        let tempForm;
        if (selectedField?.type === "CaptchaField") {
          tempForm = {
            ...standartForm,
            pages: tempPages,
            captcha: false,
          };
        } else {
          tempForm = {
            ...standartForm,
            pages: tempPages,
          };
        }
        try {
          const saveResponse = await saveChanges(tempForm);
          if (saveResponse.status === 200) {
            setStandartForm(saveResponse.data);
          }
        } catch (err) {
          console.log("Saving form after deleted field error : ", err);
        }
        messageApi.open({
          type: "success",
          content: "Field has been deleted successfully.",
        });

        setSelectedField(null);
        setSelectedSettingOption("design");
      } else {
        console.log("Field deletion failed:", response);
      }
    } catch (err) {
      console.log("Error during field deletion:", err);
    } finally {
      setLoading(false);
      setDeleteFieldModal(false);
    }
  };

  const deleteWelcomePage = async () => {
    if (standartForm.welcomePageItem) {
      try {
        setLoading(false);
        await deleteExternalPageById(
          standartForm.id,
          standartForm.welcomePageItem.id
        );
      } catch (err) {
        console.log("Deleting external page error : ", err);
      } finally {
        setLoading(false);
      }
    }
    setTimeout(() => {
      setUsedWelcomePage(false);
      setWelcomePageItem(null);
      setDeleteWelcomePageModal(false);
      const tempForm = standartForm;
      tempForm.welcomePage = false;
      setStandartForm(tempForm);
      messageApi.open({
        type: "error",
        content: "Welcome Page is deleted.",
      });
    }, 100);
  };

  const deleteThankyouPage = async () => {
    if (standartForm.thankyouPageItem) {
      try {
        setLoading(false);
        await deleteExternalPageById(
          standartForm.id,
          standartForm.thankyouPageItem.id
        );
      } catch (err) {
        console.log("Deleting external page error : ", err);
      } finally {
        setLoading(false);
      }
    }
    setTimeout(() => {
      setUsedThankyouPage(false);
      setThankyouPageItem(null);
      setDeleteThankyouPageModal(false);
      const tempForm = standartForm;
      tempForm.thankyouPage = false;
      setStandartForm(tempForm);
      messageApi.open({
        type: "error",
        content: "Thank You Page is deleted.",
      });
    }, 100);
  };

  const duplicateField = () => {
    const cloneField = structuredClone(selectedField);
    cloneField.id = generateFiedId();
    cloneField.order = cloneField.order + 1;
    selectedPage.items.forEach((field) => {
      if (field.order >= cloneField.order) {
        field.order = field.order + 1;
      }
    });
    selectedPage.items.push(cloneField);

    selectedPage.items.sort(function (a, b) {
      return a.order - b.order;
    });
    const tempPages = pages;
    tempPages.splice(selectedPage.order, 1, selectedPage);

    setTimeout(() => {
      setPages(tempPages);
      messageApi.open({
        type: "success",
        content: "Field duplicated.",
      });
      setSelectedField(cloneField);
    }, 100);
  };

  const createForm = (formName) => {
    setFormName(formName);
    const tempForm = standartForm;
    tempForm.name = formName;
    setStandartForm(tempForm);
  };

  const publish = async () => {
    try {
      setLoading(true);
      const response = await publishForm(standartForm.id);
      if (response.status === 200) {
        try {
          const formResponse = await getFormById(response.data.id);
          if (formResponse.status === 200) {
            setStandartForm(formResponse.data);
          }
        } catch (err) {
          console.log("Getting form by id error : ", err);
        }

        navigate(
          `/workspace/${mainContext.selectedWorkspace.uniqueId}/form/${standartForm.id}/share`,
          {
            state: { form: response.data, theme: selectedTheme },
          }
        );
      }
    } catch (err) {
      console.log("Publishing form error : ", err);
    } finally {
      setLoading(false);
    }
  };

  const isActivePublishButton = () => {
    if (standartForm && !standartForm.published) {
      return true;
    }
    if (standartForm && hasUnpublishedPage(standartForm.pages)) {
      console.log("emre2");
      return true;
    }
    if (standartForm && hasUnpublishedItem(standartForm)) {
      return true;
    }
    if (
      standartForm &&
      originalForm &&
      !equalPages(originalForm.pages, standartForm.pages)
    ) {
      console.log("standartForm : ", standartForm);
      console.log("originalForm : ", originalForm);
      return true;
    }
    if (
      standartForm &&
      originalForm &&
      !deepEqualItemsOnly(originalForm, standartForm)
    ) {
      console.log("emre5");
      return true;
    }
    console.log("emre6");
    return false;
  };

  const renameFormName = async (name) => {
    try {
      setLoading(true);

      const response = await renameFormById(standartForm.id, name);
      if (response.status === 200) {
        const tempForm = standartForm;
        tempForm.name = name;
        setStandartForm(tempForm);
        setTimeout(() => {
          messageApi.open({
            type: "success",
            content: "Updated form name.",
          });
        }, 100);
      } else {
        messageApi.open({
          type: "error",
          content: "An error occurred while updating the name of the form.",
        });
      }
    } catch (err) {
      console.log("renaming form error : ", err);
    } finally {
      setOpenedRenameModal(false);
      setLoading(false);
    }
  };

  return (
    <>
      {contextHolder}
      <Navbar />

      <div className="standartForm-container">
        <FieldToolbox
          loading={loading}
          setLoading={setLoading}
          isOpenToolbox={isOpenToolbox}
          handleOpen={setOpenToolbox}
          selectedPage={selectedPage}
          setSelectedPage={setSelectedPage}
          standartForm={standartForm}
          selectedTheme={selectedTheme}
          setStandartForm={setStandartForm}
          usedCaptcha={usedCaptcha}
          setUsedCaptcha={setUsedCaptcha}
          usedGoogleAuthenticate={usedGoogleAuthenticate}
          setUsedGoogleAuthenticate={setUsedGoogleAuthenticate}
          usedWeb3={usedWeb3}
          setUsedWeb3={setUsedWeb3}
          usedWelcomePage={usedWelcomePage}
          setUsedWelcomePage={setUsedWelcomePage}
          setWelcomePageItem={setWelcomePageItem}
          usedThankyouPage={usedThankyouPage}
          setUsedThankyouPage={setUsedThankyouPage}
          setThankyouPageItem={setThankyouPageItem}
          pages={pages}
          setPages={setPages}
          selectedField={selectedField}
          setSelectedField={setSelectedField}
          setSelectedSettingOption={setSelectedSettingOption}
          selectField={selectField}
          welcomePageRef={welcomePageRef}
          thankyouPageRef={thankyouPageRef}
        />
        <FormTopMenu selectedItem="build" form={standartForm} />
        <div style={container} className="standartForm-inner-container">
          <FormPublishSettings
            standartForm={standartForm}
            loading={loading}
            saveChanges={saveChanges}
            setStandartForm={setStandartForm}
            usedGoogleAuthenticate={usedGoogleAuthenticate}
            setUsedGoogleAuthenticate={setUsedGoogleAuthenticate}
            usedWeb3={usedWeb3}
            setUsedWeb3={setUsedWeb3}
            setOpenedPreviewModal={setOpenedPreviewModal}
            formName={standartForm ? standartForm.name : ""}
            setOpenedRenameModal={setOpenedRenameModal}
            publishForm={publish}
            isEdited={formId ? true : false}
            isActivePublishButton={isActivePublishButton}
          />
          <div
            className="standartForm-form-container"
            style={{
              backgroundImage:
                selectedTheme && selectedTheme.pageBackgroundImage
                  ? "url(" + selectedTheme.pageBackgroundImage + ")"
                  : "",
              backgroundColor:
                selectedTheme &&
                !selectedTheme.pageBackgroundImage &&
                selectedTheme.pageBackgroundColor
                  ? selectedTheme.pageBackgroundColor
                  : "#FFFFFF",
            }}
          >
            {usedWelcomePage && (
              <div
                ref={welcomePageRef}
                style={{
                  display: "flex",
                  justifyContent: "center",
                  width: "100%",
                }}
              >
                <WelcomePage
                  field={welcomePageItem}
                  selectField={selectField}
                  selectedField={selectedField}
                  setSelectedField={setSelectedField}
                  selectedTheme={selectedTheme}
                  setDeleteWelcomePageModal={setDeleteWelcomePageModal}
                />
              </div>
            )}
            <DragDropContext
              onDragEnd={handleDragAndDrop}
              onDragStart={handleDragAndDropStart}
            >
              {" "}
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  width: "100%",
                }}
              >
                <Droppable droppableId="ROOT" type="group">
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef}>
                      {pages.map((page, index) => (
                        <Draggable
                          draggableId={page.id}
                          key={page.id}
                          index={index}
                        >
                          {(provided) => (
                            <div
                              {...provided.dragHandleProps}
                              {...provided.draggableProps}
                              ref={provided.innerRef}
                            >
                              <FormCanvas
                                loading={loading}
                                setLoading={setLoading}
                                index={index}
                                standartForm={standartForm}
                                setStandartForm={setStandartForm}
                                pages={pages}
                                setPages={setPages}
                                page={page}
                                draggingPage={draggingPage}
                                selectedPage={selectedPage}
                                setSelectedPage={setSelectedPage}
                                selectPage={selectPage}
                                selectedField={selectedField}
                                setSelectedField={setSelectedField}
                                selectedSettingOption={selectedSettingOption}
                                setSelectedSettingOption={
                                  setSelectedSettingOption
                                }
                                selectField={selectField}
                                selectedTheme={selectedTheme}
                                openDeletePageModal={openDeletePageModal}
                                openDeleteFieldModal={openDeleteFieldModal}
                                duplicateField={duplicateField}
                                saveOptions={saveOptions}
                              />
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </div>
              <div ref={pageBottomRef}></div>
            </DragDropContext>
            {usedThankyouPage && thankyouPageItem && (
              <div
                ref={thankyouPageRef}
                style={{
                  display: "flex",
                  justifyContent: "center",
                  width: "100%",
                }}
              >
                <ThankyouPage
                  field={thankyouPageItem}
                  selectField={selectField}
                  selectedField={selectedField}
                  setSelectedField={setSelectedField}
                  selectedTheme={selectedTheme}
                  setDeleteThankyouPageModal={setDeleteThankyouPageModal}
                />
              </div>
            )}
          </div>
          <div className="standartForm-addNewPage-container">
            <div
              className="standartForm-addNewPage-button"
              onClick={addNewPage}
            >
              ADD NEW PAGE
            </div>
          </div>
        </div>
        {usedWelcomePage && welcomePageItem && (
          <div
            className="standartForm-display-welcomePage-container"
            style={{
              borderTop:
                selectedField && selectedField.type === "WelcomePageField"
                  ? "3px solid var(--black)"
                  : "",
            }}
            onClick={() => gotoWelcomePage()}
          >
            <div className="standartForm-display-welcomePage">
              <MdFirstPage />
            </div>
          </div>
        )}
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          {((pages && pages.length > 1) ||
            usedWelcomePage ||
            usedThankyouPage) && (
            <div
              className="standartForm-display-pages-container"
              style={{ left: usedWelcomePage ? "370px" : "" }}
            >
              {pages.map((page, index) => (
                <div
                  key={index}
                  className={
                    selectedPage?.id === page?.id
                      ? "standartForm-display-page-selected"
                      : "standartForm-display-page"
                  }
                  style={{
                    marginRight: pages.length - 1 !== index ? "10px" : "0",
                  }}
                  onClick={() => gotoPage(page)}
                >
                  Page {index + 1}
                </div>
              ))}
            </div>
          )}
          {usedThankyouPage && (
            <div
              className="standartForm-display-thankyouPage-container"
              style={{
                borderTop:
                  selectedField && selectedField.type === "ThankyouPageField"
                    ? "3px solid var(--black)"
                    : "",
              }}
              onClick={() => gotoThankyouPage()}
            >
              <div className="standartForm-display-thankyouPage">
                <MdLastPage />
              </div>
            </div>
          )}
          {/*
          <div className="standartForm-addNewPage" onClick={addNewPage}>
            <div className="standartForm-addNewPage-title">ADD NEW PAGE</div>
            <div />
          </div>
          */}
        </div>

        <FormSettings
          setLoading={setLoading}
          standartForm={standartForm}
          setStandartForm={setStandartForm}
          saveChanges={saveChanges}
          selectedField={selectedField}
          setSelectedField={setSelectedField}
          editField={editField}
          selectedTheme={selectedTheme}
          setSelectedTheme={setSelectedTheme}
          themeList={themeList}
          setThemeList={setThemeList}
          themeId={themeId}
          setThemeId={setThemeId}
          font={font}
          setFont={setFont}
          pageBackgroundImage={pageBackgroundImage}
          setPageBackgroundImage={setPageBackgroundImage}
          pageBackgroundColor={pageBackgroundColor}
          setPageBackgroundColor={setPageBackgroundColor}
          backgroundImage={backgroundImage}
          setBackgroundImage={setBackgroundImage}
          backgroundColor={backgroundColor}
          setBackgroundColor={setBackgroundColor}
          questionColor={questionColor}
          setQuestionColor={setQuestionColor}
          answerColor={answerColor}
          setAnswerColor={setAnswerColor}
          buttonColor={buttonColor}
          setButtonColor={setButtonColor}
          buttonTextColor={buttonTextColor}
          setButtonTextColor={setButtonTextColor}
          rounded={rounded}
          setRounded={setRounded}
          selectedSettingOption={selectedSettingOption}
          setSelectedSettingOption={setSelectedSettingOption}
        />
      </div>

      <RenameFormModal
        open={isOpenedRenameModal}
        handleOk={renameFormName}
        handleClose={() => setOpenedRenameModal(false)}
        title="Rename this form"
        inputValue={formName}
        buttonTitle="Save"
        loading={loading}
        error={renameError}
        setError={setRenameError}
      />

      <ErrorModalWithSelection
        open={deletePageModal}
        handleClose={() => setDeletePageModal(false)}
        title="Are you sure you want to delete the page?"
        message="If you confirm, this page and all associated data will be permanently deleted."
        message2="This action cannot be undone."
        buttonTitle="Delete Page"
        buttonAction={deletePage}
      />

      <ErrorModalWithSelection
        open={deleteFieldModal}
        handleClose={() => setDeleteFieldModal(false)}
        title="Are you sure you want to delete the field?"
        message="If you confirm, this field and all associated data will be permanently deleted."
        message2="This action cannot be undone."
        buttonTitle="Delete Field"
        buttonAction={deleteField}
      />

      <ErrorModalWithSelection
        open={deleteWelcomePageModal}
        handleClose={() => setDeleteWelcomePageModal(false)}
        title="Are you sure you want to delete welcome page?"
        message="If you confirm, welcome page will be deleted."
        buttonTitle="Delete Welcome Page"
        buttonAction={deleteWelcomePage}
      />

      <ErrorModalWithSelection
        open={deleteThankyouPageModal}
        handleClose={() => setDeleteThankyouPageModal(false)}
        title="Are you sure you want to delete thank you page?"
        message="If you confirm, thank you page will be deleted."
        buttonTitle="Delete Thank You Page"
        buttonAction={deleteThankyouPage}
      />

      <CreateFormModal
        open={openedCreateFormModal}
        handleClose={() => setOpenedCreateFormModal(false)}
        title="Create form"
        message="Please enter name for form"
        buttonTitle="Create Form"
        buttonAction={createForm}
        loading={creatingFormLoading}
      />

      <PreviewModal
        open={openedPreviewModal}
        handleClose={setOpenedPreviewModal}
        perspective={perspective}
        setPerspective={setPerspective}
      >
        <FForm
          form={{ ...standartForm }}
          theme={selectedTheme}
          isPreview={true}
          isOpen={openedPreviewModal}
          perspective={perspective}
        />
      </PreviewModal>
    </>
  );
};

export default StandartForm;
