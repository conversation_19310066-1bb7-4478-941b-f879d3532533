.standartForm-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background-color: var(--bg-light);
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.standartForm-form-container {
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  margin-bottom: 90px;
  flex-direction: column;
  justify-content: center;
  /* padding: 50px 70px 30px 120px;*/
  background-color: var(--white);
  /*box-shadow: rgba(0, 0, 0, 0.24) 0px 1px 4px;*/
  height: 100%;
  background-repeat: round;
  background-size: cover;
  border: 1px solid var(--grey300);
}

@media screen and (width < 1600px) {
  .standartForm-form-container {
    padding: 50px 50px 30px 80px;
  }
}

@media screen and (width < 1400px) {
  .standartForm-form-container {
    /*padding: 50px 30px 30px 60px;*/
  }
}

@media screen and (width < 1250px) {
  .standartForm-form-container {
    /*padding: 50px 20px 30px 90px;*/
  }
}

.standartForm-display-pages-container {
  position: fixed;
  display: flex;
  left: 300px;
  bottom: 0;
  margin-bottom: 13px;
  max-width: calc(100% - 640px);
  overflow-x: auto;
}

.standartForm-display-welcomePage-container {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  left: 310px;
  bottom: 0;
  margin-bottom: 13px;
  max-width: 700px;
  overflow-x: auto;
  background-color: var(--white);
  border: 1px solid var(--grey200);
  padding: 0px 5px;
  border-radius: 5px;
}

.standartForm-display-page {
  /*background-color: var(--grey300);*/
  padding: 5px;
  /*border: 1px solid var(--grey100);*/
  min-width: 55px !important;
  width: 60px;
  display: flex;
  justify-content: center;
  align-content: center;
  cursor: pointer;
  font-size: 12px;
}

.standartForm-display-welcomePage {
  padding: 5px;
  width: 35px;
  display: flex;
  justify-content: center;
  align-content: center;
  cursor: pointer;
  font-size: 24px;
}

.standartForm-display-thankyouPage-container {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  right: 550px;
  bottom: 0;
  margin-bottom: 13px;
  max-width: 700px;
  overflow-x: auto;
  background-color: var(--white);
  border: 1px solid var(--grey200);
  padding: 0px 5px;
  border-radius: 5px;
}

.standartForm-display-thankyouPage {
  padding: 5px;
  width: 35px;
  display: flex;
  justify-content: center;
  align-content: center;
  cursor: pointer;
  font-size: 24px;
}

.standartForm-display-page-selected {
  padding: 5px;
  min-width: 65px !important;
  width: 65px;
  display: flex;
  justify-content: center;
  align-content: center;
  cursor: pointer;
  font-size: 14px;
  border-top: 2px solid var(--black);
}

.standartForm-addNewPage-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  bottom: 75px;
}

.standartForm-addNewPage-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: fit-content;
  padding: 4px 30px 5px 30px;
  background-color: var(--grey300);
  color: var(--black);
  font-family: "Exo 2", sans-serif;
  font-weight: 600;
  letter-spacing: 0.7px;
  font-size: 14px;
  border: 1px solid var(--grey100);
  border-radius: 5px;
  cursor: pointer;
}

.standartForm-addNewPage-button:hover {
  background-color: var(--bg-light);
  transition: 0.3s all;
}

.standartForm-addNewPage {
  position: fixed;
  right: 350px;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 180px;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 8px;
  border: 1px solid var(--black);
  border-bottom: 5px solid var(--black);
  background-color: var(--white);
  color: var(--white);
  cursor: pointer;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 15px;
  margin-bottom: 20px;
  padding: 5px;
  border-radius: 30px;
}

.standartForm-addNewPage:hover {
  margin-top: 4px;
  border: 1px solid var(--black);
  transition: 0.15s all;
}

.standartForm-addNewPage-icon {
  background-color: var(--white);
  padding: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--black);
  color: var(--white);
  font-size: 24px;
}

.standartForm-addNewPage-title {
  color: var(--black);
}
