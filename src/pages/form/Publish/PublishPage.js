import { useEffect, useState } from "react";
import Leftside from "../../../components/UI/Form/Publish/Leftside/Leftside";
import Navbar from "../../../components/UI/Navbar/Navbar";
import "./PublishPage.css";
import { getFormById, getThemeById } from "../../../services/http";
import { useLocation, useParams } from "react-router-dom";
import SmallLoading from "../../../components/UI/Loading/SmallLoading";
import Middleside from "../../../components/UI/Form/Publish/Middleside/Middleside";
import Rightside from "../../../components/UI/Form/Publish/Rightside/Rightside";
import FormTopMenu from "../../../components/UI/Menu/FormTopMenu";
import UnpublishedForm from "../../../components/UI/Warning/UnpublishedForm";

const PublishPage = () => {
  const location = useLocation();
  const { formId } = location.state || null;
  const { form } = location.state || null;
  const { theme } = location.state || null;

  const [formState, setFormState] = useState();
  const [themeState, setThemeState] = useState();
  const [selectedLeftsideMenuItem, setSelectedLeftsideMenuItem] =
    useState("shareLink");
  const [selectedPreview, setSelectedPreview] = useState("desktop");

  const [emailSenderName, setEmailSenderName] = useState("Emre Büyükeğribey");
  const [emailSenderEmail, setEmailSenderEmail] = useState(
    "<EMAIL>"
  );
  const [emailToList, setEmailToList] = useState([]);

  const [triggerAction, setTriggerAction] = useState(false);
  const [embedMode, setEmbedMode] = useState("inPage");

  const [loading, setLoading] = useState(false);

  /* InPageEmbed */
  const [isInPageProcess, setInPageProcess] = useState(false);
  const [inPageWidth, setInPageWidth] = useState(100);
  const [inPageWidthType, setInPageWidthType] = useState("percent");
  const [inPageHeight, setInPageHeight] = useState(500);
  const [inPageHeightType, setInPageHeightType] = useState("pixel");
  /* END InPageEmbed */

  /* IFrameEmbed */
  const [isIFrameProcess, setIFrameProcess] = useState(false);
  const [iFrameWidth, setIFrameWith] = useState(100);
  const [iFrameWidthType, setIFrameWithType] = useState("percent");
  const [iFrameHeight, setIFrameHeight] = useState(500);
  const [iFrameHeightType, setIFrameHeightType] = useState("pixel");
  /* --- END IFrameEmbed --- */

  /* DrawerEmbed */
  const [isDrawerProcess, setDrawerProcess] = useState(false);
  const [drawerButtonText, setDrawerButtonText] = useState("Feedback");
  const [drawerButtonBackground, setDrawerButtonBackground] = useState("#000");
  const [drawerButtonTextColor, setDrawerButtonTextColor] = useState("#FFF");
  const [drawerPosition, setDrawerPosition] = useState("left");
  /* --- END DrawerEmbed --- */

  /* Popup Embed */
  const [isPopupProcess, setPopupProcess] = useState(false);
  const [popupButtonText, setPopupButtonText] = useState("Form");
  const [popupButtonBackground, setPopupButtonBackground] = useState("#000");
  const [popupButtonTextColor, setPopupButtonTextColor] = useState("#FFF");
  /* --- END Popup Embed --- */

  /* Popover Embed */
  const [isPopoverProcess, setPopoverProcess] = useState(false);
  const [popoverButtonBackground, setPopoverButtonBackground] =
    useState("#000");
  const [popoverIconColor, setPopoverIconColor] = useState("#FFF");

  useEffect(() => {
    if (form) {
      setFormState(form);
    }

    if (!form && formId) {
      async function getEditingFormById() {
        await findFormById();
      }
      getEditingFormById();
    }
  }, [form, formId, findFormById]);

  const findFormById = async () => {
    try {
      setLoading(true);
      const response = await getFormById(formId);
      if (response.status === 200) {
        if (response.data.themeId) {
          await findSelectedThemeById(response.data.themeId);
        }
        setFormState(response.data);
      }
    } catch (err) {
      console.log("getting form by id error : ", err);
    } finally {
      setLoading(false);
    }
  };

  const findSelectedThemeById = async (id) => {
    try {
      setLoading(true);
      const response = await getThemeById(id);
      if (response.status === 200) {
        setThemeState(response.data);
      }
    } catch (err) {
      console.log("getting selected theme by id error : ", err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <SmallLoading />;
  }

  return (
    <>
      <Navbar />
      <FormTopMenu selectedItem="share" form={formState} />
      <div className="publishPage-outer-container">
        {!form?.published && <UnpublishedForm />}
        <div className="publishPage-container">
          <Leftside
            selectedLeftsideMenuItem={selectedLeftsideMenuItem}
            setSelectedLeftsideMenuItem={setSelectedLeftsideMenuItem}
          />
          {formState && (
            <Middleside
              form={form ? form : formState}
              theme={theme ? theme : themeState}
              selectedPreview={selectedPreview}
              setSelectedPreview={setSelectedPreview}
              selectedLeftsideMenuItem={selectedLeftsideMenuItem}
              emailSenderName={emailSenderName}
              emailSenderEmail={emailSenderEmail}
              emailToList={emailToList}
              setEmailToList={setEmailToList}
              setLoading={setLoading}
              triggerAction={triggerAction}
              setTriggerAction={setTriggerAction}
              embedMode={embedMode}
              isInPageProcess={isInPageProcess}
              setInPageProcess={setInPageProcess}
              inPageWidth={inPageWidth}
              inPageWidthType={inPageWidthType}
              inPageHeight={inPageHeight}
              inPageHeightType={inPageHeightType}
              isDrawerProcess={isDrawerProcess}
              setDrawerProcess={setDrawerProcess}
              drawerButtonText={drawerButtonText}
              drawerButtonBackground={drawerButtonBackground}
              drawerButtonTextColor={drawerButtonTextColor}
              drawerPosition={drawerPosition}
              isIFrameProcess={isIFrameProcess}
              setIFrameProcess={setIFrameProcess}
              iFrameWidth={iFrameWidth}
              iFrameWidthType={iFrameWidthType}
              iFrameHeight={iFrameHeight}
              iFrameHeightType={iFrameHeightType}
              isPopupProcess={isPopupProcess}
              setPopupProcess={setPopupProcess}
              popupButtonText={popupButtonText}
              popupButtonBackground={popupButtonBackground}
              popupButtonTextColor={popupButtonTextColor}
              isPopoverProcess={isPopoverProcess}
              setPopoverProcess={setPopoverProcess}
              popoverButtonBackground={popoverButtonBackground}
              popoverIconColor={popoverIconColor}
            />
          )}
          <Rightside
            formId={formId}
            selectedLeftsideMenuItem={selectedLeftsideMenuItem}
            emailSenderName={emailSenderName}
            setEmailSenderName={setEmailSenderName}
            emailSenderEmail={emailSenderEmail}
            setEmailSenderEmail={setEmailSenderEmail}
            emailToList={emailToList}
            setEmailToList={setEmailToList}
            setLoading={setLoading}
            triggerAction={triggerAction}
            setTriggerAction={setTriggerAction}
            embedMode={embedMode}
            setEmbedMode={setEmbedMode}
            setDrawerProcess={setDrawerProcess}
            drawerButtonText={drawerButtonText}
            setDrawerButtonText={setDrawerButtonText}
            drawerButtonBackground={drawerButtonBackground}
            setDrawerButtonBackground={setDrawerButtonBackground}
            drawerButtonTextColor={drawerButtonTextColor}
            setDrawerButtonTextColor={setDrawerButtonTextColor}
            setDrawerPosition={setDrawerPosition}
            isInPageProcess={isInPageProcess}
            setInPageProcess={setInPageProcess}
            inPageWidth={inPageWidth}
            setInPageWidth={setInPageWidth}
            inPageWidthType={inPageWidthType}
            setInPageWidthType={setInPageWidthType}
            inPageHeight={inPageHeight}
            setInPageHeight={setInPageHeight}
            inPageHeightType={inPageHeightType}
            setInPageHeightType={setInPageHeightType}
            isIFrameProcess={isIFrameProcess}
            setIFrameProcess={setIFrameProcess}
            iFrameWidth={iFrameWidth}
            setIFrameWith={setIFrameWith}
            iFrameWidthType={iFrameWidthType}
            setIFrameWithType={setIFrameWithType}
            iFrameHeight={iFrameHeight}
            setIFrameHeight={setIFrameHeight}
            iFrameHeightType={iFrameHeightType}
            setIFrameHeightType={setIFrameHeightType}
            isPopupProcess={isPopupProcess}
            setPopupProcess={setPopupProcess}
            popupButtonText={popupButtonText}
            setPopupButtonText={setPopupButtonText}
            popupButtonBackground={popupButtonBackground}
            setPopupButtonBackground={setPopupButtonBackground}
            popupButtonTextColor={popupButtonTextColor}
            setPopupButtonTextColor={setPopupButtonTextColor}
            isPopoverProcess={isPopoverProcess}
            setPopoverProcess={setPopoverProcess}
            popoverButtonBackground={popoverButtonBackground}
            setPopoverButtonBackground={setPopoverButtonBackground}
            popoverIconColor={popoverIconColor}
            setPopoverIconColor={setPopoverIconColor}
          />
        </div>
      </div>
    </>
  );
};

export default PublishPage;
