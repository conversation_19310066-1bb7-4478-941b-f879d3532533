import { BsCalendar3Event } from "react-icons/bs";
import "./Analytics.css";
import { useEffect, useRef, useState } from "react";
import { IoIosArrowDown, IoMdClose } from "react-icons/io";
import { DayPicker } from "react-day-picker";
import Line<PERSON><PERSON> from "./LineChart";
import {
  MdOutlineDesktopWindows,
  MdOutlineDevices,
  MdOutlineShowChart,
} from "react-icons/md";
import { Col, Popover, Row } from "antd";
import ReactCountryFlag from "react-country-flag";
import { HiMiniDevicePhoneMobile } from "react-icons/hi2";
import { FiTablet } from "react-icons/fi";
import { format, parseISO, startOfDay, subDays } from "date-fns";

import SmallLoading from "../../../components/UI/Loading/SmallLoading";

const Analytics = ({
  views,
  setViews,
  responses,
  setResponses,
  viewResponse,
}) => {
  const [loading, setLoading] = useState(false);
  const [originalViewsData, setOriginalViewsData] = useState(views);
  const [originalResponsesData, setOriginalResponsesData] = useState(responses);
  const [selectedAnalyticOption, setSelectedAnalyticOption] = useState("views");
  const [openedAllTime, setOpenedAllTime] = useState(false);
  const [openedAllDevices, setOpenedAllDevices] = useState(false);
  const [isActiveAllTimeFilter, setActiveAllTimeFilter] = useState(false);
  const [allTimeTitle, setAllTimeTitle] = useState("All time");
  const [allDeviceTitle, setAllDeviceTitle] = useState("All devices");
  const [isActiveAllDevicesFilter, setActiveAllDevicesFilter] = useState(false);
  const [selectedAllTimeOption, setSelectedAllTimeOption] = useState();
  const [selectedAllDevicesOption, setSelectedAllDevicesOption] = useState();
  const [sortedViews, setSortedViews] = useState([]);
  const [sortedResponses, setSortedResponses] = useState([]);
  const [dayPickerDate, setDayPickerDate] = useState();
  const [isOpenedChart, setOpenedChart] = useState(true);
  const openedAllTimeRef = useRef();
  const openedAllDevicesRef = useRef();

  useEffect(() => {
    document.addEventListener("mousedown", handleOutsideAllTime);

    return () => {
      document.removeEventListener("mousedown", handleOutsideAllTime);
    };
  }, []);

  useEffect(() => {
    document.addEventListener("mousedown", handleOutsideAllDevices);

    return () => {
      document.removeEventListener("mousedown", handleOutsideAllDevices);
    };
  }, []);

  const handleOutsideAllTime = (event) => {
    if (
      openedAllTimeRef.current &&
      event.target.className !== "analtytics-top-menu-allTime-item" &&
      event.target.className !== "analtytics-top-menu-allTime-item-icon" &&
      event.target.className !== "analtytics-top-menu-allTime-item-title" &&
      event.target.className !== "analtytics-top-menu-filter-allTime-opened" &&
      event.target.className !== "" &&
      event.target.className.baseVal !== "" &&
      !openedAllTimeRef.current.contains(event.target)
    ) {
      setOpenedAllTime(false);
    }
  };

  const handleOutsideAllDevices = (event) => {
    if (
      openedAllDevicesRef.current &&
      event.target.className !== "analtytics-top-menu-allDevice-item" &&
      event.target.className !== "analtytics-top-menu-allDevice-item-icon" &&
      event.target.className !== "analtytics-top-menu-allDevice-item-title" &&
      event.target.className !==
        "analtytics-top-menu-filter-allDevice-opened" &&
      event.target.className !== "" &&
      event.target.className.baseVal !== "" &&
      !openedAllDevicesRef.current.contains(event.target)
    ) {
      setOpenedAllDevices(false);
    }
  };

  useEffect(() => {
    let tempSortedViews;
    let tempSortedResponses;

    if (views) {
      tempSortedViews = views.sort((a, b) => {
        const dateA = new Date(a.createdDate);
        const dateB = new Date(b.createdDate);
        return dateB - dateA;
      });
    }

    if (responses) {
      tempSortedResponses = responses.sort(function (a, b) {
        const dateA = new Date(a.createdDate);
        const dateB = new Date(b.createdDate);
        return dateB - dateA;
      });
      setSortedResponses(tempSortedResponses);
    }

    setSortedViews(tempSortedViews);
    setSortedResponses(tempSortedResponses);
  }, [views, responses]);

  const calculateTimeToComplete = () => {
    let totalDifference = 0;
    let count = responses?.length;
    if (responses && responses.length > 0) {
      responses.forEach((response) => {
        const startedDate = new Date(response.startedDate);
        const endedDate = new Date(response.endedDate);

        const differenceInMilliseconds = endedDate - startedDate;
        const differenceInSeconds = differenceInMilliseconds / 1000;

        totalDifference += differenceInSeconds;
      });
      const averageDifferenceInSeconds = totalDifference / count;

      const hours = Math.floor(averageDifferenceInSeconds / 3600);
      const minutes = Math.floor((averageDifferenceInSeconds % 3600) / 60);
      const seconds = Math.round(averageDifferenceInSeconds % 60);

      return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
        2,
        "0"
      )}:${String(seconds).padStart(2, "0")}`;
    }
    return "N/A";
  };

  function timeAgo(createdDate) {
    const pastDate = new Date(createdDate);
    const now = new Date();
    const secondsAgo = Math.floor((now - pastDate) / 1000);

    const intervals = [
      { label: "year", seconds: 31536000 },
      { label: "month", seconds: 2592000 },
      { label: "week", seconds: 604800 },
      { label: "day", seconds: 86400 },
      { label: "hour", seconds: 3600 },
      { label: "minute", seconds: 60 },
      { label: "second", seconds: 1 },
    ];

    for (const interval of intervals) {
      const count = Math.floor(secondsAgo / interval.seconds);
      if (count >= 1) {
        return `${count} ${interval.label}${count > 1 ? "s" : ""} ago`;
      }
    }

    return "just now";
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString);

    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();

    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
  };

  const clearAllTimeFilter = () => {
    setLoading(true);
    setActiveAllTimeFilter(false);
    setAllTimeTitle("");
    setViews([...originalViewsData]);
    setResponses([...originalResponsesData]);
    if (isActiveAllDevicesFilter && selectedAllDevicesOption) {
      applyDeviceFilter(true);
    }
    setTimeout(() => {
      setLoading(false);
    }, 300);
  };

  const clearDeviceFilter = () => {
    setLoading(true);
    setActiveAllDevicesFilter(false);
    setViews([...originalViewsData]);
    setResponses([...originalResponsesData]);
    if (isActiveAllTimeFilter && (selectedAllTimeOption || dayPickerDate)) {
      applyAllTimeFilter(true);
    }
    setTimeout(() => {
      setLoading(false);
    }, 300);
  };

  const onSelectDayPicker = (dates) => {
    setSelectedAllTimeOption();
    setDayPickerDate(dates);
  };

  const applyAllTimeFilter = (fromClear) => {
    setLoading(true);
    let tempFilteredViewsData = [...originalViewsData];
    let tempFilteredResponsesData = [...originalResponsesData];
    if (isActiveAllDevicesFilter && selectedAllDevicesOption) {
      tempFilteredViewsData = [...views];
      tempFilteredResponsesData = [...responses];
    }
    if (fromClear) {
      tempFilteredViewsData = [...originalViewsData];
      tempFilteredResponsesData = [...originalResponsesData];
    }

    const todayDate = getTodayDate();
    if (selectedAllTimeOption === "today") {
      tempFilteredViewsData = tempFilteredViewsData.filter((response) => {
        const createdDate = parseISO(response.createdDate);
        const startOfDayCreatedDate = startOfDay(createdDate);
        setAllTimeTitle("Today");
        return startOfDayCreatedDate >= todayDate;
      });
      tempFilteredResponsesData = tempFilteredResponsesData.filter(
        (response) => {
          const createdDate = parseISO(response.createdDate);
          const startOfDayCreatedDate = startOfDay(createdDate);
          setAllTimeTitle("Today");
          return startOfDayCreatedDate >= todayDate;
        }
      );
    } else if (selectedAllTimeOption === "thisWeek") {
      tempFilteredViewsData = tempFilteredViewsData.filter((response) => {
        const createdDate = parseISO(response.createdDate);
        const sevenDaysAgo = getThisWeekDate();
        const formattedDate = format(sevenDaysAgo, "MMM dd, yyyy");
        setAllTimeTitle(`${formattedDate} - Today`);

        return createdDate >= sevenDaysAgo;
      });
      tempFilteredResponsesData = tempFilteredResponsesData.filter(
        (response) => {
          const createdDate = parseISO(response.createdDate);
          const sevenDaysAgo = getThisWeekDate();
          const formattedDate = format(sevenDaysAgo, "MMM dd, yyyy");
          setAllTimeTitle(`${formattedDate} - Today`);

          return createdDate >= sevenDaysAgo;
        }
      );
    } else if (selectedAllTimeOption === "thisMonth") {
      tempFilteredViewsData = tempFilteredViewsData.filter((response) => {
        const createdDate = parseISO(response.createdDate);
        const oneMonthAgo = getThisMonth();
        const formattedDate = format(oneMonthAgo, "MMM dd, yyyy");
        setAllTimeTitle(`${formattedDate} - Today`);

        return createdDate >= oneMonthAgo;
      });
      tempFilteredResponsesData = tempFilteredResponsesData.filter(
        (response) => {
          const createdDate = parseISO(response.createdDate);
          const oneMonthAgo = getThisMonth();
          const formattedDate = format(oneMonthAgo, "MMM dd, yyyy");
          setAllTimeTitle(`${formattedDate} - Today`);

          return createdDate >= oneMonthAgo;
        }
      );
    } else if (selectedAllTimeOption === "thisYear") {
      tempFilteredViewsData = tempFilteredViewsData.filter((response) => {
        const createdDate = parseISO(response.createdDate);
        const oneYearAgo = getThisYear();
        const formattedDate = format(oneYearAgo, "MMM dd, yyyy");
        setAllTimeTitle(`${formattedDate} - Today`);

        return createdDate >= oneYearAgo;
      });
      tempFilteredResponsesData = tempFilteredResponsesData.filter(
        (response) => {
          const createdDate = parseISO(response.createdDate);
          const oneYearAgo = getThisYear();
          const formattedDate = format(oneYearAgo, "MMM dd, yyyy");
          setAllTimeTitle(`${formattedDate} - Today`);

          return createdDate >= oneYearAgo;
        }
      );
    } else {
      const { from, to } = dayPickerDate;
      tempFilteredViewsData = tempFilteredViewsData.filter((response) => {
        const createdDate = parseISO(response.createdDate);

        const formattedFromDate = format(
          from.setHours(0, 0, 0, 0),
          "MMM dd, yyyy"
        );
        const formattedToDate = format(
          to.setHours(23, 59, 59, 999),
          "MMM dd, yyyy"
        );

        setAllTimeTitle(`${formattedFromDate} - ${formattedToDate}`);

        return createdDate >= from && createdDate <= to;
      });
      tempFilteredResponsesData = tempFilteredResponsesData.filter(
        (response) => {
          const createdDate = parseISO(response.createdDate);

          const formattedFromDate = format(
            from.setHours(0, 0, 0, 0),
            "MMM dd, yyyy"
          );
          const formattedToDate = format(
            to.setHours(23, 59, 59, 999),
            "MMM dd, yyyy"
          );

          setAllTimeTitle(`${formattedFromDate} - ${formattedToDate}`);

          return createdDate >= from && createdDate <= to;
        }
      );
    }

    setActiveAllTimeFilter(true);
    setViews(tempFilteredViewsData);
    setResponses(tempFilteredResponsesData);
    setOpenedAllTime(false);
    setTimeout(() => {
      setLoading(false);
    }, 300);
  };

  const applyDeviceFilter = (fromClear) => {
    setLoading(true);
    let tempFilteredViewsData = [...originalViewsData];
    let tempFilteredResponsesData = [...originalResponsesData];
    if (isActiveAllTimeFilter && (selectedAllTimeOption || dayPickerDate)) {
      tempFilteredViewsData = [...views];
      tempFilteredResponsesData = [...responses];
    }
    if (fromClear) {
      tempFilteredViewsData = [...originalViewsData];
      tempFilteredResponsesData = [...originalResponsesData];
    }

    if (selectedAllDevicesOption === "desktop") {
      tempFilteredViewsData = tempFilteredViewsData?.filter((response) => {
        setAllDeviceTitle("Desktop");
        return response.device === "desktop";
      });
      tempFilteredResponsesData = tempFilteredResponsesData?.filter(
        (response) => {
          setAllDeviceTitle("Desktop");
          return response.device === "desktop";
        }
      );
    } else if (selectedAllDevicesOption === "tablet") {
      tempFilteredViewsData = tempFilteredViewsData?.filter((response) => {
        setAllDeviceTitle("Desktop");
        return response.device === "tablet";
      });
      tempFilteredResponsesData = tempFilteredResponsesData?.filter(
        (response) => {
          setAllDeviceTitle("Desktop");
          return response.device === "tablet";
        }
      );
      setAllDeviceTitle("Tablet");
    } else if (selectedAllDevicesOption === "mobile") {
      tempFilteredViewsData = tempFilteredViewsData?.filter((response) => {
        setAllDeviceTitle("Desktop");
        return response.device === "mobile";
      });
      tempFilteredResponsesData = tempFilteredResponsesData?.filter(
        (response) => {
          setAllDeviceTitle("Desktop");
          return response.device === "mobile";
        }
      );
      setAllDeviceTitle("Mobile");
    }
    setViews(tempFilteredViewsData);
    setResponses(tempFilteredResponsesData);
    setActiveAllDevicesFilter(true);
    setOpenedAllDevices(false);
    setTimeout(() => {
      setLoading(false);
    }, 300);
  };

  const getTodayDate = () => subDays(new Date(), 1);
  const getThisWeekDate = () => subDays(new Date(), 7);
  const getThisMonth = () => subDays(new Date(), 31);
  const getThisYear = () => subDays(new Date(), 366);

  const onChangeAllTimeOption = (option) => {
    setSelectedAllTimeOption(option);
    const today = subDays(new Date(), 0);
    switch (option) {
      case "today":
        setDayPickerDate({
          from: subDays(new Date(), 0),
          to: subDays(new Date(), 0),
        });
        break;
      case "thisWeek":
        setDayPickerDate({ from: getThisWeekDate(), to: today });
        break;
      case "thisMonth":
        setDayPickerDate({ from: getThisMonth(), to: today });
        break;
      case "thisYear":
        setDayPickerDate({ from: getThisYear(), to: today });
    }
  };

  const onChangeDeviceOption = (option) => {
    setSelectedAllDevicesOption(option);
  };

  const getDeviceIcon = (device) => {
    if (device === "desktop") {
      return (
        <Popover
          placement="bottom"
          title="Desktop"
          overlayInnerStyle={{
            height: "45px",
            textAlign: "center",
            padding: "10px 0 0 0",
          }}
        >
          <MdOutlineDesktopWindows style={{ fontSize: "20px" }} />
        </Popover>
      );
    } else if (device === "tablet") {
      return (
        <Popover
          placement="bottom"
          title="Tablet"
          overlayInnerStyle={{
            height: "45px",
            textAlign: "center",
            padding: "10px 0 0 0",
          }}
        >
          <FiTablet style={{ fontSize: "20px" }} />
        </Popover>
      );
    } else {
      return (
        <Popover
          placement="bottom"
          title="Mobile"
          overlayInnerStyle={{
            height: "45px",
            textAlign: "center",
            padding: "10px 0 0 0",
          }}
        >
          <HiMiniDevicePhoneMobile style={{ fontSize: "20px" }} />
        </Popover>
      );
    }
  };

  const isShowedChart = () => {
    if (selectedAnalyticOption === "views" && views && views.length === 0) {
      return false;
    }
    if (
      selectedAnalyticOption === "responses" &&
      responses &&
      responses.length === 0
    ) {
      return false;
    }
    return true;
  };

  if (loading) {
    return <SmallLoading />;
  }

  return (
    <div className="analtytics-container">
      <div className="analtytics-top-menu-container">
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            position: "relative",
          }}
        >
          {isActiveAllTimeFilter ? (
            <div className="analtytics-top-menu-allTime-item-filtered-container">
              <div
                style={{ backgroundColor: "#EBF4F6" }}
                className="analtytics-top-menu-filter-item-filtered"
                onClick={() => {
                  // setSelectedAllTimeOption();
                  setOpenedAllTime(!openedAllTime);
                }}
              >
                <div className="analtytics-top-menu-filter-item-icon-filtered">
                  <BsCalendar3Event />
                </div>
                <div className="analtytics-top-menu-filter-item-title-filtered">
                  {allTimeTitle}
                </div>
              </div>
              <IoMdClose
                style={{ marginLeft: "10px" }}
                className="analtytics-top-menu-filter-item-filtered-clear"
                onClick={clearAllTimeFilter}
              />
            </div>
          ) : (
            <div
              className="analtytics-top-menu-allTime-item"
              onClick={() => setOpenedAllTime(!openedAllTime)}
              style={{ marginLeft: "10px" }}
            >
              <div className="analtytics-top-menu-allTime-item-icon">
                <BsCalendar3Event />
              </div>
              <div className="analtytics-top-menu-allTime-item-title">
                All time
              </div>
              <IoIosArrowDown style={{ marginLeft: "10px" }} />
            </div>
          )}

          <div
            ref={openedAllTimeRef}
            className={
              openedAllTime
                ? "analtytics-top-menu-filter-allTime-opened"
                : "analtytics-top-menu-filter-allTime"
            }
          >
            <div className="analtytics-top-menu-filter-allTime-left">
              <div
                className={
                  selectedAllTimeOption === "today"
                    ? "analtytics-top-menu-filter-allTime-left-item-selected"
                    : "analtytics-top-menu-filter-allTime-left-item"
                }
                onClick={() => onChangeAllTimeOption("today")}
              >
                Today
              </div>
              <div
                className={
                  selectedAllTimeOption === "thisWeek"
                    ? "analtytics-top-menu-filter-allTime-left-item-selected"
                    : "analtytics-top-menu-filter-allTime-left-item"
                }
                onClick={() => onChangeAllTimeOption("thisWeek")}
              >
                This week
              </div>
              <div
                className={
                  selectedAllTimeOption === "thisMonth"
                    ? "analtytics-top-menu-filter-allTime-left-item-selected"
                    : "analtytics-top-menu-filter-allTime-left-item"
                }
                onClick={() => onChangeAllTimeOption("thisMonth")}
              >
                This month
              </div>
              <div
                className={
                  selectedAllTimeOption === "thisYear"
                    ? "analtytics-top-menu-filter-allTime-left-item-selected"
                    : "analtytics-top-menu-filter-allTime-left-item"
                }
                onClick={() => onChangeAllTimeOption("thisYear")}
              >
                This year
              </div>
            </div>
            <div className="analtytics-top-menu-filter-allTime-right">
              <DayPicker
                mode="range"
                selected={dayPickerDate}
                onSelect={onSelectDayPicker}
              />
              <div className="analtytics-top-menu-filter-allTime-right-buttons">
                <div
                  className="analtytics-top-menu-filter-allTime-right-button-cancel"
                  onClick={() => setOpenedAllTime(false)}
                >
                  Cancel
                </div>
                <div
                  className="analtytics-top-menu-filter-allTime-right-button-apply"
                  onClick={() => applyAllTimeFilter(false)}
                >
                  Apply
                </div>
              </div>
            </div>
          </div>
          <div style={{ position: "relative" }}>
            {isActiveAllDevicesFilter ? (
              <div className="analtytics-top-menu-allDevice-item-filtered-container">
                <div
                  style={{ backgroundColor: "#EBF4F6" }}
                  className="analtytics-top-menu-filter-item-filtered"
                  onClick={() => {
                    // setSelectedAllTimeOption();
                    setOpenedAllDevices(!openedAllDevices);
                  }}
                >
                  <div className="analtytics-top-menu-filter-item-icon-filtered">
                    <BsCalendar3Event />
                  </div>
                  <div className="analtytics-top-menu-filter-item-title-filtered">
                    {allDeviceTitle}
                  </div>
                </div>
                <IoMdClose
                  style={{ marginLeft: "10px" }}
                  className="analtytics-top-menu-filter-item-filtered-clear"
                  onClick={clearDeviceFilter}
                />
              </div>
            ) : (
              <div
                className="analtytics-top-menu-allDevice-item"
                onClick={() => setOpenedAllDevices(!openedAllDevices)}
              >
                <div className="analtytics-top-menu-allDevice-item-icon">
                  <MdOutlineDevices />
                </div>
                <div className="analtytics-top-menu-allDevice-item-title">
                  All Devices
                </div>
                <IoIosArrowDown style={{ marginLeft: "10px" }} />
              </div>
            )}

            <div
              ref={openedAllDevicesRef}
              className={
                openedAllDevices
                  ? "analtytics-top-menu-filter-allDevice-opened"
                  : "analtytics-top-menu-filter-allDevice"
              }
            >
              <div className="analtytics-top-menu-filter-allDevice-left">
                <div
                  className={
                    selectedAllDevicesOption === "desktop"
                      ? "analtytics-top-menu-filter-allDevice-left-item-selected"
                      : "analtytics-top-menu-filter-allDevice-left-item"
                  }
                  onClick={() => onChangeDeviceOption("desktop")}
                >
                  Desktop
                </div>
                <div
                  className={
                    selectedAllDevicesOption === "tablet"
                      ? "analtytics-top-menu-filter-allDevice-left-item-selected"
                      : "analtytics-top-menu-filter-allDevice-left-item"
                  }
                  onClick={() => onChangeDeviceOption("tablet")}
                >
                  Tablet
                </div>
                <div
                  className={
                    selectedAllDevicesOption === "mobile"
                      ? "analtytics-top-menu-filter-allDevice-left-item-selected"
                      : "analtytics-top-menu-filter-allDevice-left-item"
                  }
                  onClick={() => onChangeDeviceOption("mobile")}
                >
                  Mobile
                </div>
                <div className="analtytics-top-menu-filter-allDevice-buttons">
                  <div
                    className="analtytics-top-menu-filter-allDevice-button-cancel"
                    onClick={() => setOpenedAllDevices(false)}
                  >
                    Cancel
                  </div>
                  <div
                    className="analtytics-top-menu-filter-allDevice-button-apply"
                    onClick={() => applyDeviceFilter(false)}
                  >
                    Apply
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {isShowedChart() ? (
        <div className="analtytics-chart-container">
          <div className="analtytics-chart-toggle-button-container">
            {isOpenedChart ? (
              <div
                className="analtytics-chart-close-button"
                onClick={() => setOpenedChart(false)}
              >
                <IoMdClose />
              </div>
            ) : (
              <div
                className="analtytics-chart-open-button"
                onClick={() => setOpenedChart(true)}
              >
                <MdOutlineShowChart />
              </div>
            )}
          </div>

          {isOpenedChart && (
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                height: "300px",
              }}
            >
              <LineChart
                borderWidth="100%"
                lineData={
                  selectedAnalyticOption === "views" ? views : responses
                }
              />
            </div>
          )}
        </div>
      ) : (
        <div></div>
      )}
      <div className="analytics-options">
        <div
          onClick={() => setSelectedAnalyticOption("views")}
          className={
            selectedAnalyticOption === "views"
              ? "analytics-options-item-selected"
              : "analytics-options-item"
          }
        >
          <div className="analytics-options-item-title">Views</div>
          <div className="analytics-options-item-result">
            {sortedViews?.length || 0}
          </div>
        </div>

        <div
          onClick={() => setSelectedAnalyticOption("responses")}
          className={
            selectedAnalyticOption === "responses"
              ? "analytics-options-item-selected"
              : "analytics-options-item"
          }
        >
          <div className="analytics-options-item-title">Responses</div>
          <div className="analytics-options-item-result">
            {sortedResponses?.length || 0}
          </div>
        </div>

        <div
          className="analytics-options-item-explanation"
          /*style={{ backgroundColor: "#fcd5c1" }}*/
        >
          <div className="analytics-options-item-title">Completion rate</div>
          <div className="analytics-options-item-result">
            {responses && responses.length > 0 && views && views.length > 0
              ? Math.round((responses.length / views.length) * 100) + "%"
              : "N/A"}
          </div>
        </div>
        <div
          className="analytics-options-item-explanation"
          /*style={{ backgroundColor: "#c8cafb" }}*/
        >
          <div className="analytics-options-item-title">Time to complete</div>
          <div className="analytics-options-item-result">
            {" "}
            {calculateTimeToComplete()}
          </div>
        </div>
      </div>
      {isShowedChart() ? (
        <div className="analytics-content-container">
          <div className="analytics-content-headers">
            <Row style={{ width: "100%" }}>
              <Col span={3} className="analytics-content-header-item">
                Visitors
              </Col>
              <Col span={3} className="analytics-content-header-item">
                Date
              </Col>
              <Col span={3} className="analytics-content-header-item">
                Country
              </Col>
              <Col span={3} className="analytics-content-header-item">
                City
              </Col>

              <Col span={3} className="analytics-content-header-item">
                Platform
              </Col>
              <Col span={3} className="analytics-content-header-item">
                Browser
              </Col>
              <Col span={3} className="analytics-content-header-item">
                Device
              </Col>
              {selectedAnalyticOption === "responses" && (
                <Col span={3} className="analytics-content-header-item">
                  Response
                </Col>
              )}
            </Row>
          </div>

          {selectedAnalyticOption === "views" &&
            views &&
            views.length > 0 &&
            sortedViews?.map((view, index) => (
              <div className="analytics-content-data" key={index}>
                <Row style={{ width: "100%" }}>
                  <Col span={3} className="analytics-content-header-item">
                    {view.ip}
                  </Col>
                  <Col span={3} className="analytics-content-header-item">
                    <Popover
                      placement="bottom"
                      title={formatDate(view.createdDate)}
                      overlayInnerStyle={{
                        height: "45px",
                        textAlign: "center",
                        padding: "10px 0 0 0",
                      }}
                    >
                      {timeAgo(view.createdDate)}
                    </Popover>
                  </Col>
                  <Col span={3} className="analytics-content-header-item">
                    <ReactCountryFlag
                      countryCode={view.country}
                      svg
                      style={{
                        width: "1.2em",
                        height: "1.2em",
                      }}
                      aria-label={view.country}
                    />
                    <div style={{ marginLeft: "10px" }}>{view.countryName}</div>
                  </Col>
                  <Col span={3} className="analytics-content-header-item">
                    {view.city}
                  </Col>

                  <Col span={3} className="analytics-content-header-item">
                    {view.platform}
                  </Col>
                  <Col span={3} className="analytics-content-header-item">
                    {view.browser}
                  </Col>
                  <Col span={3} className="analytics-content-header-item">
                    {getDeviceIcon(view.device)}
                  </Col>
                </Row>
              </div>
            ))}

          {selectedAnalyticOption === "responses" &&
            responses &&
            responses.length > 0 &&
            sortedResponses?.map((response, index) => (
              <div className="analytics-content-data" key={index}>
                <Row style={{ width: "100%" }}>
                  <Col span={3} className="analytics-content-header-item">
                    {response.ip}
                  </Col>
                  <Col span={3} className="analytics-content-header-item">
                    <Popover
                      placement="bottom"
                      title={formatDate(response.createdDate)}
                      overlayInnerStyle={{
                        height: "45px",
                        textAlign: "center",
                        padding: "10px 0 0 0",
                      }}
                    >
                      {timeAgo(response.createdDate)}
                    </Popover>
                  </Col>
                  <Col span={3} className="analytics-content-header-item">
                    <ReactCountryFlag
                      countryCode={response.country}
                      svg
                      style={{
                        width: "1.2em",
                        height: "1.2em",
                      }}
                      aria-label={response.country}
                    />
                  </Col>
                  <Col span={3} className="analytics-content-header-item">
                    {response.city}
                  </Col>

                  <Col span={3} className="analytics-content-header-item">
                    {response.platform}
                  </Col>
                  <Col span={3} className="analytics-content-header-item">
                    {response.browser}
                  </Col>
                  <Col span={3} className="analytics-content-header-item">
                    {getDeviceIcon(response.device)}
                  </Col>
                  <Col
                    span={3}
                    className="analytics-content-header-item"
                    onClick={() => viewResponse(response.responseId)}
                  >
                    <div className="analytics-content-data-col-viewResponse">
                      View Response
                    </div>
                  </Col>
                </Row>
              </div>
            ))}
        </div>
      ) : (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            marginTop: "50px",
            padding: "30px",
            color: "var(--grey100)",
            background: "white",
            border: "1px solid var(--grey300)",
            borderRadius: "5px",
            margin: "20px",
          }}
        >
          No data
        </div>
      )}
    </div>
  );
};

export default Analytics;
