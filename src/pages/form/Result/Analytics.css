.analtytics-container {
  padding: 20px 10px;
  width: 100%;
}

.analtytics-top-menu-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 10px 15px 15px 15px;
  border-bottom: 1px solid var(--grey300);
}

.analtytics-top-menu-allTime-item-filtered-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10px;
  padding: 5px 10px 5px 15px;
  margin-right: 10px;
  border-radius: 5px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--green600);
  border: 1px solid var(--green400);
  cursor: pointer;
}

.analtytics-top-menu-filter-item-filtered {
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "Exo 2", sans-serif;
}

.analtytics-top-menu-filter-item-filtered-clear {
  background-color: var(--green600);
  border-left: 1px solid var(--grey200);
  padding-left: 4px;
  font-size: 20px;
  color: var(--green400);
}

.analtytics-top-menu-filter-item-filtered-clear:hover {
  opacity: 0.7;
}

.analtytics-top-menu-filter-item-icon-filtered {
  margin-right: 10px;
  color: var(--green400);
  font-size: 12px;
}

.analtytics-top-menu-filter-item-title-filtered {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  color: var(--green400);
}

.analtytics-top-menu-filter-item-icon-filtered {
  margin-right: 10px;
  color: var(--green400);
  font-size: 12px;
}

.analtytics-top-menu-allDevice-item-filtered-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 10px 5px 15px;
  margin-right: 10px;
  border-radius: 5px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--green600);
  border: 1px solid var(--green400);
  cursor: pointer;
}

.analtytics-top-menu-filter-item-filtered {
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "Exo 2", sans-serif;
}

.analtytics-top-menu-filter-item-filtered-clear {
  background-color: var(--green600);
  border-left: 1px solid var(--grey200);
  padding-left: 4px;
  font-size: 20px;
  color: var(--green400);
}

.analtytics-top-menu-filter-item-filtered-clear:hover {
  opacity: 0.7;
}

.analtytics-top-menu-filter-item-icon-filtered {
  margin-right: 10px;
  color: var(--green400);
  font-size: 12px;
}

.analtytics-top-menu-filter-item-title-filtered {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  color: var(--green400);
}

.analtytics-top-menu-filter-item-icon-filtered {
  margin-right: 10px;
  color: var(--green400);
  font-size: 12px;
}

.analtytics-top-menu-allTime-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 15px;
  margin-right: 10px;
  border-radius: 3px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--grey300);
  border: 1px solid var(--grey200);
  cursor: pointer;
}

.analtytics-top-menu-allTime-item:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.analtytics-top-menu-allTime-item-title {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  color: var(--black);
}

.analtytics-top-menu-allTime-item-icon {
  margin-right: 10px;
  color: var(--black);
  font-size: 12px;
}

.analtytics-top-menu-filter-allTime {
  position: absolute;
  display: flex;
  background-color: var(--white);
  border: 1px solid var(--grey200);
  border-radius: 5px;
  top: 180px;

  z-index: 999;
  display: none;
}

.analtytics-top-menu-filter-allTime-opened {
  position: absolute;
  display: flex;
  background-color: var(--white);
  border: 1px solid var(--grey100);
  border-radius: 5px;
  top: 40px;
  left: 20px;
  z-index: 999;
}

.analtytics-top-menu-filter-allTime-left {
  padding: 20px;
  border-right: 1px solid var(--grey200);
  margin-right: 20px;
}

.analtytics-top-menu-filter-allTime-left-item {
  font-family: "Exo 2", sans-serif;
  padding: 5px 15px;
  margin-bottom: 5px;
  font-size: 14px;
  color: var(--black);
  border-radius: 5px;
  cursor: pointer;
  width: 150px;
  transition: 0.3s all;
}

.analtytics-top-menu-filter-allTime-left-item-selected {
  font-family: "Exo 2", sans-serif;
  padding: 5px 15px;
  margin-bottom: 5px;
  font-size: 14px;
  color: var(--black);
  border-radius: 5px;
  cursor: pointer;
  background-color: var(--grey300);
  transition: 0.3s all;
}

.analtytics-top-menu-filter-allTime-left-item:hover {
  background-color: var(--grey300);
}

.analtytics-top-menu-filter-allTime-right {
  padding: 20px;
  margin-right: 20px;
}

.analtytics-top-menu-filter-allTime-right-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 25px;
}

.analtytics-top-menu-filter-allTime-right-button-cancel {
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
  font-weight: 500;
  padding: 5px 10px;
  background-color: var(--grey300);
  border: 1px solid var(--grey200);
  border-radius: 5px;
  margin-right: 15px;
  cursor: pointer;
}

.analtytics-top-menu-filter-allTime-right-button-cancel:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.analtytics-top-menu-filter-allTime-right-button-apply {
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
  font-weight: 500;
  padding: 5px 10px;
  background-color: var(--black);
  border: 1px solid var(--black);
  color: var(--white);
  border-radius: 5px;
  cursor: pointer;
}

.analtytics-top-menu-filter-allTime-right-button-apply:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.analtytics-top-menu-allDevice-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 15px;
  margin-right: 10px;
  border-radius: 3px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--grey300);
  border: 1px solid var(--grey200);
  cursor: pointer;
}

.analtytics-top-menu-allDevice-item:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.analtytics-top-menu-allDevice-item-title {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  color: var(--black);
}

.analtytics-top-menu-allDevice-item-icon {
  margin-right: 10px;
  color: var(--black);
  font-size: 16px;
}

.analtytics-top-menu-filter-allDevice {
  position: absolute;
  display: flex;
  background-color: var(--white);
  border: 1px solid var(--grey200);
  border-radius: 5px;
  top: 180px;
  z-index: 999;
  display: none;
}

.analtytics-top-menu-filter-allDevice-opened {
  position: absolute;
  display: flex;
  background-color: var(--white);
  border: 1px solid var(--grey100);
  border-radius: 5px;
  width: 200px;
  top: 40px;
  z-index: 999;
}

.analtytics-top-menu-filter-allDevice-left {
  padding: 20px;
}

.analtytics-top-menu-filter-allDevice-left-item {
  font-family: "Exo 2", sans-serif;
  padding: 5px 20px;
  margin-bottom: 5px;
  font-size: 14px;
  color: var(--black);
  border-radius: 5px;
  cursor: pointer;
  transition: 0.3s all;
}

.analtytics-top-menu-filter-allDevice-left-item-selected {
  font-family: "Exo 2", sans-serif;
  padding: 5px 20px;
  margin-bottom: 5px;
  font-size: 14px;
  color: var(--black);
  border-radius: 5px;
  cursor: pointer;
  background-color: var(--grey300);
  transition: 0.3s all;
}

.analtytics-top-menu-filter-allDevice-left-item:hover {
  background-color: var(--grey300);
}

.analtytics-top-menu-filter-allDevice-buttons {
  display: flex;
  justify-content: flex-end;
  width: 165px;
  margin-top: 25px;
}

.analtytics-top-menu-filter-allDevice-button-cancel {
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
  font-weight: 500;
  padding: 5px 10px;
  background-color: var(--grey300);
  border: 1px solid var(--grey200);
  border-radius: 5px;
  margin-right: 15px;
  cursor: pointer;
}

.analtytics-top-menu-filter-allDevice-button-cancel:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.analtytics-top-menu-filter-allDevice-button-apply {
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
  font-weight: 500;
  padding: 5px 10px;
  background-color: var(--black);
  border: 1px solid var(--black);
  color: var(--white);
  border-radius: 5px;
  cursor: pointer;
}

.analtytics-top-menu-filter-allDevice-button-apply:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.analtytics-chart-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.analtytics-chart-toggle-button-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin: 15px 40px -50px 0;
}

.analtytics-chart-close-button {
  padding: 3px;
  border: 1px solid var(--red100);
  border-radius: 5px;
  background-color: var(--red200);
  color: var(--white);
  font-size: 18px;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  cursor: pointer;
  z-index: 999;
}

.analtytics-chart-open-button {
  padding: 3px;
  border: 1px solid var(--black);
  border-radius: 5px;
  background-color: var(--white);
  color: var(--black);
  font-size: 18px;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  cursor: pointer;
  z-index: 999;
}

.analytics-options {
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
  width: 100%;
  padding: 20px 15px 0 15px;
}

.analytics-options-item {
  text-align: center;
  padding: 10px;
  background-color: var(--white);
  border-radius: 5px;
  border: 1px solid var(--grey200);
  width: 155px;
  cursor: pointer;
  margin-right: 20px;
}

.analytics-options-item:hover {
  text-align: center;
  padding: 10px;
  background-color: var(--bg-light);
  border-radius: 5px;
  border: 1px solid var(--grey200);
  width: 155px;
  cursor: pointer;
}

.analytics-options-item-selected {
  text-align: center;
  padding: 10px;
  margin-right: 20px;
  background-color: var(--black);
  border: 1px solid var(--grey200);
  border-radius: 5px;
  color: var(--white);
  width: 155px;
  cursor: pointer;
}

.analytics-options-item-explanation {
  text-align: center;
  padding: 10px;
  background-color: var(--grey400);
  border: 1px solid var(--grey200);
  border-radius: 5px;
  width: 155px;
  cursor: default;
  margin-right: 20px;
}

.analytics-filter-time-item-customExpanded {
  position: absolute;
  top: 40px;
  left: 0;
  background: white;
  padding: 30px 20px;
  width: 390px;
  border: 1px solid var(--grey200);
  border-radius: 5px;
  z-index: 999;
}

.analytics-filter-time-item-customClosed {
  display: none;
}

.analytics-filter-time-item-customExpanded-item {
  color: var(--black);
}

.analytics-filter-time-item-customExpanded-item-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3px 10px;
  background-color: var(--black);
  color: var(--white);
  border-radius: 5px;
  margin-left: 20px;
}

.analytics-filter-time-item-customExpanded-button-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20px 0 0 0;
}

.analytics-filter-time-item-customExpanded-button {
  padding: 3px 15px;
  background-color: var(--black);
  color: var(--white);
  border-radius: 5px;
  font-family: "Exo 2", sans-serif;
  border: 1px solid var(--grey200);
  cursor: pointer;
}

.analytics-filter-time-item-customExpanded-button:hover {
  opacity: 0.7;
}

.analytics-options-item-title {
  font-family: "Exo 2", sans-serif;
}

.analytics-options-item-result {
  font-family: "Exo 2", sans-serif;
  font-size: 30px;
  font-weight: 500;
}

.analytics-content-container {
  margin: 20px 15px;
  background-color: var(--white);
  padding: 20px 15px;
  border-radius: 5px;
  border: 1px solid var(--grey400);
}

.analytics-content-headers {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--grey200);
  font-family: "Exo 2", sans-serif;
  font-weight: 600;
}

.analytics-content-header-item {
  display: flex;
  align-items: center;
}

.analytics-content-data-container {
  display: flex;
  justify-content: space-between;
}

.analytics-content-data {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid var(--grey200);
}

.analytics-content-data-col {
  cursor: default;
  font-family: "Exo 2", sans-serif;
  font-size: 15px;
  font-weight: 500;
}

.analytics-content-data-col-viewResponse {
  display: flex;
  justify-content: center;
  align-items: center;
  width: fit-content;
  background-color: var(--green400);
  color: var(--white);
  padding: 1px 6px 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 400;
  font-family: "Exo 2", sans-serif;
  cursor: pointer;
}

.analytics-content-data-col-viewResponse:hover {
  opacity: 0.8;
}

.ant-picker-outlined {
  width: 235px !important;
}
