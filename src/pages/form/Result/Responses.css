.responses-container {
  padding: 20px 10px;
  width: 100%;
}

.response-top-menu-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--grey300);
}

.response-top-menu-filter-container {
  display: flex;
  align-items: center;
}

.response-top-menu-filter-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 15px;
  margin-right: 10px;
  border-radius: 5px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--white);
  border: 1px solid var(--grey300);
  cursor: pointer;
}

.response-top-menu-filter-item-title {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  color: var(--black);
}

.response-top-menu-filter-item-icon {
  margin-right: 10px;
  color: var(--black);
  font-size: 12px;
}

.response-top-menu-allTime-item:hover {
  background-color: var(--grey300);
  transition: 0.3s all;
}

.response-top-menu-allTime-item-filtered-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 10px 5px 15px;
  margin-right: 20px;
  border-radius: 5px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--green600);
  border: 1px solid var(--green400);
  cursor: pointer;
}

.response-top-menu-allTime-item-filtered {
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "Exo 2", sans-serif;
}

.response-top-menu-allTime-item-filtered-clear {
  background-color: var(--green600);
  border-left: 1px solid var(--grey200);
  padding-left: 4px;
  font-size: 20px;
  color: var(--green400);
}

.response-top-menu-allTime-item-filtered-clear:hover {
  opacity: 0.7;
}

.response-top-menu-allTime-item-icon-filtered {
  margin-right: 10px;
  color: var(--green400);
  font-size: 12px;
}

.response-top-menu-allTime-item-title-filtered {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  color: var(--green400);
}

.response-top-menu-allTime-item-icon-filtered {
  margin-right: 10px;
  color: var(--green400);
  font-size: 12px;
}

.response-top-menu-allTime-item-filters {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 15px;
  margin-right: 10px;
  border-radius: 5px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--white);
  border: 1px solid var(--grey300);
  cursor: pointer;
}

.response-top-menu-allTime-item-filters:hover {
  background-color: var(--grey300);
  transition: 0.3s all;
}

.response-top-menu-allTime-item-filters-icon {
  margin-right: 10px;
  color: var(--black);
  font-size: 12px;
}

.response-top-menu-allTime-item-filters-title {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  color: var(--black);
}

.response-top-menu-filter-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 15px;
  margin-right: 10px;
  border-radius: 3px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--grey300);
  border: 1px solid var(--grey200);
  cursor: pointer;
}

.response-top-menu-filter-item-title {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  color: var(--black);
}

.response-top-menu-filter-item-icon {
  margin-right: 10px;
  color: var(--black);
  font-size: 12px;
}

.response-top-menu-filter-item:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.response-top-menu-filter-item-filtered-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 10px 5px 15px;
  margin-right: 20px;
  border-radius: 5px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--green600);
  border: 1px solid var(--green400);
  cursor: pointer;
}

.response-top-menu-filter-item-filtered {
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "Exo 2", sans-serif;
}

.response-top-menu-filter-item-filtered-clear {
  background-color: var(--green600);
  border-left: 1px solid var(--grey200);
  padding-left: 4px;
  font-size: 20px;
  color: var(--green400);
}

.response-top-menu-filter-item-filtered-clear:hover {
  opacity: 0.7;
}

.response-top-menu-filter-item-icon-filtered {
  margin-right: 10px;
  color: var(--green400);
  font-size: 12px;
}

.response-top-menu-filter-item-title-filtered {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  color: var(--green400);
}

.response-top-menu-filter-item-icon-filtered {
  margin-right: 10px;
  color: var(--green400);
  font-size: 12px;
}

.response-top-menu-filter-item-filters {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 15px;
  margin-right: 10px;
  border-radius: 3px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--grey300);
  border: 1px solid var(--grey200);
  cursor: pointer;
}

.response-top-menu-filter-item-filters:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.response-top-menu-filter-item-filters-icon {
  margin-right: 10px;
  color: var(--black);
  font-size: 12px;
}

.response-top-menu-filter-item-filters-title {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  color: var(--black);
}

.response-top-menu-filter-item-search {
  display: flex;
  justify-content: center;
  align-items: center;
}

.response-top-menu-filter-search-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 20px;
}

.response-top-menu-filter-item-search-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 35px;
  height: 35px;
  background-color: var(--white);
  border: 1px solid var(--grey300);
  margin-left: 1px;
  cursor: pointer;
}

.response-top-menu-filter-item-search-button:hover {
  background-color: var(--grey300);
  transition: 0.3s all;
}

.response-top-menu-filter-item-search-button-filtered {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 35px;
  height: 35px;
  background-color: var(--green600);
  border: 1px solid var(--grey400);
  margin-left: 1px;
  cursor: pointer;
}

.response-top-menu-filter-item-search-input {
  border: 1px solid var(--grey300);
  height: 35px;
  width: 250px;
  padding: 0 15px;
}

.response-top-menu-filter-allTime {
  position: absolute;
  display: flex;
  background-color: var(--white);
  border: 1px solid var(--grey200);
  border-radius: 5px;
  top: 180px;

  z-index: 999;
  display: none;
}

.response-top-menu-filter-allTime-opened {
  position: absolute;
  display: flex;
  background-color: var(--white);
  border: 1px solid var(--grey100);
  border-radius: 5px;
  top: 40px;
  z-index: 999;
}

.response-top-menu-filter-allTime-left {
  padding: 20px;
  border-right: 1px solid var(--grey200);
  margin-right: 20px;
}

.response-top-menu-filter-allTime-left-item {
  font-family: "Exo 2", sans-serif;
  padding: 5px 15px;
  margin-bottom: 5px;
  font-size: 14px;
  color: var(--black);
  border-radius: 5px;
  cursor: pointer;
  width: 150px;
  transition: 0.3s all;
}

.response-top-menu-filter-allTime-left-item-selected {
  font-family: "Exo 2", sans-serif;
  padding: 5px 15px;
  margin-bottom: 5px;
  font-size: 14px;
  color: var(--black);
  border-radius: 5px;
  cursor: pointer;
  background-color: var(--grey300);
  transition: 0.3s all;
}

.response-top-menu-filter-allTime-left-item:hover {
  background-color: var(--grey300);
}

.response-top-menu-filter-allTime-right {
  padding: 20px;
  margin-right: 20px;
}

.response-top-menu-filter-allTime-right-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 25px;
}

.response-top-menu-filter-allTime-right-button-cancel {
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
  font-weight: 500;
  padding: 5px 10px;
  background-color: var(--grey300);
  border: 1px solid var(--grey200);
  border-radius: 5px;
  margin-right: 15px;
  cursor: pointer;
}

.response-top-menu-filter-allTime-right-button-cancel:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.response-top-menu-filter-allTime-right-button-apply {
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
  font-weight: 500;
  padding: 5px 10px;
  background-color: var(--black);
  border: 1px solid var(--black);
  color: var(--white);
  border-radius: 5px;
  cursor: pointer;
}

.response-top-menu-filter-allTime-right-button-apply:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.response-top-menu-filters-opened {
  position: absolute;
  padding: 20px;
  top: 40px;
  background-color: var(--white);
  border: 1px solid var(--grey100);
  border-radius: 5px;
  z-index: 999;
}

.response-top-menu-filters-closed {
  display: none;
}

.response-top-menu-filters-opened-title {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  color: var(--black);
  margin-bottom: 15px;
  font-weight: 300;
}

.response-top-menu-filters-opened-filter-container {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid var(--grey300);
  border-radius: 5px;
  padding: 15px;
}

.response-top-menu-filters-item-select {
  position: relative;
  border: 1px solid var(--grey200);
  margin-right: 10px;
}

.response-top-menu-filters-item-input {
  width: 200px;
  border: 1px solid var(--grey200);
  padding: 2px 10px;
  font-weight: 300;
  font-size: 13px;
}

.response-top-menu-filters-opened-filter-delete {
  font-size: 24px;
  margin-left: 10px;
  padding: 3px;
  background-color: var(--red200);
  color: var(--white);
  border: 1px solid var(--red100);
  border-radius: 5px;
  cursor: pointer;
}

.response-top-menu-filters-opened-filter-delete:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.response-top-menu-setting-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-right: 20px;
}

.response-top-menu-setting-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 15px;
  margin-right: 10px;
  border: 1px solid var(--green400);
  border-radius: 3px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--green400);
  cursor: pointer;
}

.response-top-menu-setting-item:hover {
  opacity: 0.8;
  transition: 0.3s all;
}

.response-top-menu-setting-item-icon {
  margin-right: 10px;
  color: var(--white);
}

.response-top-menu-setting-item-title {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  color: var(--white);
}

.response-table-container {
  padding: 20px 30px 0 20px;
}

.response-column-selected-outer-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.response-column-selected-container {
  display: flex;
  justify-content: center;
  align-content: center;

  /*background-color: var(--black);*/
  width: fit-content;
  border-radius: 30px;
  color: var(--black);
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
  font-weight: 400;
}

.response-column-selected-close {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 15px;
  font-size: 14px;
}

.response-column-item-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.response-column-selected-actions {
  display: flex;
  align-items: center;
}

.response-column-selected-action-download {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  cursor: pointer;
  color: var(--black);
  padding: 2px;
  border: 1px solid var(--white);
}

.response-column-selected-action-download:hover {
  opacity: 0.8;
  border: 1px solid var(--grey200);
  border-radius: 5px;
  transition: 0.3s all;
  background-color: var(--grey300);
}

.response-column-selected-action-delete {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: var(--red100);
  cursor: pointer;
  padding: 2px;
  border: 1px solid var(--white);
  margin-left: 3px;
}

.response-column-selected-action-delete:hover {
  opacity: 0.8;
  border: 1px solid var(--grey200);
  border-radius: 5px;
  transition: 0.3s all;
  background-color: var(--grey300);
}

.response-column-item {
  display: flex;
  justify-content: center;
  align-items: center;
}

.response-column-item-icon {
  font-size: 15px;
  margin-right: 10px;
  padding: 3px;
  border: 1px solid var(--grey200);
  border-radius: 3px;
  background-color: var(--white);
  color: var(--black);
}

.response-column-item-title {
  font-size: 13px;
  font-weight: 400;
}

.response-multipleField {
  width: fit-content;
  margin: 5px;
  padding: 0 5px;
  border-radius: 5px;
  background-color: var(--white);
  border: 1px solid var(--grey100);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
}

.response-uploadField {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 10px 0;
  padding: 5px 10px;
  border-radius: 5px;
  background-color: var(--white);
  border: 1px solid var(--grey100);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
  cursor: pointer;
}

.response-uploadField-field {
  display: flex;
  align-items: center;
}

.response-uploadField-field-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 35px;
  margin-right: 10px;
}

.response-uploadField-field-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 35px;
  height: 35px;
  margin-right: 10px;
}

.response-uploadField-field-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  max-width: 200px;
}

.response-uploadField-field-size {
  opacity: 0.7;
}

.response-uploadField-download {
  display: flex;
  justify-content: center;
  align-items: center;
  width: fit-content;
  background-color: var(--grey300);
  border: 1px solid var(--grey200);
  border-radius: 5px;
  padding: 5px;
  margin-left: 20px;
  cursor: pointer;
}

.response-uploadField-download:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.response-uploadField-download-icon {
  font-size: 16px;
}

.response-item {
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
}

.response-createdDate-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 16px;
}

.response-createdDate-container:hover .response-expand-icon {
  visibility: visible;
  transition: 0.3s all;
}

.response-expand-icon {
  visibility: hidden;
  padding: 5px;
  font-size: 16px;
  border-radius: 5px;
  background-color: var(--black);
  color: var(--white);
}

.response-selectedResponse-item {
  margin-bottom: 30px;
}

.response-selectedResponse-header-container {
  display: flex;
  justify-content: space-between;
  width: 500px;
}

.response-selectedResponse-header-date-container {
  display: flex;
  align-items: center;
  margin-left: -10px;
}

.response-selectedResponse-header-selections {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.response-selectedResponse-header-selection {
  padding: 5px;
  font-size: 16px;
  background-color: var(--white);
  border: 1px solid var(--grey100);
  border-radius: 3px;
  margin-right: 5px;
  cursor: pointer;
}

.response-selectedResponse-header-selection:hover {
  background-color: var(--grey200);
  transition: 0.3s all;
}

.response-selectedResponse-header-date {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
}

.response-selectedResponse-header-action-container {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.response-selectedResponse-print {
  display: flex;
  align-items: center;
  padding: 5px;
  background-color: var(--grey300);
  border: 1px solid var(--grey200);
  border-radius: 3px;
  margin-right: 8px;
  cursor: pointer;
}

.response-selectedResponse-print:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.response-selectedResponse-print-icon {
  font-size: 16px;
}

.response-selectedResponse-print-title {
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
}

.response-selectedResponse-delete {
  display: flex;
  align-items: center;
  padding: 5px;
  background-color: var(--red200);
  border: 1px solid var(--red100);
  color: var(--white);
  border-radius: 3px;
  cursor: pointer;
}

.response-selectedResponse-delete:hover {
  opacity: 0.7;
  transition: 0.3s all;
}

.response-selectedResponse-delete-icon {
  font-size: 16px;
}

.response-selectedResponse-delete-title {
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
}

.response-selectedResponse-item-innerContainer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.response-selectedResponse-item-icon {
  font-size: 16px;
  padding: 3px;
  border: 1px solid var(--grey200);
  border-radius: 5px;
  margin-right: 15px;
}

.response-selectedResponse-item-title {
  font-family: "Exo 2", sans-serif;
  font-size: 13px;
  opacity: 0.7;
}

.response-selectedResponse-item-multiple-answer {
  padding: 0px 5px;
  margin: 5px 0 5px 40px;
  border: 1px solid var(--grey100);
  border-radius: 5px;
  width: fit-content;
}

.response-selectedResponse-item-answer {
  padding: 5px 0 0 40px;
}

.response-selectedResponse-item-upload-answer {
  padding: 0px 5px;
  margin: 5px 0 5px 40px;
  border: 1px solid var(--grey100);
  border-radius: 5px;
  width: fit-content;
}

.ant-table-wrapper table {
  border: 1px solid var(--grey300);
}

.ant-table-wrapper .ant-table-cell-fix-righ {
  background-color: var(--white) !important;
}

.ant-table-wrapper .ant-table-cell-fix-left {
  background-color: var(--white) !important;
}

.ant-table-wrapper .ant-table-thead > tr > th {
  background-color: var(--white);
}

.ant-table-wrapper .ant-table-tbody > tr > td {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6 !important;
  /*border-right: 1px solid #eaeaea;*/
  white-space: break-spaces;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 400px;
}

.ant-table-row.ant-table-row-selected > .ant-table-cell {
  background-color: #ebf4f6 !important;
}

.ant-checkbox-indeterminate .ant-checkbox-inner:after {
  background-color: var(--black);
}

.ant-table-wrapper .ant-table-cell-fix-righ {
}

.ant-table-wrapper
  .ant-table-thead
  > tr
  > th:not(:last-child):not(.ant-table-selection-column):not(
    .ant-table-row-expand-icon-cell
  ):not([colspan])::before {
  height: 100% !important;
  top: 50% !important;
}

.ant-table-wrapper .ant-table-tbody > tr > td:nth-child(2) {
  padding: 0 !important;
}

.ant-drawer-header-title {
  width: 30px !important;
}

.ant-drawer-close {
  position: absolute;
  right: 0;
  color: var(--black) !important;
}

.ant-drawer-close:hover {
  padding: 5px !important;
  background-color: var(--white) !important;
  opacity: 0.5 !important;
  transition: 0.3s all;
}

.rdp-caption_label {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 16px;
}

.rdp-chevron {
  fill: var(--black) !important;
}

.rdp-range_start .rdp-day_button {
  border: none !important;
  border-radius: 5px !important;
  background-color: var(--green400) !important;
}

.rdp-range_end .rdp-day_button {
  border: none !important;
  border-radius: 5px !important;
  background-color: var(--green400) !important;
}

.rdp-range_middle {
  background-color: var(--green600) !important;
}

.rdp-today {
  color: var(--black) !important;
}

.rdp-day {
  font-size: small !important;
}

.rdp-selected {
  font-size: small !important;
  font-weight: 500 !important;
}

@media print {
  /* Yazıcı için tüm içeriği görünür hale getirin */
  body,
  html {
    visibility: visible !important;
    display: block !important;
    padding: 30px 0 0 30px;
  }

  .no-print {
    display: none !important;
  }
}
