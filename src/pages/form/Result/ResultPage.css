.resultPage-outer-container {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: calc(100% - 90px);
    margin-top: 90px;
}

.resultPage-container {
    display: flex;
    align-items: center;
    flex-direction: column;
    width: 100%;
    height: calc(100vh - 90px);
    margin-top: 60px;
    background-color: var(--bg-light);
}

.resultPage-top-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 10px 20px 0 20px;
    background-color: var(--white);
}

.resultPage-top-tab {
    display: flex;
    align-items: center;
    
}

.resultPage-top-tab-item {
    padding: 10px 10px;
    cursor: pointer;
    margin-right: 30px;
    margin-bottom: 3px;
    display: flex;
    font-family: "Exo 2", sans-serif;
    font-weight: 500;
}

.resultPage-top-tap-item-budget {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 8px;
    font-family: "Exo 2", sans-serif;
    font-size: 13px;
    font-weight: 600;
    color: var(--black);
    background-color: #ebf4f6;
    border: 1px solid #416067;
    width: fit-content;
    height: fit-content;
    padding: 2px 8px;
    border-radius: 5px;
    text-align: center;
}

.resultPage-top-tab-item:hover {
    opacity: .8;
}

.resultPage-top-tab-item-selected {
    display: flex;
    padding: 10px 10px;
    margin-right: 30px;
    border-bottom: 3px solid var(--black);
    font-family: "Exo 2", sans-serif;
    font-weight: 500;
    cursor: default;
}
