.analytics-container {
    width: 100%;
    max-width: 1200px;
    margin-top: 20px;
}

.analytics-filter-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    /*border-bottom: 1px solid var(--grey200);*/
}

.analytics-filter-timeContainer {
    display: flex;
}

.analytics-filter-time-item {
    margin-right: 20px;
    font-family: "Exo 2", sans-serif;
    padding: 5px 20px;
    border-radius: 5px;
    cursor: pointer;
}

.analytics-filter-time-item-selected {
    margin-right: 20px;
    font-family: "Exo 2", sans-serif;
    padding: 5px 20px;
    background-color: var(--black);
    color: var(--white);
    border-radius: 5px;
    cursor: default
}

.analytics-filter-button {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--white);
    border: 1px solid var(--grey200);
    border-radius: 5px;
    padding: 7px 15px;
    cursor: pointer;
}

.analytics-filter-button:hover {
    background-color: var(--grey400);
}

.analytics-filter-button-icon {
    margin-right: -30px;
    z-index: 1;
    font-size: 18px;
}

.analytics-filter-button-title {
    font-family: "Exo 2", sans-serif;
    font-weight: 500;
}

.analytics-filter-selectDevice-container {
    display: flex;
    align-items: center;
}

.analytics-filter-selectDevice {
    width: 170px;
    border: 1px solid var(--grey200) !important;
    height: 35px !important;
    padding: 0 0 0 30px;
}

.analytics-filter-selectDevice-select::after {
    border-width: 0 !important;
}

.analytics-filter-selectDevice::selection {
    border-width: 0 !important;
}

.analytics-filter-selectDevice:active {
    border-width: 0 !important;
}

.analytics-filter-selectDevice:checked {
    border-width: 0 !important;
}

.analytics-filter-selectDevice:visited {
    border-width: 0 !important;
}

.analytics-options {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    width: 100%;
}

.analytics-options-item {
    text-align: center;
    padding: 10px;
    background-color: var(--white);
    border-radius: 5px;
    border: 1px solid var(--grey200);
    width: 155px;
    cursor: pointer;
}

.analytics-options-item:hover {
    text-align: center;
    padding: 10px;
    background-color: var(--bg-light);
    border-radius: 5px;
    border: 1px solid var(--grey200);
    width: 155px;
    cursor: pointer;
}

.analytics-options-item-selected {
    text-align: center;
    padding: 10px;
    background-color: var(--black);
    border: 1px solid var(--grey200);
    border-radius: 5px;
    color: var(--white);
    width: 155px;
    cursor: pointer; 
    
}

.analytics-options-item-explanation {
    text-align: center;
    padding: 10px;
    background-color: var(--grey400);
    border-radius: 5px;
    width: 155px;
    cursor: default;
}

.analytics-filter-time-item-customExpanded {
    position: absolute;
    top: 40px;
    left: 0;
    background: white;
    padding: 30px 20px;
    width: 390px;
    border: 1px solid var(--grey200);
    border-radius: 5px;
    z-index: 999;
}

.analytics-filter-time-item-customClosed {
    display: none;
}

.analytics-filter-time-item-customExpanded-item {
    color: var(--black);
}

.analytics-filter-time-item-customExpanded-item-button {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3px 10px;
    background-color: var(--black);
    color: var(--white);
    border-radius: 5px;
    margin-left: 20px;
}

.analytics-filter-time-item-customExpanded-button-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 20px 0 0 0; 
}

.analytics-filter-time-item-customExpanded-button {
    padding: 3px 15px;
    background-color: var(--black);
    color: var(--white);
    border-radius: 5px;
    font-family: "Exo 2", sans-serif;
    border: 1px solid var(--grey200);
    cursor: pointer;
}

.analytics-filter-time-item-customExpanded-button:hover {
    opacity: .7;
}

.analytics-options-item-title {
    font-family: "Exo 2", sans-serif;
}

.analytics-options-item-result {
    font-family: "Exo 2", sans-serif;
    font-size: 30px;
    font-weight: 500;
}

.analytics-content-container {
    margin-top: 20px;
    background-color: var(--white);
    padding: 20px;
    border-radius: 5px;
    border: 1px solid var(--grey400);
}

.analytics-content-headers {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--grey200);
    font-family: "Exo 2", sans-serif;
    font-weight: 600;
}

.analytics-content-data-container {
    display: flex;
    justify-content: space-between;
}

.analytics-content-data {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid var(--grey200);
}

.analytics-content-data-col {
    cursor: default;
    font-family: "Exo 2",sans-serif;
    font-size: 15px;
    font-weight: 500;
    
}

.analytics-content-data-col-viewResponse {
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    background-color: var(--black);
    color: var(--white);
    padding: 3px 6px;
    border-radius: 5px;
    font-size: 11px;
    font-weight: 300;
    font-family: "Exo 2", sans-serif;
    cursor: pointer;
}

.analytics-content-data-col-viewResponse:hover {
    opacity: .8;
}

.ant-picker-outlined {
    width: 235px !important;
}