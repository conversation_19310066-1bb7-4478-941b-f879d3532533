import Navbar from "../../../components/UI/Navbar/Navbar";
import "./ResultPage.css";
import { useEffect, useState } from "react";
import Analytics from "./Analytics";
import Responses from "./Responses";
import { useLocation } from "react-router-dom";
import {
  getFormById,
  getResponsesByFormId,
  getTrackingByFormId,
} from "../../../services/http";
import SmallLoading from "../../../components/UI/Loading/SmallLoading";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import FormTopMenu from "../../../components/UI/Menu/FormTopMenu";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const ResultPage = () => {
  const location = useLocation();
  const formId = location.state?.formId;
  const form = location.state?.form;

  const [formState, setFormState] = useState(form);
  const [selectedTabItem, setSelectedTabItem] = useState("analytics");

  const [loading, setLoading] = useState();
  const [views, setViews] = useState();
  const [starts, setStarts] = useState();
  const [responses, setResponses] = useState();
  const [responseData, setResponseData] = useState([]);
  const [originalResponseData, setOriginalResponseData] = useState([]);
  const [responseDataProcessing, setResponseDataProcessing] = useState(false);
  const [viewedResponseId, setViewedResponseId] = useState();

  useEffect(() => {
    async function fetchTrackingDatas() {
      await getTrackings();
    }
    fetchTrackingDatas();
  }, []);

  useEffect(() => {
    async function fetchResponseData() {
      await getResponseData();
    }
    fetchResponseData();
  }, [responseDataProcessing]);

  useEffect(() => {
    if (!form && formId) {
      async function getEditingFormById() {
        await findFormById();
      }
      getEditingFormById();
    }
  }, [setFormState]);

  const findFormById = async () => {
    try {
      setLoading(true);
      const response = await getFormById(formId);
      if (response.status === 200) {
        setFormState(response.data);
      }
    } catch (err) {
      console.log("getting form by id error : ", err);
    } finally {
      setLoading(false);
    }
  };

  const getResponseData = async () => {
    try {
      setResponseDataProcessing(true);
      setLoading(true);
      const response = await getResponsesByFormId(formId);
      if (response.status === 200) {
        const sortedByOrder = response.data
          .filter((res) => res.status === "COMPLETED")
          .sort((a, b) => a.order - b.order);
        setResponseData(sortedByOrder);
        setOriginalResponseData(sortedByOrder);
      }
    } catch (err) {
      console.log("Getting response data error : ", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    async function reloadTracking() {
      await getTrackings();
    }
    reloadTracking();
  }, [responseData]);

  const getTrackings = async () => {
    try {
      const response = await getTrackingByFormId(formId);
      if (response.status === 200) {
        setTrackingDatas(response.data);
      }
    } catch (err) {
      console.log("Getting trackings error : ", err);
    } finally {
      setLoading(false);
    }
  };

  const setTrackingDatas = (trackingData) => {
    const viewData = trackingData.filter((data) => data.type === "view");
    setViews(viewData);
    const startData = trackingData.filter((data) => data.type === "start");
    setStarts(startData);
    const responseData = trackingData.filter(
      (data) => data.type === "response"
    );
    setResponses(responseData);
  };

  const viewResponse = async (responseId) => {
    setViewedResponseId(responseId);
    setSelectedTabItem("responses");
  };

  if (loading) {
    return <SmallLoading />;
  }

  return (
    <>
      <Navbar />
      <FormTopMenu selectedItem="results" form={formState} />
      <div className="resultPage-container">
        <div className="resultPage-top-container">
          <div className="resultPage-top-tab">
            <div
              className={
                selectedTabItem === "analytics"
                  ? "resultPage-top-tab-item-selected"
                  : "resultPage-top-tab-item"
              }
              onClick={() => {
                setResponseData(originalResponseData);
                setViewedResponseId(null);
                setSelectedTabItem("analytics");
              }}
            >
              Analytics
            </div>
            <div
              className={
                selectedTabItem === "responses"
                  ? "resultPage-top-tab-item-selected"
                  : "resultPage-top-tab-item"
              }
              onClick={() => {
                setResponseData(originalResponseData);
                setViewedResponseId(null);
                setSelectedTabItem("responses");
              }}
            >
              <div>Responses</div>
              <div className="resultPage-top-tap-item-budget">
                {responseData?.length}
              </div>
            </div>
          </div>
        </div>

        {selectedTabItem === "analytics" && (
          <Analytics
            views={views}
            setViews={setViews}
            starts={starts}
            responses={responses}
            setResponses={setResponses}
            viewResponse={viewResponse}
          />
        )}
        {selectedTabItem === "responses" && (
          <Responses
            formId={formId}
            responseData={responseData}
            setResponseData={setResponseData}
            setResponseDataProcessing={setResponseDataProcessing}
            viewedResponseId={viewedResponseId}
          />
        )}
      </div>
    </>
  );
};

export default ResultPage;
