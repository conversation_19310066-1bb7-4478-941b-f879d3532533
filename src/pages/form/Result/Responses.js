import { Io<PERSON><PERSON><PERSON>, IoVideocamOutline } from "react-icons/io5";
import "./Responses.css";
import { BsArrowsAngleExpand, BsCalendar3Event } from "react-icons/bs";
import { FiFilter } from "react-icons/fi";
import {
  IoIosArrowBack,
  IoIosArrowDown,
  IoIosArrowForward,
  IoMdClose,
  IoMdSettings,
} from "react-icons/io";
import {
  MdFileDownload,
  MdOutlineCloudUpload,
  MdOutlineFileDownload,
} from "react-icons/md";
import { Drawer, Popover, Table } from "antd";
import { useEffect, useRef, useState } from "react";
import { createStyles } from "antd-style";
import {
  deleteResponseById,
  getHeadersById,
  updateFormHeaders,
} from "../../../services/http";
import SmallLoading from "../../../components/UI/Loading/SmallLoading";

import { getIconByType } from "./HeaderUtil";
import { format, parseISO, startOfDay, subDays } from "date-fns";
import { RiDeleteBin6Line } from "react-icons/ri";
import DeleteModal from "../../../components/UI/Modals/DeleteFormModal";
import TableSettingsModal from "../../../components/UI/Modals/TableSettingsModal";
import { GrPrint } from "react-icons/gr";
import { useReactToPrint } from "react-to-print";
import { DayPicker } from "react-day-picker";
import "react-day-picker/style.css";
import ResponseFilter from "./ResponseFilter";
import DownloadResponseModal from "../../../components/UI/Modals/DownloadResponseModal";

const useStyle = createStyles(({ css, token }) => {
  const { antCls } = token;
  return {
    customTable: css`
      ${antCls}-table {
        ${antCls}-table-container {
          ${antCls}-table-body,
          ${antCls}-table-content {
            scrollbar-width: thin;
            scrollbar-color: #eaeaea transparent;
            scrollbar-gutter: stable;
          }
        }
      }
    `,
  };
});

const Responses = ({
  formId,
  responseData,
  setResponseData,
  setResponseDataProcessing,
  viewedResponseId,
}) => {
  const [originalResponseData, setOriginalResponseData] =
    useState(responseData);

  console.log("originalResponseData : ", originalResponseData);
  const [loading, setLoading] = useState(false);
  const [headers, setHeaders] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [openedDownloadModal, setOpenedDownloadModal] = useState(false);
  const [openedDownloadError, setOpenedDownloadError] = useState();
  const [openedTableSettingsModal, setOpenedTableSettingsModal] =
    useState(false);
  const [tableSettingsResponseError, setTableSettingsResponseError] =
    useState();
  const [openedDeleteResponseModal, setOpenedDeleteResponseModal] =
    useState(false);
  const [deleteResponseError, setDeleteResponseError] = useState();

  const [openedDeleteResponseForDrawer, setOpenedResponseForDrawer] =
    useState(false);
  const [deleteResponseForDrawerError, setDeleteResponseForDrawerError] =
    useState();
  const [selectedResponse, setSelectedResponse] = useState();
  const [openResponseDrawer, setOpenResponseDrawer] = useState(false);

  const [selectedAllTimeOption, setSelectedAllTimeOption] = useState();
  const [allTimeTitle, setAllTimeTitle] = useState("All time");
  const [openedAllTime, setOpenedAllTime] = useState(false);
  const [isActiveAllTimeFilter, setActiveAllTimeFilter] = useState(false);
  const [dayPickerDate, setDayPickerDate] = useState();

  const [openedFilters, setOpenedFilters] = useState(false);
  const [isActiveFilters, setActiveFilters] = useState(false);
  const [filterOptions, setFilterOptions] = useState([]);
  const [filterHeaders, setFilterHeaders] = useState([]);

  const [searchValue, setSearchValue] = useState("");
  const [isActiveSearchFilter, setActiveSearchFilter] = useState(false);

  const openedAllTimeRef = useRef();
  const openedFiltersRef = useRef();

  const selectedResponseRef = useRef();
  const printSelectedResponse = useReactToPrint({
    contentRef: selectedResponseRef,
  });

  const { styles } = useStyle();

  const handleAddFilter = (selectedHeader) => {
    const tempFilterOptions = [...filterOptions];

    setFilterHeaders((prevHeaders) => {
      const updatedFilterHeaders = prevHeaders.map((header) => {
        if (header.id === selectedHeader.id) {
          return { ...header, used: true, isOk: true };
        }
        return header;
      });

      return updatedFilterHeaders;
    });

    const newOption = {
      key: tempFilterOptions.length,
      headerId: "",
    };

    tempFilterOptions.push(newOption);
    setFilterOptions(tempFilterOptions);
  };

  const handleDeleteFilter = (option) => {
    const updatedFilterOptions = filterOptions.filter(
      (filter) => filter.key !== option.key
    );
    const updatedFilterOptionsWithNewKeys = updatedFilterOptions.map(
      (option, index) => ({
        ...option,
        key: index,
      })
    );
    setFilterOptions(updatedFilterOptionsWithNewKeys);
    const updatedFilterHeaders = filterHeaders.map((header) => {
      if (header.id === option.headerId) {
        return { ...header, used: false, isOk: false };
      }
      return header;
    });

    setFilterHeaders(updatedFilterHeaders);
  };

  const onChangeFilterOption = (val, filterId) => {
    const updatedFilterOptions = filterOptions.map((filter) =>
      filter.id === filterId ? { ...filter, selectedValue: val } : filter
    );
    setFilterOptions(updatedFilterOptions);
  };

  useEffect(() => {
    if (viewedResponseId && responseData) {
      const res = responseData.filter(
        (response) => response.id === viewedResponseId
      )[0];
      openResponseDetail(res);

      onSelectChange([res.id]);
    }
  }, [viewedResponseId]);

  useEffect(() => {
    const tempFilterOptions = filterOptions;
    if (headers && headers.length > 0) {
      const firstOption = { key: 0 };
      const tempHeaders = headers;
      const tempFilterTitleOptions = tempHeaders.filter(
        (h) =>
          h.type !== "createdDate" &&
          h.type !== "TimePickerField" &&
          h.type !== "DatePickerField" &&
          h.type !== "FileUploadField" &&
          h.type !== "ImageUploadField" &&
          h.type !== "VideoUploadField" &&
          h.visible === true
      );

      tempFilterTitleOptions.forEach((option) => {
        option.used = false;
      });

      if (filterHeaders.length === 0) {
        setFilterHeaders(tempFilterTitleOptions);
      }
      if (tempFilterOptions.length === 0) {
        tempFilterOptions.push(firstOption);
      }
    }

    setFilterOptions(tempFilterOptions);
  }, [filterOptions, headers]);

  useEffect(() => {
    async function getHeaders() {
      await getHeadersByFormId();
    }
    getHeaders();
  }, []);

  useEffect(() => {}, [headers]);

  useEffect(() => {
    document.addEventListener("mousedown", handleOutsideAllTime);

    return () => {
      document.removeEventListener("mousedown", handleOutsideAllTime);
    };
  }, []);

  const handleOutsideAllTime = (event) => {
    if (
      openedAllTimeRef.current &&
      event.target.className !== "response-top-menu-filter-item" &&
      event.target.className !== "response-top-menu-filter-item-icon" &&
      event.target.className !== "response-top-menu-filter-item-title" &&
      event.target.className !== "response-top-menu-filter-allTime-opened" &&
      event.target.className !== "response-top-menu-filter-item-filtered" &&
      event.target.className !== "" &&
      event.target.className.baseVal !== "" &&
      !openedAllTimeRef.current.contains(event.target)
    ) {
      setOpenedAllTime(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleOutsideFilters);

    return () => {
      document.removeEventListener("mousedown", handleOutsideFilters);
    };
  }, []);

  const handleOutsideFilters = (event) => {
    const filterContainer = openedFiltersRef.current;
    if (
      filterContainer &&
      !filterContainer.contains(event.target) &&
      event.target.className !== "response-top-menu-filter-item-filters" &&
      event.target.className !== "response-top-menu-filter-item-filters-icon" &&
      event.target.className !== "responseFilter-filter-option" &&
      event.target.className !== "ant-select-selection-item" &&
      event.target.className !== "ant-select-item-option-content" &&
      event.target.className !==
        "ant-select-item ant-select-item-option ant-select-item-option-active" &&
      event.target.className !==
        "ant-select-item ant-select-item-option ant-select-item-option-active ant-select-item-option-selected" &&
      event.target.className !==
        "ant-select-dropdown css-dev-only-do-not-override-zg0ahe ant-select-dropdown-placement-bottomLeft" &&
      event.target.className.baseVal !==
        "response-top-menu-filter-item-filters-icon-svg" &&
      event.target.className !==
        "response-top-menu-filter-item-icon-filtered" &&
      event.target.className !==
        "response-top-menu-filter-item-title-filtered" &&
      event.target.className !==
        "response-top-menu-filter-allTime-opened-filtered" &&
      event.target.className !== "ant-select-selection-search-input" &&
      event.target.className !== "responseFilter-filter-option" &&
      event.target.className !== "responseFilter-filter-option-title" &&
      event.target.className !== "responseFilter-filter-value" &&
      event.target.className !== "" &&
      event.target.className.baseVal !== ""
    ) {
      setOpenedFilters(false);
    }
  };

  const getHeadersByFormId = async () => {
    try {
      setLoading(false);
      const response = await getHeadersById(formId);

      if (response.status === 200) {
        const sortedHeadersByOrder = response.data.sort(
          (a, b) => a.order - b.order
        );
        setHeaders(sortedHeadersByOrder);
      }
    } catch (err) {
    } finally {
      setLoading(false);
    }
  };

  const deleteResponse = async () => {
    try {
      setResponseDataProcessing(true);
      setLoading(true);
      const deletePromises = selectedRowKeys.map(async (rowKey) => {
        const deletingResponse = responseData.find((r) => r.id === rowKey);
        if (!deletingResponse) {
          return;
        }
        try {
          const response = await deleteResponseById(deletingResponse.id);
          if (response.status === 200) {
            const deletedResponseIndex = responseData.indexOf(deletingResponse);
            responseData.splice(deletedResponseIndex, 1);
          } else {
            console.error(
              `Failed to delete response with ID: ${deletingResponse.id}`
            );
          }
        } catch (err) {
          console.error("Error deleting response:", err);
        }
      });
      await Promise.all(deletePromises);
    } catch (err) {
      console.error("Error in deleteResponse:", err);
    } finally {
      setResponseDataProcessing(false);
      setLoading(false);
      setOpenedDeleteResponseModal(false);
    }
  };

  const deleteResponseForDrawer = async () => {
    try {
      setResponseDataProcessing(true);
      setLoading(true);
      const response = await deleteResponseById(selectedResponse.id);

      if (response.status === 200) {
        const deletedResponseIndex = responseData.findIndex(
          (response) => response.id === selectedResponse.id
        );

        if (deletedResponseIndex > -1) {
          const updatedResponseData = [...responseData];
          updatedResponseData.splice(deletedResponseIndex, 1);

          setResponseData(updatedResponseData);

          if (updatedResponseData.length === 0) {
            setSelectedResponse(null);
            setOpenResponseDrawer(false);
            setResponseDataProcessing(false);
          } else {
            handleNext();
          }
        }
      }
    } catch (err) {
      console.log("err : ", err);
    } finally {
      setLoading(false);
      setOpenedResponseForDrawer(false);
    }
  };

  const openResponseDetail = (response) => {
    setSelectedResponse(response);
    setOpenResponseDrawer(true);
  };

  const handleNext = () => {
    const currentIndex = responseData.findIndex(
      (response) => response.id === selectedResponse.id
    );
    const nextIndex = (currentIndex + 1) % responseData.length;
    setSelectedResponse(responseData[nextIndex]);
  };

  const handlePrevious = () => {
    const currentIndex = responseData.findIndex(
      (response) => response.id === selectedResponse.id
    );
    const prevIndex =
      (currentIndex - 1 + responseData.length) % responseData.length;
    setSelectedResponse(responseData[prevIndex]);
  };

  const getFileSize = (fileSize) => {
    if (fileSize < 1024 * 1024) {
      return (fileSize / 1024).toFixed(2) + " KB";
    } else {
      return (fileSize / (1024 * 1024)).toFixed(2) + " MB";
    }
  };

  const handleDownload = (fileUrl, fileName) => {
    const a = document.createElement("a");
    a.href = fileUrl;
    a.download = fileName;
    a.click();
  };

  const getFieldResponseValue = (fieldResponse) => {
    if (
      fieldResponse.type === "SelectMenuField" ||
      fieldResponse.type === "MultiSelectMenuField" ||
      fieldResponse.type === "SingleSelectMenuField" ||
      fieldResponse.type === "CheckboxField"
    ) {
      return fieldResponse.value.map((val, index) => (
        <div
          className="response-selectedResponse-item-multiple-answer"
          key={index}
        >
          {val}
        </div>
      ));
    } else if (
      fieldResponse.type === "FileUploadField" ||
      fieldResponse.type === "ImageUploadField" ||
      fieldResponse.type === "VideoUploadField"
    ) {
      return fieldResponse.files.map((val, index) => (
        <div className="response-uploadField" key={index}>
          <div className="response-uploadField-field" style={{ width: "90%" }}>
            {val.fieldType === "ImageUploadField" ? (
              <img
                className="response-uploadField-field-image"
                src={`${process.env.REACT_APP_BACKEND_URL}/api/v1/response/files/images/${val.uploadedName}`}
                alt="response"
              />
            ) : val.fieldType === "FileUploadField" ? (
              <div className="response-uploadField-field-icon">
                <MdOutlineCloudUpload />
              </div>
            ) : (
              <div className="response-uploadField-field-icon">
                <IoVideocamOutline />
              </div>
            )}

            <div style={{ width: "100%" }}>
              <div
                className="response-uploadField-field-title"
                style={{ maxWidth: "100%" }}
              >
                {val.originalName}
              </div>
              <div className="response-uploadField-field-size">
                {" "}
                {getFileSize(val.size)}
              </div>
            </div>
          </div>
        </div>
      ));
    } else {
      return (
        <div className="response-selectedResponse-item-answer">
          {fieldResponse.value}
        </div>
      );
    }
  };

  const columns = headers
    .filter((header) => header.visible !== false)
    .map((header, index) => ({
      width: header.type === "createdDate" ? 200 : 0,
      title: (
        <div key={index}>
          {header.type === "createdDate" ? (
            <>
              {selectedRowKeys && selectedRowKeys.length > 0 && (
                <div className="response-column-selected-outer-container">
                  <div className="response-column-selected-container">
                    <div>
                      {"["} <strong>{selectedRowKeys.length}</strong> {"]"}{" "}
                      selected
                    </div>{" "}
                    {/*
                  <div className="response-column-selected-close">
                    <MdClose />
                  </div>
                  */}
                  </div>
                  <div className="response-column-selected-actions">
                    <div className="response-column-selected-action-download">
                      <MdFileDownload />
                    </div>
                    <div
                      className="response-column-selected-action-delete"
                      onClick={() => setOpenedDeleteResponseModal(true)}
                    >
                      <RiDeleteBin6Line />
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="response-column-item-container">
              <div className="response-column-item">
                <div
                  className="response-column-item-icon"
                  /*style={{ backgroundColor: getIconBgColor(header.type) }}*/
                >
                  {getIconByType(header.type)}
                </div>
                <div className="response-column-item-title">{header.title}</div>
              </div>
            </div>
          )}
        </div>
      ),
      dataIndex: header?.dataIndex?.replaceAll("-", ""),
      key: header.key,
      fixed: header.fixed,
    }));

  const dataSource = responseData?.map((response) => {
    return {
      key: response.id,
      ...response.fieldResponses.reduce(
        (acc, fieldResponse) => {
          const fieldKey = fieldResponse.dataIndex?.replaceAll("-", "");
          const fieldType = fieldResponse.type;
          let value = fieldResponse.value;
          let files = fieldResponse.files;

          if (fieldResponse.visible === false) {
            return acc;
          }

          if (
            fieldType === "SelectMenuField" ||
            fieldType === "MultiSelectMenuField" ||
            fieldType === "SingleSelectMenuField" ||
            fieldType === "CheckboxField"
          ) {
            value = value.join(", ");
            acc[fieldKey] = value.split(",")?.map((val, index) => (
              <div className="response-multipleField" key={index}>
                {val}
              </div>
            ));
          } else if (fieldType === "FileUploadField") {
            acc[fieldKey] = files?.map((file, index) => (
              <div key={index} className="response-uploadField">
                <div className="response-uploadField-field">
                  <div className="response-uploadField-field-icon">
                    <MdOutlineCloudUpload />
                  </div>
                  <div>
                    <div className="response-uploadField-field-title">
                      {file.originalName}
                    </div>
                    <div className="response-uploadField-field-size">
                      {" "}
                      {getFileSize(file.size)}
                    </div>
                  </div>
                </div>
                <div
                  className="response-uploadField-download"
                  onClick={() =>
                    handleDownload(
                      `${process.env.REACT_APP_BACKEND_URL}/api/v1/response/files/download/files/${file.uploadedName}`,
                      file.originalName
                    )
                  }
                >
                  <div className="response-uploadField-download-icon">
                    <MdOutlineFileDownload />
                  </div>
                </div>
              </div>
            ));
          } else if (fieldType === "ImageUploadField") {
            acc[fieldKey] = files?.map((file, index) => (
              <div key={index} className="response-uploadField">
                <div className="response-uploadField-field">
                  <img
                    className="response-uploadField-field-image"
                    src={`${process.env.REACT_APP_BACKEND_URL}/api/v1/response/files/images/${file.uploadedName}`}
                    alt="uploadedImage"
                  />
                  <div>
                    <div className="response-uploadField-field-title">
                      {file.originalName}
                    </div>
                    <div className="response-uploadField-field-size">
                      {" "}
                      {getFileSize(file.size)}
                    </div>
                  </div>
                </div>
                <div
                  className="response-uploadField-download"
                  onClick={() =>
                    handleDownload(
                      `${process.env.REACT_APP_BACKEND_URL}/api/v1/response/files/download/images/${file.uploadedName}`,
                      file.originalName
                    )
                  }
                >
                  <div className="response-uploadField-download-icon">
                    <MdOutlineFileDownload />
                  </div>
                </div>
              </div>
            ));
          } else if (fieldType === "VideoUploadField") {
            acc[fieldKey] = files?.map((file, index) => (
              <div key={index} className="response-uploadField">
                <div className="response-uploadField-field">
                  <div className="response-uploadField-field-icon">
                    <IoVideocamOutline />
                  </div>
                  <div>
                    <div className="response-uploadField-field-title">
                      {file.originalName}
                    </div>
                    <div className="response-uploadField-field-size">
                      {" "}
                      {getFileSize(file.size)}
                    </div>
                  </div>
                </div>
                <div
                  className="response-uploadField-download"
                  onClick={() =>
                    handleDownload(
                      `${process.env.REACT_APP_BACKEND_URL}/api/v1/response/files/download/videos/${file.uploadedName}`,
                      file.originalName
                    )
                  }
                >
                  <div className="response-uploadField-download-icon">
                    <MdOutlineFileDownload />
                  </div>
                </div>
              </div>
            ));
          } else {
            acc[fieldKey] = <div className="response-item">{value[0]}</div>;
          }

          return acc;
        },

        {
          createdDate: (
            <div className="response-createdDate-container">
              <div>
                <div style={{ fontWeight: "500" }}>
                  {format(new Date(response.createdDate), "MMM dd, yyyy")}
                </div>
                <div style={{ fontWeight: "500", opacity: ".7" }}>
                  {format(new Date(response.createdDate), "HH:MM")}
                </div>
              </div>
              <Popover
                placement="bottom"
                title="Open this response"
                overlayInnerStyle={{
                  height: "45px",
                  textAlign: "center",
                  padding: "10px 0 0 0",
                }}
              >
                <div
                  className="response-expand-icon"
                  onClick={() => openResponseDetail(response)}
                >
                  <BsArrowsAngleExpand />
                </div>
              </Popover>
            </div>
          ),
          googleAuthenticate: (
            <div>
              <div>
                {response.googleUser?.name || "Unknown"} <br />
              </div>
              <div>
                {response.googleUser?.email || "No Email"} <br />
              </div>
            </div>
          ),
        }
      ),
    };
  });

  const onSelectChange = (newSelectedRowKeys, selectedRows) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const applyHeaderChanges = async (updatedHeaderList) => {
    try {
      setLoading(true);
      const response = await updateFormHeaders(updatedHeaderList);
      if (response.status === 200) {
        setHeaders(updatedHeaderList);
        const sortedData = response.data.sort((a, b) => a.order - b.order);
        const tempFilterHeaders = sortedData.filter(
          (h) => h.type !== "createdDate" && h.visible
        );
        setFilterHeaders(tempFilterHeaders);
      }
    } catch (err) {
      console.log("Updating Headers error : ", err);
    } finally {
      setLoading(false);
    }
  };

  const getTodayDate = () => subDays(new Date(), 1);
  const getThisWeekDate = () => subDays(new Date(), 7);
  const getThisMonth = () => subDays(new Date(), 31);
  const getThisYear = () => subDays(new Date(), 366);

  const onChangeAllTimeOption = (option) => {
    setSelectedAllTimeOption(option);
    const today = subDays(new Date(), 0);
    switch (option) {
      case "today":
        setDayPickerDate({
          from: subDays(new Date(), 0),
          to: subDays(new Date(), 0),
        });
        break;
      case "thisWeek":
        setDayPickerDate({ from: getThisWeekDate(), to: today });
        break;
      case "thisMonth":
        setDayPickerDate({ from: getThisMonth(), to: today });
        break;
      case "thisYear":
        setDayPickerDate({ from: getThisYear(), to: today });
    }
  };

  const applyAllTimeFilter = (fromClear) => {
    setLoading(true);
    let tempFilteredResponseData;
    if (!fromClear) {
      tempFilteredResponseData = [...responseData];
    } else {
      tempFilteredResponseData = [...originalResponseData];
    }
    const todayDate = getTodayDate();

    if (selectedAllTimeOption === "today") {
      tempFilteredResponseData = tempFilteredResponseData.filter((response) => {
        const createdDate = parseISO(response.createdDate);
        const startOfDayCreatedDate = startOfDay(createdDate);
        setAllTimeTitle("Today");

        return startOfDayCreatedDate >= todayDate;
      });
    } else if (selectedAllTimeOption === "thisWeek") {
      tempFilteredResponseData = tempFilteredResponseData.filter((response) => {
        const createdDate = parseISO(response.createdDate);
        const sevenDaysAgo = getThisWeekDate();

        const formattedDate = format(sevenDaysAgo, "MMM dd, yyyy");
        setAllTimeTitle(`${formattedDate} - Today`);

        return createdDate >= sevenDaysAgo;
      });
    } else if (selectedAllTimeOption === "thisMonth") {
      tempFilteredResponseData = tempFilteredResponseData.filter((response) => {
        const createdDate = parseISO(response.createdDate);
        const oneMonthAgo = getThisMonth();

        const formattedDate = format(oneMonthAgo, "MMM dd, yyyy");
        setAllTimeTitle(`${formattedDate} - Today`);

        return createdDate >= oneMonthAgo;
      });
    } else if (selectedAllTimeOption === "thisYear") {
      tempFilteredResponseData = tempFilteredResponseData.filter((response) => {
        const createdDate = parseISO(response.createdDate);
        const oneYearAgo = getThisYear();

        const formattedDate = format(oneYearAgo, "MMM dd, yyyy");
        setAllTimeTitle(`${formattedDate} - Today`);

        return createdDate >= oneYearAgo;
      });
    } else {
      const { from, to } = dayPickerDate;

      tempFilteredResponseData = tempFilteredResponseData.filter((response) => {
        const createdDate = parseISO(response.createdDate);

        const formattedFromDate = format(
          from.setHours(0, 0, 0, 0),
          "MMM dd, yyyy"
        );
        const formattedToDate = format(
          to.setHours(23, 59, 59, 999),
          "MMM dd, yyyy"
        );

        setAllTimeTitle(`${formattedFromDate} - ${formattedToDate}`);

        return createdDate >= from && createdDate <= to;
      });
    }

    setActiveAllTimeFilter(true);
    setResponseData(tempFilteredResponseData);
    setOpenedAllTime(false);
    setTimeout(() => {
      setLoading(false);
    }, 300);
  };

  const clearAllTimeFilter = () => {
    setLoading(true);
    setActiveAllTimeFilter(false);
    setAllTimeTitle("");

    setResponseData([...originalResponseData]);

    if (isActiveFilters) {
      applyFilters(true);
    }
    if (isActiveSearchFilter) {
      searchResponse();
    }

    setTimeout(() => {
      setLoading(false);
    }, 300);
  };

  const clearFilters = () => {
    setLoading(true);
    setActiveFilters(false);
    const clearedFilterOptions = filterOptions.map((filter) => ({
      key: 0,
    }));
    setFilterOptions(clearedFilterOptions);
    setResponseData([...originalResponseData]);

    const clearedFilterHeaders = filterHeaders.map((header) => ({
      ...header,
      used: false,
      isOk: false,
    }));

    setFilterHeaders(clearedFilterHeaders);
    if (isActiveAllTimeFilter && (selectedAllTimeOption || dayPickerDate)) {
      applyAllTimeFilter(true);
    }
    if (isActiveSearchFilter) {
      searchResponse();
    }

    setActiveFilters(false);
    setTimeout(() => {
      setLoading(false);
    }, 300);
  };

  const applyFilters = (fromClear) => {
    setLoading(true);
    let tempResponseData;
    if (!fromClear) {
      tempResponseData = [...responseData];
    } else {
      tempResponseData = [...originalResponseData];
    }
    const clearedFilterOptions = filterOptions.filter(
      (filter) => filter.value !== ""
    );

    const filteredData = tempResponseData.filter((response) => {
      return clearedFilterOptions.every((filter) => {
        const fieldResponse = response.fieldResponses.find(
          (field) => field.dataIndex === filter.dataIndex
        );

        if (!fieldResponse) {
          return false;
        }

        const fieldValue = fieldResponse.value;
        const filterValue = filter.value;
        const operator = filter.operator;

        switch (operator) {
          case "equals":
            if (Array.isArray(fieldValue)) {
              return fieldValue.length === 1 && fieldValue[0] === filterValue;
            }
            return fieldValue === filterValue;

          case "includes":
            if (Array.isArray(fieldValue)) {
              return fieldValue.includes(filterValue);
            }
            return false;

          default:
            return false;
        }
      });
    });

    setResponseData(filteredData);
    setOpenedFilters(false);
    setActiveFilters(true);
    setTimeout(() => {
      setLoading(false);
    }, 300);
  };

  const onSelectDayPicker = (dates) => {
    setSelectedAllTimeOption();
    setDayPickerDate(dates);
  };

  const onChangeSearch = (e) => {
    const value = e.target.value;
    setSearchValue(value);
  };

  const searchResponse = () => {
    const query = searchValue;
    if (query === "") {
      setResponseData([...originalResponseData]);
    } else {
      const filtered = responseData.filter((item) =>
        item.fieldResponses.some((response) =>
          headers.some(
            (header) =>
              response.dataIndex === header.dataIndex &&
              response.value.some((val) => val.toLowerCase().includes(query))
          )
        )
      );
      setActiveSearchFilter(true);
      setResponseData(filtered);
    }
  };

  const clearSearchFilter = () => {
    setLoading(true);
    setActiveSearchFilter(false);
    setSearchValue("");
    const tempResponseData = [...originalResponseData];
    if (isActiveAllTimeFilter && (selectedAllTimeOption || dayPickerDate)) {
      applyAllTimeFilter(true);
    }
    if (isActiveFilters) {
      applyFilters(true);
    }

    if (!isActiveAllTimeFilter && !isActiveFilters) {
      setResponseData(tempResponseData);
    }

    setTimeout(() => {
      setLoading(false);
    }, 300);
  };

  if (loading) {
    return <SmallLoading />;
  }

  return (
    <>
      <div className="responses-container">
        <div className="response-top-menu-container">
          <div className="response-top-menu-filter-container">
            <div className="response-top-menu-filter-search-item">
              <div className="response-top-menu-filter-item-search">
                <input
                  value={searchValue}
                  placeholder="Search responses"
                  className="response-top-menu-filter-item-search-input"
                  onChange={onChangeSearch}
                />
                {isActiveSearchFilter ? (
                  <div
                    className="response-top-menu-filter-item-search-button-filtered"
                    onClick={clearSearchFilter}
                  >
                    <IoMdClose />
                  </div>
                ) : (
                  <div
                    className="response-top-menu-filter-item-search-button"
                    onClick={searchResponse}
                  >
                    <IoSearch />
                  </div>
                )}
              </div>
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                position: "relative",
              }}
            >
              {isActiveAllTimeFilter ? (
                <div className="response-top-menu-allTime-item-filtered-container">
                  <div
                    style={{ backgroundColor: "#EBF4F6" }}
                    className="response-top-menu-filter-item-filtered"
                    onClick={() => {
                      // setSelectedAllTimeOption();
                      setOpenedAllTime(!openedAllTime);
                    }}
                  >
                    <div className="response-top-menu-filter-item-icon-filtered">
                      <BsCalendar3Event />
                    </div>

                    <div className="response-top-menu-filter-item-title-filtered">
                      {allTimeTitle}
                    </div>
                  </div>
                  <IoMdClose
                    style={{ marginLeft: "10px" }}
                    className="response-top-menu-filter-item-filtered-clear"
                    onClick={clearAllTimeFilter}
                  />
                </div>
              ) : (
                <div
                  className="response-top-menu-filter-item"
                  onClick={() => {
                    setActiveAllTimeFilter(false);
                    // setSelectedAllTimeOption();
                    setOpenedAllTime(!openedAllTime);
                  }}
                >
                  <div className="response-top-menu-filter-item-icon">
                    <BsCalendar3Event />
                  </div>

                  <div className="response-top-menu-filter-item-title">
                    All time
                  </div>

                  <IoIosArrowDown style={{ marginLeft: "10px" }} />
                </div>
              )}

              <div
                ref={openedAllTimeRef}
                className={
                  openedAllTime
                    ? "response-top-menu-filter-allTime-opened"
                    : "response-top-menu-filter-allTime"
                }
              >
                <div className="response-top-menu-filter-allTime-left">
                  <div
                    className={
                      selectedAllTimeOption === "today"
                        ? "response-top-menu-filter-allTime-left-item-selected"
                        : "response-top-menu-filter-allTime-left-item"
                    }
                    onClick={() => onChangeAllTimeOption("today")}
                  >
                    Today
                  </div>
                  <div
                    className={
                      selectedAllTimeOption === "thisWeek"
                        ? "response-top-menu-filter-allTime-left-item-selected"
                        : "response-top-menu-filter-allTime-left-item"
                    }
                    onClick={() => onChangeAllTimeOption("thisWeek")}
                  >
                    This week
                  </div>
                  <div
                    className={
                      selectedAllTimeOption === "thisMonth"
                        ? "response-top-menu-filter-allTime-left-item-selected"
                        : "response-top-menu-filter-allTime-left-item"
                    }
                    onClick={() => onChangeAllTimeOption("thisMonth")}
                  >
                    This month
                  </div>
                  <div
                    className={
                      selectedAllTimeOption === "thisYear"
                        ? "response-top-menu-filter-allTime-left-item-selected"
                        : "response-top-menu-filter-allTime-left-item"
                    }
                    onClick={() => onChangeAllTimeOption("thisYear")}
                  >
                    This year
                  </div>
                </div>
                <div className="response-top-menu-filter-allTime-right">
                  <DayPicker
                    mode="range"
                    selected={dayPickerDate}
                    onSelect={onSelectDayPicker}
                  />
                  <div className="response-top-menu-filter-allTime-right-buttons">
                    <div
                      className="response-top-menu-filter-allTime-right-button-cancel"
                      onClick={() => setOpenedAllTime(false)}
                    >
                      Cancel
                    </div>
                    <div
                      className="response-top-menu-filter-allTime-right-button-apply"
                      onClick={() => applyAllTimeFilter(false)}
                    >
                      Apply
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                position: "relative",
              }}
            >
              {isActiveFilters ? (
                <div className="response-top-menu-allTime-item-filtered-container">
                  <div
                    style={{ backgroundColor: "#EBF4F6" }}
                    className="response-top-menu-filter-item-filtered"
                    onClick={() => {
                      // setSelectedAllTimeOption();
                      setOpenedFilters(!openedFilters);
                    }}
                  >
                    <div className="response-top-menu-filter-item-icon-filtered">
                      <FiFilter className="response-top-menu-filter-item-filters-icon-svg" />
                    </div>

                    <div className="response-top-menu-filter-item-title-filtered">
                      Filters
                    </div>
                  </div>
                  <IoMdClose
                    style={{ marginLeft: "10px" }}
                    className="response-top-menu-filter-item-filtered-clear"
                    onClick={clearFilters}
                  />
                </div>
              ) : (
                <div
                  className="response-top-menu-filter-item-filters"
                  onClick={() => setOpenedFilters(!openedFilters)}
                >
                  <div className="response-top-menu-filter-item-filters-icon">
                    <FiFilter className="response-top-menu-filter-item-filters-icon-svg" />
                  </div>
                  <div className="response-top-menu-filter-item-filters-title">
                    Filters
                  </div>
                </div>
              )}

              <div
                ref={openedFiltersRef}
                className={
                  openedFilters
                    ? "response-top-menu-filters-opened"
                    : "response-top-menu-filters-closed"
                }
              >
                <ResponseFilter
                  filterOptions={filterOptions}
                  setFilterOptions={setFilterOptions}
                  filterHeaders={filterHeaders}
                  setFilterHeaders={setFilterHeaders}
                  onChangeFilterOption={onChangeFilterOption}
                  handleAddFilter={handleAddFilter}
                  handleDeleteFilter={handleDeleteFilter}
                  applyFilters={applyFilters}
                />
              </div>
            </div>
          </div>
          <div className="response-top-menu-setting-container">
            <div
              className="response-top-menu-setting-item"
              onClick={() => setOpenedTableSettingsModal(true)}
            >
              <div className="response-top-menu-setting-item-icon">
                <IoMdSettings />
              </div>
              <div className="response-top-menu-setting-item-title">
                Table settings
              </div>
            </div>
            <div
              className="response-top-menu-setting-item"
              onClick={() => setOpenedDownloadModal(true)}
            >
              <div className="response-top-menu-setting-item-icon">
                <MdFileDownload />
              </div>
              <div className="response-top-menu-setting-item-title">
                Download
              </div>
            </div>
          </div>
        </div>

        <div className="response-table-container">
          <Table
            bordered
            rowSelection={rowSelection}
            rowKey="key"
            className={styles.customTable}
            columns={columns}
            dataSource={dataSource}
            scroll={{
              x: "max-content",
            }}
            pagination={{ pageSize: 8 }}
          />
        </div>
      </div>
      <DownloadResponseModal
        open={openedDownloadModal}
        handleOk={() => {}}
        handleClose={() => setOpenedDownloadModal(false)}
        error={openedDownloadError}
        title="Choose a file to download"
        buttonTitle="Download"
        allResponses={originalResponseData}
        filteredResponses={responseData}
        selectedRows={selectedRowKeys}
        isActiveFiltered={isActiveAllTimeFilter || isActiveFilters}
        isActiveSelected={selectedRowKeys && selectedRowKeys.length > 0}
        loading={loading}
        setLoading={setLoading}
      />
      <TableSettingsModal
        open={openedTableSettingsModal}
        handleOk={applyHeaderChanges}
        handleClose={() => setOpenedTableSettingsModal(false)}
        headerList={headers}
        setHeaders={setHeaders}
        title="Table settings"
        buttonTitle="Apply"
        loading={loading}
        error={tableSettingsResponseError}
        setError={setTableSettingsResponseError}
      />
      <DeleteModal
        open={openedDeleteResponseModal}
        handleOk={deleteResponse}
        handleClose={() => setOpenedDeleteResponseModal(false)}
        title="Delete this response"
        message="Deleting this response will permanently remove all associated data. "
        message2="This action cannot be undone. "
        message3="Are you sure you want to proceed?"
        buttonTitle="Delete"
        loading={loading}
        error={deleteResponseError}
        setError={setDeleteResponseError}
      />

      <DeleteModal
        open={openedDeleteResponseForDrawer}
        handleOk={deleteResponseForDrawer}
        handleClose={() => setOpenedResponseForDrawer(false)}
        title="Delete this response"
        message="Deleting this response will permanently remove all associated data. "
        message2="This action cannot be undone. "
        message3="Are you sure you want to proceed?"
        buttonTitle="Delete"
        loading={loading}
        error={deleteResponseForDrawerError}
        setError={setDeleteResponseForDrawerError}
      />

      <Drawer
        width={550}
        destroyOnClose
        extra={
          <div className="response-selectedResponse-header-container">
            <div className="response-selectedResponse-header-date-container">
              <div className="response-selectedResponse-header-selections">
                <div
                  className="response-selectedResponse-header-selection"
                  onClick={handlePrevious}
                >
                  <IoIosArrowBack />
                </div>
                <div
                  className="response-selectedResponse-header-selection"
                  onClick={handleNext}
                >
                  <IoIosArrowForward />
                </div>
              </div>
              <div className="response-selectedResponse-header-date">
                {selectedResponse &&
                  selectedResponse.createdDate &&
                  format(
                    new Date(selectedResponse.createdDate),
                    "MMM dd, yyyy HH:mm"
                  )}
              </div>
            </div>

            <div className="response-selectedResponse-header-action-container">
              <Popover
                placement="bottom"
                title="Print this response"
                overlayInnerStyle={{
                  height: "45px",
                  textAlign: "center",
                  padding: "10px 0 0 0",
                }}
                onClick={printSelectedResponse}
              >
                <div className="response-selectedResponse-print">
                  <div className="response-selectedResponse-print-icon">
                    <GrPrint />
                  </div>
                </div>
              </Popover>

              <Popover
                placement="bottom"
                title="Delete this response"
                overlayInnerStyle={{
                  height: "45px",
                  textAlign: "center",
                  padding: "10px 0 0 0",
                }}
                onClick={() => {
                  setOpenedResponseForDrawer(true);
                }}
              >
                <div className="response-selectedResponse-delete">
                  <div className="response-selectedResponse-delete-icon">
                    <RiDeleteBin6Line />
                  </div>
                </div>
              </Popover>
            </div>
          </div>
        }
        closable={() => {
          setOpenResponseDrawer(false);
        }}
        onClose={() => {
          setOpenResponseDrawer(false);
        }}
        open={openResponseDrawer}
      >
        <div>
          <div ref={selectedResponseRef}>
            {selectedResponse && selectedResponse.createdDate && (
              <div
                style={{
                  marginBottom: "20px",
                  paddingRight: "50px",
                }}
              >
                {format(
                  new Date(selectedResponse?.createdDate),
                  "MMM dd, yyyy HH:mm"
                )}
              </div>
            )}

            {selectedResponse?.googleUser && (
              <div
                className="response-selectedResponse-item"
                key={selectedResponse?.googleUser.id}
              >
                <div className="response-selectedResponse-item-innerContainer">
                  <div className="response-selectedResponse-item-icon">
                    {getIconByType("GoogleAuthenticateField")}
                  </div>
                  <div className="response-selectedResponse-item-title">
                    Google Authenticate
                  </div>
                </div>
                <div
                  className="response-selectedResponse-item-answer"
                  style={{ display: "flex" }}
                >
                  <div>{selectedResponse?.googleUser?.name}</div>
                  <div style={{ opacity: ".7", marginLeft: "10px" }}>
                    {"("}
                    {selectedResponse?.googleUser?.email}
                    {")"}
                  </div>
                </div>
              </div>
            )}
            {selectedResponse?.fieldResponses?.map((fieldResponse) => (
              <div
                className="response-selectedResponse-item"
                key={fieldResponse.id}
              >
                <div className="response-selectedResponse-item-innerContainer">
                  <div className="response-selectedResponse-item-icon">
                    {getIconByType(fieldResponse.type)}
                  </div>
                  <div className="response-selectedResponse-item-title">
                    {fieldResponse.title}
                  </div>
                </div>
                <div className="response-selectedResponse-item-answer-container">
                  {getFieldResponseValue(fieldResponse)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default Responses;
