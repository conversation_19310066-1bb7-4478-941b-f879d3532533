.responseFilter-container {
    width: 450px;
    height: 100%;
    min-height: 250px;
    max-height: 600px;
    position: relative;
}

.responseFilter-title {
    font-family: "Exo 2", sans-serif;
    font-weight: 400;
    font-size: 13px;
    color: var(--black);
    margin-bottom: 15px;
    opacity: .6;
}

.responseFilter-item-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow-y: auto;
    max-height: 400px;
    padding: 10px 0 50px 0;
}

.responseFilter-item {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 20px;
}

.responseFilter-filter-options-container {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid var(--grey200);
    margin-bottom: 10px;
    height: 30px;
    border-radius: 5px;
}

.responseFilter-filter-options {
    display: flex;
    width: 100%;
}


.responseFilter-filter-option {
    display: flex;
    align-items: center;
}

.responseFilter-filter-option-icon {
    padding: 3px;
    border: 1px solid var(--grey200);
    border-radius: 3px;
    margin-right: 10px;
}

.responseFilter-item-innerContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.responseFilter-filter-operator-container {
    display: flex;
    align-items: center;
    height: 30px;
    width: 40%;
    border: 1px solid var(--grey200);
    border-radius: 5px;
    margin-right: 5px;
}

.responseFilter-filter-operator {
    width: 100%
}

.responseFilter-filter-value {
    height: 30px;
    border: 1px solid var(--grey200);
    border-radius: 5px;
    width: 60%;
    padding-left: 5px;
    font-size: 14px;
    font-weight: 400;
}

.responseFilter-filter-delete {
    display: flex;
    justify-content: center;
    align-items: center;
    height: fit-content;
    padding: 5px;
    background-color: var(--grey200);
    color: var(--black);
    border-radius: 5px;
    cursor: pointer;
    margin-right: 20px;
}

.responseFilter-filter-delete:hover {
    background-color: var(--red200);
    color: var(--white);
    
    transition: .3s all;
}

.responseFilter-addFilter-button {
    padding: 5px 10px;
    background-color: var(--green400);
    color: var(--white);
    width: fit-content;
    border-radius: 5px;
    font-family: "Exo 2", sans-serif;
    font-size: 14px;
    cursor: pointer;
}

.responseFilter-addFilter-button:hover {
    opacity: .7;
    transition: .3s all;
}

.responseFilter-filter-buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    width: 100%;
    bottom: 0;
    right: 0;
    margin-top: 20px;
    padding-top: 10px;
    background-color: var(--white);
    border-top: 1px solid var(--grey200);
}

.responseFilter-filter-button-cancel {
    padding: 5px 10px;
    border: 1px solid var(--grey200);
    border-radius: 5px;
    background-color: var(--grey300);
    color: var(--black);
    font-family: "Exo 2", sans-serif;
    font-size: 13px;
    margin-right: 15px;
    cursor: pointer;
}

.responseFilter-filter-button-cancel:hover {
    opacity: .7;
    transition: .3s all;
}

.responseFilter-filter-button-apply {
    padding: 5px 10px;
    border: 1px solid var(--black);
    border-radius: 5px;
    background-color: var(--black);
    color: var(--white);
    font-family: "Exo 2", sans-serif;
    font-size: 13px;
    cursor: pointer;
}

.responseFilter-filter-button-apply:hover {
    opacity: .7;
    transition: .3s all;
}

.responseFilter-andDivider {
    display: flex; 
    align-items: center;
    font-family: "Exo 2", sans-serif;
    font-size: 13px;
    font-weight: 400;
    margin-bottom: 20px;
  }
  
  .responseFilter-andDivider::before {
    content: '';
    flex-grow: 1;
    border-top: 1px solid var(--grey200);
    margin-right: 10px;
    margin-left: 10px;
  }
  
  .responseFilter-andDivider::after {
    content: ''; 
    flex-grow: 1; 
    border-top: 1px solid var(--grey200);
    margin-left: 10px; 
    margin-right: 65px;
  }