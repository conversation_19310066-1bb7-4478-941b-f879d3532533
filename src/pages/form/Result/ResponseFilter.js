import React, { useState } from "react";
import { Select } from "antd";
import { MdDeleteOutline } from "react-icons/md";
import "./ResponseFilter.css";
import { getIconByType } from "./HeaderUtil";

const ResponseFilter = ({
  filterOptions,
  setFilterOptions,
  filterHeaders,
  setFilterHeaders,
  handleAddFilter,
  handleDeleteFilter,
  applyFilters,
}) => {
  const [selectedHeader, setSelectedHeader] = useState();

  const handleFilterOption = (key, headerId) => {
    let selectedOption = filterOptions.find((option) => option.key === key);
    const selectedHeader = filterHeaders.find((h) => h.id === headerId);

    setSelectedHeader(selectedHeader);
    const updatedFilterHeaders = filterHeaders.map((header) => {
      if (header.isOk) {
        return header;
      }

      if (header.id === headerId) {
        return { ...header, used: true, isOk: false };
      }
      return { ...header, used: false };
    });

    setFilterHeaders(updatedFilterHeaders);
    const operators = getOperatorsByOption(selectedHeader.type);

    selectedOption = {
      key: key,
      dataIndex: selectedHeader.dataIndex,
      headerId: selectedHeader.id,
      headerType: selectedHeader.type,
      headerTitle: selectedHeader.title,
      operators: operators,
      operator: operators[0].value,
      value: "",
    };
    const updatedFilterOptions = filterOptions.map((option) =>
      option.key === key ? selectedOption : option
    );

    setFilterOptions(updatedFilterOptions);
  };

  const handleFilterOperator = (e, selectedOption) => {
    const val = e;

    selectedOption.operator = val;
    const updatedFilterOptions = filterOptions.map((option) =>
      option.key === selectedOption.key ? selectedOption : option
    );

    setFilterOptions(updatedFilterOptions);
  };

  const handleFilterValue = (e, selectedOption) => {
    e.preventDefault();
    const val = e.target.value;
    selectedOption.value = val;
    const updatedFilterOptions = filterOptions.map((option) =>
      option.key === selectedOption.key ? selectedOption : option
    );
    setFilterOptions(updatedFilterOptions);
  };

  const getOperatorsByOption = (type) => {
    if (!type) return [];

    if (
      type === "NumberField" ||
      type === "StarRatingField" ||
      type === "ScaleRatingField" ||
      type === "SmileRatingField"
    ) {
      return [
        { value: "equals", title: "is equal to" },
        { value: "less", title: "is less than" },
        { value: "more", title: "is more than" },
      ];
    } else if (type === "SelectMenuField" || type === "SingleSelectField") {
      return [{ value: "equals", title: "is equal to" }];
    } else if (type === "MultiSelectMenuField" || type === "CheckboxField") {
      return [{ value: "includes", title: "includes" }];
    } else {
      return [{ value: "equals", title: "is equal to" }];
    }
  };

  const isActiveAddFilterButton = () => {
    const hasEmptyField = filterOptions.some(
      (option) => !option.headerId || !option.operator || option.value === ""
    );

    if (hasEmptyField) {
      return false;
    }
    return true;
  };

  return (
    <div className="responseFilter-container">
      <div className="responseFilter-title">
        Show me all responses where ...
      </div>

      <div className="responseFilter-item-container">
        {filterOptions?.map((option, index) => (
          <div key={index}>
            <div className="responseFilter-item">
              <div style={{ width: "85%" }}>
                <div className="responseFilter-filter-options-container">
                  <Select
                    key={option.id}
                    className="responseFilter-filter-options"
                    placeholder="Select question"
                    value={option.headerTitle}
                    onChange={(val) => handleFilterOption(option.key, val)}
                  >
                    {filterHeaders.map((header, index) => (
                      <Select.Option
                        key={index}
                        value={header.id}
                        disabled={header.used}
                      >
                        <div className="responseFilter-filter-option">
                          <div className="responseFilter-filter-option-icon">
                            {getIconByType(header.type)}{" "}
                          </div>
                          <div className="responseFilter-filter-option-title">
                            {header.title}
                          </div>
                        </div>
                      </Select.Option>
                    ))}
                  </Select>
                </div>
                {option.headerId && (
                  <div className="responseFilter-item-innerContainer">
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        width: "100%",
                      }}
                    >
                      <div className="responseFilter-filter-operator-container">
                        <Select
                          defaultValue={option.operator}
                          className="responseFilter-filter-operator"
                          onChange={(e) => handleFilterOperator(e, option)}
                        >
                          {option.operators?.map((operator) => (
                            <Select.Option
                              key={operator.value}
                              value={operator.value}
                            >
                              {operator.title}
                            </Select.Option>
                          ))}
                        </Select>
                      </div>

                      <input
                        value={option.value}
                        className="responseFilter-filter-value"
                        onChange={(e) => handleFilterValue(e, option)}
                      />
                    </div>
                  </div>
                )}
              </div>

              <div
                className="responseFilter-filter-delete"
                onClick={() => handleDeleteFilter(option)}
              >
                <MdDeleteOutline />
              </div>
            </div>

            {filterOptions[filterOptions.length - 1]?.key !== option.key && (
              <div className="responseFilter-andDivider">and</div>
            )}
          </div>
        ))}
      </div>

      <div className="responseFilter-filter-buttons">
        {isActiveAddFilterButton() ? (
          <div
            className="responseFilter-addFilter-button"
            onClick={() => handleAddFilter(selectedHeader)}
          >
            Add Filter
          </div>
        ) : (
          <div></div>
        )}

        <div style={{ display: "flex", alignItems: "center" }}>
          <div className="responseFilter-filter-button-cancel">Cancel</div>
          <div
            className="responseFilter-filter-button-apply"
            onClick={() => applyFilters(false)}
          >
            Apply
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResponseFilter;
