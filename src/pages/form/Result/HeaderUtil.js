import { FaRegStar } from "react-icons/fa6";
import { GoImage } from "react-icons/go";
import {
  IoCalendarNumberOutline,
  IoTimeOutline,
  IoVideocamOutline,
} from "react-icons/io5";
import { LuPhone, LuS<PERSON>, LuText } from "react-icons/lu";
import {
  MdAlternateEmail,
  MdOutlineCalendarViewDay,
  MdOutlineChecklist,
  MdOutlineCloudUpload,
  MdOutlineRadioButtonChecked,
  MdOutlineShortText,
} from "react-icons/md";
import { RiCheckboxMultipleLine, RiGoogleLine } from "react-icons/ri";
import { TbBrandGoogleAnalytics, TbNumber123 } from "react-icons/tb";

export const getIconByType = (type) => {
  if (type === "ShortInputField") {
    return <MdOutlineShortText />;
  } else if (type === "LongInputField") {
    return <LuText />;
  } else if (type === "SelectMenuField") {
    return <MdOutlineCalendarViewDay />;
  } else if (type === "MultiSelectMenuField") {
    return <MdOutlineChecklist />;
  } else if (type === "SingleSelectField") {
    return <MdOutlineRadioButtonChecked />;
  } else if (type === "CheckboxField") {
    return <RiCheckboxMultipleLine />;
  } else if (type === "EmailField") {
    return <MdAlternateEmail />;
  } else if (type === "NumberField") {
    return <TbNumber123 />;
  } else if (type === "PhoneNumberField") {
    return <LuPhone />;
  } else if (type === "TimePickerField") {
    return <IoTimeOutline />;
  } else if (type === "DatePickerField") {
    return <IoCalendarNumberOutline />;
  } else if (type === "FileUploadField") {
    return <MdOutlineCloudUpload />;
  } else if (type === "ImageUploadField") {
    return <GoImage />;
  } else if (type === "VideoUploadField") {
    return <IoVideocamOutline />;
  } else if (type === "StarRatingField") {
    return <FaRegStar />;
  } else if (type === "ScaleRatingField") {
    return <TbBrandGoogleAnalytics />;
  } else if (type === "SmileRatingField") {
    return <LuSmile />;
  } else if (type === "SmileRatingField") {
    return <LuSmile />;
  } else if (type === "GoogleAuthenticateField") {
    return <RiGoogleLine />;
  }
};

export const getIconSVCByType = (type) => {
  let icon;
  if (type === "ShortInputField") {
    icon = <MdOutlineShortText />;
  } else if (type === "LongInputField") {
    icon = <LuText />;
  } else if (type === "SelectMenuField") {
    icon = <MdOutlineCalendarViewDay />;
  } else if (type === "MultiSelectMenuField") {
    icon = <MdOutlineChecklist />;
  } else if (type === "SingleSelectField") {
    icon = <MdOutlineRadioButtonChecked />;
  } else if (type === "CheckboxField") {
    icon = <RiCheckboxMultipleLine />;
  } else if (type === "EmailField") {
    icon = <MdAlternateEmail />;
  } else if (type === "NumberField") {
    icon = <TbNumber123 />;
  } else if (type === "PhoneNumberField") {
    icon = <LuPhone />;
  } else if (type === "TimePickerField") {
    icon = <IoTimeOutline />;
  } else if (type === "DatePickerField") {
    icon = <IoCalendarNumberOutline />;
  } else if (type === "FileUploadField") {
    icon = <MdOutlineCloudUpload />;
  } else if (type === "ImageUploadField") {
    icon = <GoImage />;
  } else if (type === "VideoUploadField") {
    icon = <IoVideocamOutline />;
  } else if (type === "StarRatingField") {
    icon = <FaRegStar />;
  } else if (type === "ScaleRatingField") {
    icon = <TbBrandGoogleAnalytics />;
  } else if (type === "SmileRatingField") {
    icon = <LuSmile />;
  } else if (type === "SmileRatingField") {
    icon = <LuSmile />;
  } else if (type === "GoogleAuthenticateField") {
    icon = <RiGoogleLine />;
  }

  return new XMLSerializer().serializeToString(icon.props.children);
};

export const getIconBgColor = (type) => {
  if (type === "ShortInputField") {
    return "#FFD1E3";
  } else if (type === "LongInputField") {
    return "#FFD1E3";
  } else if (type === "SelectMenuField") {
    return "#FFD1E3";
  } else if (type === "MultiSelectMenuField") {
    return "#FFD1E3";
  } else if (type === "SingleSelectField") {
    return "#FFD1E3";
  } else if (type === "CheckboxField") {
    return "#FFD1E3";
  } else if (type === "EmailField") {
    return "#FFD1E3";
  } else if (type === "NumberField") {
    return "#FFD1E3";
  }
};
