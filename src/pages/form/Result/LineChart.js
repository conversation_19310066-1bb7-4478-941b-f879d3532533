import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { format } from "date-fns";
import { useEffect, useState } from "react";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const LineChart = ({ lineData }) => {
  const [processedData, setProcessedData] = useState({
    labels: [],
    counts: [],
  });

  const processData = (data) => {
    const groupedData = data.reduce((acc, item) => {
      const date = format(new Date(item?.createdDate), "MMM dd, yyyy");
      if (acc[date]) {
        acc[date] += 1;
      } else {
        acc[date] = 1;
      }
      return acc;
    }, {});

    const groupedEntries = Object.entries(groupedData);

    const sortedEntries = groupedEntries.sort(
      (a, b) => new Date(a[0]) - new Date(b[0])
    );

    const labels = sortedEntries.map((entry) => entry[0]);
    const counts = sortedEntries.map((entry) => entry[1]);

    return { labels, counts };
  };

  useEffect(() => {
    if (lineData && lineData.length > 0) {
      const { labels, counts } = processData(lineData);
      setProcessedData({ labels, counts });
    }
  }, [lineData]);

  const { labels, counts } = processedData;

  const data = {
    labels,
    datasets: [
      {
        label: "",
        data: counts,
        borderColor: "black",
        backgroundColor: "rgba(0, 0, 0, 1)",
        borderWidth: 1.5,
        tension: 0.1,
      },
    ],
  };

  const options = {
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: false,
      },
      legend: {
        display: false,
      },
      tooltip: {
        mode: "index",
        intersect: false,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        title: {
          display: false,
          text: "Date",
        },
        ticks: {
          autoSkip: true,
        },
      },
      y: {
        title: {
          display: false,
          text: "Record Count",
        },
      },
    },
  };

  return (
    <>
      {labels.length > 0 && counts.length > 0 && (
        <div
          style={{
            position: "relative",
            marginTop: "20px",
            height: "30vh",
            width: "calc(100vw - 70px)",
            background: "white",
            padding: "10px",
            borderRadius: "5px",
            border: "1px solid var(--grey200)",
          }}
        >
          <Line data={data} options={options} />
        </div>
      )}
    </>
  );
};

export default LineChart;
