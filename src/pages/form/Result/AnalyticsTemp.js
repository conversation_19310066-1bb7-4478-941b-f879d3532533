import "./Analytics.css";
import { TbDevices } from "react-icons/tb";
import { useEffect, useRef, useState } from "react";
import { Col, DatePicker, Popover, Row, Select } from "antd";

import ReactCountryFlag from "react-country-flag";
import { MdOutlineDesktopWindows } from "react-icons/md";
import { FiMinus, FiTablet } from "react-icons/fi";
import { HiMiniDevicePhoneMobile } from "react-icons/hi2";
import { FaMinus } from "react-icons/fa6";
import LineChart from "./LineChart";
import { endOfMonth, parseISO, subDays } from "date-fns";
import dayjs from "dayjs";

const { RangePicker } = DatePicker;

let selectedDevice = "allDevices";

const Analytics = ({ views, starts, responses }) => {
  const [selectedAnalyticOption, setSelectedAnalyticOption] = useState("views");
  const [selectedTimeOption, setSelectedTimeOption] = useState("allTime");
  const [sortedViews, setSortedViews] = useState(views);
  const [sortedStarts, setSortedStarts] = useState([]);
  const [sortedResponses, setSortedResponses] = useState([]);
  const [isExpandedCustom, setExpandedCustom] = useState(false);
  const [customStartedDate, setCustomStartedDate] = useState(null);
  const [customEndedDate, setCustomEndedDate] = useState(null);

  const expandedCustomRef = useRef();

  useEffect(() => {
    let tempSortedViews;
    let tempSortedStarts;
    let tempSortedResponses;

    if (views) {
      tempSortedViews = views.sort((a, b) => {
        const dateA = new Date(a.createdDate);
        const dateB = new Date(b.createdDate);
        return dateB - dateA;
      });
    }

    if (starts) {
      tempSortedStarts = starts.sort(function (a, b) {
        const dateA = new Date(a.createdDate);
        const dateB = new Date(b.createdDate);
        return dateB - dateA;
      });
      setSortedStarts(tempSortedStarts);
    }

    if (responses) {
      tempSortedResponses = responses.sort(function (a, b) {
        const dateA = new Date(a.createdDate);
        const dateB = new Date(b.createdDate);
        return dateB - dateA;
      });
      setSortedResponses(tempSortedResponses);
    }

    if (
      (tempSortedViews && tempSortedViews.length > 0) ||
      (tempSortedStarts && tempSortedStarts.length > 0) ||
      (tempSortedResponses && tempSortedResponses.length > 0)
    ) {
      const allList = [
        ...tempSortedViews,
        ...tempSortedStarts,
        tempSortedResponses,
      ];
      const startedDate = formatDateWithoutHours(
        allList[tempSortedViews.length - 1].createdDate
      );
      const formattedStartedDate = dayjs(startedDate, "YYYY-MM-DD").subtract(
        1,
        "day"
      );
      setCustomStartedDate(formattedStartedDate);

      const endedDate = formatDateWithoutHours(allList[0].createdDate);
      const formattedEndedDate = dayjs(endedDate, "YYYY-MM-DD").add(1, "day");
      setCustomEndedDate(formattedEndedDate);
    }

    setSortedViews(tempSortedViews);
    setSortedStarts(tempSortedStarts);
    setSortedResponses(tempSortedResponses);
  }, [views, setSortedViews, starts, setSortedStarts]);

  useEffect(() => {
    document.addEventListener("mousedown", handleOutsideClicks);

    return () => {
      document.removeEventListener("mousedown", handleOutsideClicks);
    };
  }, []);

  const handleOutsideClicks = (event) => {
    if (
      event.target.className !== "analytics-filter-time-item-selected" &&
      event.target.className !== "ant-picker-cell-inner" &&
      event.target.className !== "ant-picker-cell" &&
      event.target.className !== "ant-picker-cell" &&
      event.target.className !== "ant-picker ant-picker-range" &&
      event.target.className !== "ant-picker-outlined" &&
      event.target.className !== "ant-picker-cell-range-start" &&
      event.target.className !== "ant-picker-cell-range-end" &&
      event.target.className !== "ant-picker-cell-in-view" &&
      event.target.className !== "ant-picker-prev-icon" &&
      event.target.className !== "ant-picker-header-prev-btn" &&
      event.target.className !== "ant-picker-header-nect-btn" &&
      !event.target.className &&
      event.target.className !== undefined &&
      event.target.className !== "" &&
      event.target.className.baseVal !== "" &&
      expandedCustomRef.current &&
      !expandedCustomRef.current.contains(event.target)
    ) {
      setExpandedCustom(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);

    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();

    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
  };

  const formatDateWithoutHours = (dateString) => {
    const date = new Date(dateString);

    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();

    return `${year}-${month}-${day} `;
  };

  function timeAgo(createdDate) {
    const pastDate = new Date(createdDate);
    const now = new Date();
    const secondsAgo = Math.floor((now - pastDate) / 1000);

    const intervals = [
      { label: "year", seconds: 31536000 },
      { label: "month", seconds: 2592000 },
      { label: "week", seconds: 604800 },
      { label: "day", seconds: 86400 },
      { label: "hour", seconds: 3600 },
      { label: "minute", seconds: 60 },
      { label: "second", seconds: 1 },
    ];

    for (const interval of intervals) {
      const count = Math.floor(secondsAgo / interval.seconds);
      if (count >= 1) {
        return `${count} ${interval.label}${count > 1 ? "s" : ""} ago`;
      }
    }

    return "just now";
  }

  const getDeviceIcon = (device) => {
    if (device === "desktop") {
      return (
        <Popover
          placement="bottom"
          title="Desktop"
          overlayInnerStyle={{
            height: "45px",
            textAlign: "center",
            padding: "10px 0 0 0",
          }}
        >
          <MdOutlineDesktopWindows style={{ fontSize: "20px" }} />
        </Popover>
      );
    } else if (device === "tablet") {
      return (
        <Popover
          placement="bottom"
          title="Tablet"
          overlayInnerStyle={{
            height: "45px",
            textAlign: "center",
            padding: "10px 0 0 0",
          }}
        >
          <FiTablet style={{ fontSize: "20px" }} />
        </Popover>
      );
    } else {
      return (
        <Popover
          placement="bottom"
          title="Mobile"
          overlayInnerStyle={{
            height: "45px",
            textAlign: "center",
            padding: "10px 0 0 0",
          }}
        >
          <HiMiniDevicePhoneMobile style={{ fontSize: "20px" }} />
        </Popover>
      );
    }
  };

  const filterDataForTime = (type) => {
    setExpandedCustom(false);
    let filteredViews;
    let filteredStarts;
    let filteredResponses;
    let viewsToFilter = views;
    let startsToFilter = sortedStarts;
    let responsesToFilter = sortedResponses;

    switch (type) {
      case "week":
        const lastWeekDate = getLastWeekDate();

        filteredViews = viewsToFilter.filter((view) => {
          const createdDate = parseISO(view.createdDate);
          if (selectedDevice !== "allDevices") {
            return (
              createdDate >= lastWeekDate && view.device === selectedDevice
            );
          }
          return createdDate >= lastWeekDate;
        });

        filteredStarts = startsToFilter?.filter((start) => {
          const createdDate = parseISO(start.createdDate);
          if (selectedDevice !== "allDevices") {
            return (
              createdDate >= lastWeekDate && start.device === selectedDevice
            );
          }
          return createdDate >= lastWeekDate;
        });

        filteredResponses = responsesToFilter?.filter((response) => {
          const createdDate = parseISO(response.createdDate);
          if (selectedDevice !== "allDevices") {
            return (
              createdDate >= lastWeekDate && response.device === selectedDevice
            );
          }
          return createdDate >= lastWeekDate;
        });
        break;

      case "month":
        viewsToFilter = views;
        const startOfMonthDate = getStartOfMonth();
        filteredViews = viewsToFilter.filter((view) => {
          const createdDate = parseISO(view.createdDate);

          if (selectedDevice !== "allDevices") {
            return (
              createdDate >= startOfMonthDate && view.device === selectedDevice
            );
          } else {
            return createdDate >= startOfMonthDate;
          }
        });

        startsToFilter = starts;
        filteredStarts = startsToFilter.filter((start) => {
          const createdDate = parseISO(start.createdDate);

          if (selectedDevice !== "allDevices") {
            return (
              createdDate >= startOfMonthDate && start.device === selectedDevice
            );
          } else {
            return createdDate >= startOfMonthDate;
          }
        });

        responsesToFilter = responses;
        filteredResponses = startsToFilter.filter((response) => {
          const createdDate = parseISO(response.createdDate);

          if (selectedDevice !== "allDevices") {
            return (
              createdDate >= startOfMonthDate &&
              response.device === selectedDevice
            );
          } else {
            return createdDate >= startOfMonthDate;
          }
        });
        break;

      case "custom":
        viewsToFilter = views;
        filteredViews = viewsToFilter.filter((view) => {
          const createdDate = parseISO(view.createdDate);
          if (selectedDevice !== "allDevices") {
            return (
              customStartedDate.$d <= createdDate &&
              createdDate <= customEndedDate.$d &&
              view.device === selectedDevice
            );
          } else {
            return (
              customStartedDate.$d <= createdDate &&
              createdDate <= customEndedDate.$d
            );
          }
        });

        startsToFilter = starts;
        filteredStarts = startsToFilter.filter((start) => {
          const createdDate = parseISO(start.createdDate);

          if (selectedDevice !== "allDevices") {
            return (
              customStartedDate.$d <= createdDate &&
              createdDate <= customEndedDate.$d &&
              start.device === selectedDevice
            );
          } else {
            return (
              customStartedDate.$d <= createdDate &&
              createdDate <= customEndedDate.$d
            );
          }
        });

        responsesToFilter = responses;
        filteredResponses = responsesToFilter.filter((response) => {
          const createdDate = parseISO(response.createdDate);

          if (selectedDevice !== "allDevices") {
            return (
              customStartedDate.$d <= createdDate &&
              createdDate <= customEndedDate.$d &&
              response.device === selectedDevice
            );
          } else {
            return (
              customStartedDate.$d <= createdDate &&
              createdDate <= customEndedDate.$d
            );
          }
        });
        break;

      case "allTime":
        if (selectedDevice !== "allDevices") {
          filteredViews = views.filter(
            (view) => view.device === selectedDevice
          );
        } else {
          filteredViews = views;
        }

        if (selectedDevice !== "allDevices") {
          filteredStarts = starts.filter(
            (start) => start.device === selectedDevice
          );
        } else {
          filteredStarts = starts;
        }

        if (selectedDevice !== "allDevices") {
          filteredResponses = responses.filter(
            (response) => response.device === selectedDevice
          );
        } else {
          filteredResponses = responses;
        }

        break;

      default:
        filteredViews = viewsToFilter;
        filteredStarts = viewsToFilter;
        break;
    }
    setSortedViews(filteredViews);
    setSortedStarts(filteredStarts);
  };

  const getLastWeekDate = () => subDays(new Date(), 7);
  const getStartOfMonth = () => subDays(new Date(), 30);
  const getEndOfMonth = () => endOfMonth(new Date());

  const onChangeDevice = (e) => {
    selectedDevice = e;
    filterDataForTime(selectedTimeOption);
  };

  const toggleCustomFilter = () => {
    filterDataForTime("custom");
    setExpandedCustom(!isExpandedCustom);
  };

  const handleCustomRangeChange = (dates) => {
    const viewsToFilter = views;
    const startsToFilter = starts;
    if (dates && dates.length === 2) {
      setCustomStartedDate(dates[0]);
      setCustomEndedDate(dates[1]);

      const filteredViews = viewsToFilter.filter((view) => {
        const createdDate = parseISO(view.createdDate);
        if (selectedDevice !== "allDevices") {
          return (
            dates[0] <= createdDate &&
            createdDate <= dates[1] &&
            view.device === selectedDevice
          );
        } else {
          return dates[0].$d <= createdDate && createdDate <= dates[1].$d;
        }
      });
      setSortedViews(filteredViews);

      const filteredStarts = startsToFilter.filter((start) => {
        const createdDate = parseISO(start.createdDate);
        if (selectedDevice !== "allDevices") {
          return (
            dates[0] <= createdDate &&
            createdDate <= dates[1] &&
            start.device === selectedDevice
          );
        } else {
          return dates[0].$d <= createdDate && createdDate <= dates[1].$d;
        }
      });
      setSortedViews(filteredViews);
      setSortedStarts(filteredStarts);

      //filterDataForTime("custom", dates[0], dates[1]);
    }
  };

  const calculateTimeToComplete = () => {
    let totalDifference = 0;
    let count = responses?.length;
    if (responses && responses.length > 0) {
      responses.forEach((response) => {
        const startedDate = new Date(response.startedDate);
        const endedDate = new Date(response.endedDate);

        const differenceInMilliseconds = endedDate - startedDate;
        const differenceInSeconds = differenceInMilliseconds / 1000;

        totalDifference += differenceInSeconds;
      });
      const averageDifferenceInSeconds = totalDifference / count;

      const hours = Math.floor(averageDifferenceInSeconds / 3600);
      const minutes = Math.floor((averageDifferenceInSeconds % 3600) / 60);
      const seconds = Math.round(averageDifferenceInSeconds % 60);

      return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
        2,
        "0"
      )}:${String(seconds).padStart(2, "0")}`;
    }
    return "00:00:00";
  };

  return (
    <>
      {(selectedAnalyticOption === "views" && views && views.length === 0) ||
      (selectedAnalyticOption === "starts" && starts && starts.length === 0) ? (
        <div />
      ) : (
        <div className="analytics-container">
          <div className="analytics-filter-container">
            <div className="analytics-filter-timeContainer">
              <div
                onClick={() => {
                  setSelectedTimeOption("week");
                  filterDataForTime("week");
                }}
                className={
                  selectedTimeOption === "week"
                    ? "analytics-filter-time-item-selected"
                    : "analytics-filter-time-item"
                }
              >
                This Week
              </div>
              <div
                onClick={() => {
                  setSelectedTimeOption("month");
                  filterDataForTime("month");
                }}
                className={
                  selectedTimeOption === "month"
                    ? "analytics-filter-time-item-selected"
                    : "analytics-filter-time-item"
                }
              >
                This Month
              </div>
              <div
                onClick={() => {
                  setSelectedTimeOption("allTime");
                  filterDataForTime("allTime");
                }}
                className={
                  selectedTimeOption === "allTime"
                    ? "analytics-filter-time-item-selected"
                    : "analytics-filter-time-item"
                }
              >
                All time
              </div>
              <div style={{ position: "relative" }}>
                <div
                  onClick={() => {
                    setSelectedTimeOption("custom");
                    toggleCustomFilter();
                  }}
                  className={
                    selectedTimeOption === "custom"
                      ? "analytics-filter-time-item-selected"
                      : "analytics-filter-time-item"
                  }
                  style={{ position: "relative", cursor: "pointer" }}
                >
                  Custom
                </div>
                <div
                  ref={expandedCustomRef} // Referans bağlandı
                  className={
                    isExpandedCustom
                      ? "analytics-filter-time-item-customExpanded"
                      : "analytics-filter-time-item-customClosed"
                  }
                >
                  <div style={{ display: "flex" }}>
                    <div
                      className="analytics-filter-time-item-customExpanded-item"
                      style={{ marginRight: "30px" }}
                    >
                      <RangePicker
                        value={[customStartedDate, customEndedDate]}
                        format="YYYY-MM-DD"
                        onChange={handleCustomRangeChange}
                      />
                    </div>
                    <div
                      className="analytics-filter-time-item-customExpanded-button"
                      style={{
                        background: "var(--grey400)",
                        color: "var(--black)",
                        marginRight: "20px",
                      }}
                      onClick={() => {
                        setExpandedCustom(false);
                      }}
                    >
                      Cancel
                    </div>
                    {/*
                    <div
                      className="analytics-filter-time-item-customExpanded-button"
                      onClick={() => {
                        setExpandedCustom(false);
                      }}
                    >
                      Apply
                                          </div>
                    */}
                  </div>
                </div>
              </div>
            </div>
            <div className="analytics-filter-selectDevice-container">
              <div className="analytics-filter-button-icon">
                <TbDevices />
              </div>
              <Select
                defaultValue="allDevices"
                onChange={(e) => {
                  onChangeDevice(e);
                }}
                className="analytics-filter-selectDevice"
              >
                <Select.Option value="allDevices" key="allDevices">
                  All Devices
                </Select.Option>
                <Select.Option value="desktop" key="desktop">
                  Desktop
                </Select.Option>
                <Select.Option value="tablet" key="tablet">
                  Tablet
                </Select.Option>
                <Select.Option value="mobile" key="mobile">
                  Mobile
                </Select.Option>
              </Select>
            </div>
          </div>
          <div style={{ height: "300px", width: "1200px", marginTop: "10px" }}>
            <LineChart
              lineData={
                selectedAnalyticOption === "views" ? sortedViews : sortedStarts
              }
            />
          </div>
          <div className="analytics-options">
            <div
              onClick={() => setSelectedAnalyticOption("views")}
              className={
                selectedAnalyticOption === "views"
                  ? "analytics-options-item-selected"
                  : "analytics-options-item"
              }
            >
              <div className="analytics-options-item-title">Views</div>
              <div className="analytics-options-item-result">
                {sortedViews?.length || 0}
              </div>
            </div>

            <div
              onClick={() => setSelectedAnalyticOption("starts")}
              className={
                selectedAnalyticOption === "starts"
                  ? "analytics-options-item-selected"
                  : "analytics-options-item"
              }
            >
              <div className="analytics-options-item-title">Starts</div>
              <div className="analytics-options-item-result">
                {sortedStarts?.length || 0}
              </div>
            </div>
            <div
              onClick={() => setSelectedAnalyticOption("responses")}
              className={
                selectedAnalyticOption === "responses"
                  ? "analytics-options-item-selected"
                  : "analytics-options-item"
              }
            >
              <div className="analytics-options-item-title">Responses</div>
              <div className="analytics-options-item-result">
                {sortedResponses?.length || 0}
              </div>
            </div>
            <div
              className="analytics-options-item-explanation"
              /*style={{ backgroundColor: "#fcd5c1" }}*/
            >
              <div className="analytics-options-item-title">
                Completion rate
              </div>
              <div className="analytics-options-item-result">
                {responses &&
                  responses.length > 0 &&
                  views &&
                  views.length > 0 &&
                  Math.round((responses.length / views.length) * 100)}{" "}
                %
              </div>
            </div>
            <div
              className="analytics-options-item-explanation"
              /*style={{ backgroundColor: "#c8cafb" }}*/
            >
              <div className="analytics-options-item-title">
                Time to complete
              </div>
              <div className="analytics-options-item-result">
                {" "}
                {calculateTimeToComplete()}
              </div>
            </div>
          </div>

          <div className="analytics-content-container">
            <div className="analytics-content-headers">
              <Row style={{ width: "100%" }}>
                <Col span={3} className="analytics-content-header-item">
                  Visitors
                </Col>
                <Col span={3} className="analytics-content-header-item">
                  Date
                </Col>
                <Col span={3} className="analytics-content-header-item">
                  Country
                </Col>
                <Col span={3} className="analytics-content-header-item">
                  City
                </Col>

                <Col span={3} className="analytics-content-header-item">
                  Platform
                </Col>
                <Col span={3} className="analytics-content-header-item">
                  Browser
                </Col>
                <Col span={3} className="analytics-content-header-item">
                  Device
                </Col>
                {selectedAnalyticOption === "responses" && (
                  <Col span={3} className="analytics-content-header-item">
                    Response
                  </Col>
                )}
              </Row>
            </div>

            {selectedAnalyticOption === "views" &&
              sortedViews?.map((view, index) => (
                <div className="analytics-content-data" key={index}>
                  <Row style={{ width: "100%" }}>
                    <Col span={3} className="analytics-content-header-item">
                      {view.ip}
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      <Popover
                        placement="bottom"
                        title={formatDate(view.createdDate)}
                        overlayInnerStyle={{
                          height: "45px",
                          textAlign: "center",
                          padding: "10px 0 0 0",
                        }}
                      >
                        {timeAgo(view.createdDate)}
                      </Popover>
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      <ReactCountryFlag
                        countryCode={view.country}
                        svg
                        style={{
                          width: "1.2em",
                          height: "1.2em",
                        }}
                        aria-label={view.country}
                      />
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      {view.city}
                    </Col>

                    <Col span={3} className="analytics-content-header-item">
                      {view.platform}
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      {view.browser}
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      {getDeviceIcon(view.device)}
                    </Col>
                  </Row>
                </div>
              ))}

            {selectedAnalyticOption === "starts" &&
              sortedStarts?.map((start, index) => (
                <div className="analytics-content-data" key={index}>
                  <Row style={{ width: "100%" }}>
                    <Col span={3} className="analytics-content-header-item">
                      {start.ip}
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      <Popover
                        placement="bottom"
                        title={formatDate(start.createdDate)}
                        overlayInnerStyle={{
                          height: "45px",
                          textAlign: "center",
                          padding: "10px 0 0 0",
                        }}
                      >
                        {timeAgo(start.createdDate)}
                      </Popover>
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      <ReactCountryFlag
                        countryCode={start.country}
                        svg
                        style={{
                          width: "1.2em",
                          height: "1.2em",
                        }}
                        aria-label={start.country}
                      />
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      {start.city}
                    </Col>

                    <Col span={3} className="analytics-content-header-item">
                      {start.platform}
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      {start.browser}
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      {getDeviceIcon(start.device)}
                    </Col>
                  </Row>
                </div>
              ))}

            {selectedAnalyticOption === "responses" &&
              sortedResponses?.map((response, index) => (
                <div className="analytics-content-data" key={index}>
                  <Row style={{ width: "100%" }}>
                    <Col span={3} className="analytics-content-header-item">
                      {response.ip}
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      <Popover
                        placement="bottom"
                        title={formatDate(response.createdDate)}
                        overlayInnerStyle={{
                          height: "45px",
                          textAlign: "center",
                          padding: "10px 0 0 0",
                        }}
                      >
                        {timeAgo(response.createdDate)}
                      </Popover>
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      <ReactCountryFlag
                        countryCode={response.country}
                        svg
                        style={{
                          width: "1.2em",
                          height: "1.2em",
                        }}
                        aria-label={response.country}
                      />
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      {response.city}
                    </Col>

                    <Col span={3} className="analytics-content-header-item">
                      {response.platform}
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      {response.browser}
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      {getDeviceIcon(response.device)}
                    </Col>
                    <Col span={3} className="analytics-content-header-item">
                      <div className="analytics-content-data-col-viewResponse">
                        View Response
                      </div>
                    </Col>
                  </Row>
                </div>
              ))}
          </div>
        </div>
      )}
    </>
  );
};

export default Analytics;
