import { createRef } from "react";

export function generateFormId() {
  const array = new Uint8Array(10);
  window.crypto.getRandomValues(array);
  return (
    "F" +
    Array.from(array)
      .map((b) => b.toString(36).padStart(2, "0"))
      .join("")
      .toLocaleUpperCase()
  );
}

export function generatePageId() {
  const array = new Uint8Array(14);
  window.crypto.getRandomValues(array);
  return (
    "P" +
    Array.from(array)
      .map((b) => b.toString(36).padStart(2, "0"))
      .join("")
      .toLocaleUpperCase()
  );
}

export function generateFiedId() {
  const array = new Uint8Array(16);
  window.crypto.getRandomValues(array);
  return (
    "I" +
    Array.from(array)
      .map((b) => b.toString(36).padStart(2, "0"))
      .join("")
      .toLocaleUpperCase()
  );
}

export function generateOptionId() {
  const array = new Uint8Array(16);
  window.crypto.getRandomValues(array);
  return (
    "O" +
    Array.from(array)
      .map((b) => b.toString(36).padStart(2, "0"))
      .join("")
      .toLocaleUpperCase()
  );
}

export function generateImageId(imageType) {
  const type = imageType.substring(
    imageType.indexOf("/") + 1,
    imageType.length
  );
  const array = new Uint8Array(16);
  window.crypto.getRandomValues(array);
  return (
    Array.from(array)
      .map((b) => b.toString(36).padStart(2, "0"))
      .join("")
      .toLocaleUpperCase() +
    "." +
    type
  );
}

export function pagesWithoutRef(pages) {
  const pagesWithoutRef = [];
  pages?.forEach((p, index) => {
    p.ref = null;
    pagesWithoutRef.push(p);
  });
  return pagesWithoutRef;
}

export function pagesWithRef(pages) {
  const pagesWithRef = [];
  pages.forEach((p, index) => {
    p.ref = { id: index + 1, ref: createRef() };
    pagesWithRef.push(p);
  });
  return pagesWithRef;
}

export function deepClone(obj, cache = new Map()) {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  if (cache.has(obj)) {
    return cache.get(obj);
  }

  if (obj instanceof Date) {
    return new Date(obj);
  }

  if (obj instanceof RegExp) {
    return new RegExp(obj);
  }
  let clone = Array.isArray(obj) ? [] : {};
  cache.set(obj, clone);
  for (let key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      clone[key] = deepClone(obj[key], cache);
    }
  }
  return clone;
}

export function deepEqual(obj1, obj2) {
  if (obj1 === obj2) return true;
  if (obj1 == null || obj2 == null) return false;
  if (typeof obj1 !== "object" || typeof obj2 !== "object") return false;
  const { updatedDate: _, ...obj1WithoutUpdatedDate } = obj1;
  const { updatedDate: __, ...obj2WithoutUpdatedDate } = obj2;
  const keys1 = Object.keys(obj1WithoutUpdatedDate);
  const keys2 = Object.keys(obj2WithoutUpdatedDate);
  if (keys1.length !== keys2.length) return false;

  for (let key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1WithoutUpdatedDate[key], obj2WithoutUpdatedDate[key]))
      return false;
  }

  return true;
}

export function deepEqualItemsOnly(obj1, obj2) {
  const removeDataIndex = (item) => {
    const { dataIndex, ...rest } = item;
    return rest;
  };

  if (
    obj1 &&
    obj1.pages &&
    obj1.pages.length > 0 &&
    obj2 &&
    obj2.pages &&
    obj2.pages.length > 0
  ) {
    for (let i = 0; i < obj1.pages.length; i++) {
      const page1 = obj1.pages[i];
      const page2 = obj2.pages[i];

      if (
        page1 &&
        page2 &&
        page1.items &&
        page2.items &&
        page1.items.length !== page2.items.length
      ) {
        return false;
      }
      if (page1 && page2 && page1.items && page2.items) {
        for (let j = 0; j < page1.items.length; j++) {
          const page1Item = removeDataIndex(page1.items[j]);
          const page2Item = removeDataIndex(page2.items[j]);

          if (page1Item.order !== page2Item.order) {
            return false;
          }
          if (JSON.stringify(page1Item) !== JSON.stringify(page2Item)) {
            return false;
          }
        }
      }
    }
    return true;
  }
  return false;
}

export function hasUnpublishedPage(array) {
  if (array && array.length > 0) {
    for (let page of array) {
      if (!page.published) {
        return true;
      }
    }
  }
  return false;
}

export function equalPages(pages1, pages2) {
  console.log("pages1 : ", pages1);
  console.log("pages2 : ", pages2);
  if (pages1 && pages2 && pages1.length === pages2.length) {
    for (let i = 0; i < pages1.length; i++) {
      const page1 = pages1[i];
      const page2 = pages2[i];

      if (page1.id !== page2.id || page1.order !== page2.order) {
        return false;
      }
    }
    return true;
  }
  return false;
}

export function hasUnpublishedItem(array) {
  if (array && Array.isArray(array.pages) && array.pages.length > 0) {
    for (let page of array.pages) {
      if (Array.isArray(page.items) && page.items.length > 0) {
        for (let item of page.items) {
          if (item.published === false) {
            return true;
          }
        }
      }
    }
  }
  return false;
}
