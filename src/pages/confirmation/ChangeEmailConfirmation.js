import { useEffect, useState } from "react";
import "./ChangeEmailConfirmation.css";
import { updateEmail } from "../../services/http";
import logo from "../../assets/images/logo/logo-white-title-50.png";
import { IoMdWarning } from "react-icons/io";
import { FaCheckCircle } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";

const ChangeEmailConfirmation = () => {
  const [error, setError] = useState();

  const auth = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    async function update() {
      await updateUserEmail();
    }
    update();
  }, []);

  const updateUserEmail = async () => {
    try {
      const url = window.location.href;
      const token = extractTokenFromUrl(url);
      const response = await updateEmail(token);
      if (response.status === 200) {
        auth.logout();
      }
    } catch (err) {
      console.log("err : ", err);
      setError(err.response.data.errorCode);
    } finally {
    }
  };

  const extractTokenFromUrl = (url) => {
    try {
      const basePath = "changeEmail/";
      const indexOfBasePath = url.indexOf(basePath);

      if (indexOfBasePath === -1) {
        return "Url is not valid or Token not found.";
      }
      const token = url.substring(indexOfBasePath + basePath.length);

      return token || "Token npot found.";
    } catch (error) {
      console.error("Extracting token error:", error);
    }
  };

  return (
    <>
      <div className="navbar-container">
        <div className="navbar-logo-container" style={{ cursor: "default" }}>
          <img src={logo} alt="logo" />
        </div>
      </div>
      <div className="changeEmailConfirmation-container">
        {!error && (
          <div className="changeEmailConfirmation-inner-container">
            <div className="changeEmailConfirmation-icon-container">
              <FaCheckCircle />
            </div>
            <div className="changeEmailConfirmation-content">
              The email address associated with your account has been changed
            </div>
            <div className="changeEmailConfirmation-login">
              <div className="changeEmailConfirmation-login-text">
                Please log in with your new email address.
              </div>
              <div
                className="changeEmailConfirmation-login-signIn"
                onClick={() => navigate("/login")}
              >
                Sign In
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="changeEmailConfirmation-inner-container">
            <div className="changeEmailConfirmation-error-icon-container">
              <IoMdWarning />
            </div>
            <div className="changeEmailConfirmation-error-content">
              Not Valid Link
            </div>
            <div className="changeEmailConfirmation-login">
              <div className="changeEmailConfirmation-login-text">
                This link is not valid for this user!
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default ChangeEmailConfirmation;
