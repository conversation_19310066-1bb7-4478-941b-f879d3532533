import { useEffect, useState } from "react";
import "./ChangePasswordConfirmation.css";
import { updatePassword } from "../../services/http";
import logo from "../../assets/images/logo/logo-white-title-50.png";
import { IoMdWarning } from "react-icons/io";
import { FaCheckCircle } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";

const ChangePasswordConfirmation = () => {
  const [error, setError] = useState();

  const auth = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    async function update() {
      await updateUserPassword();
    }
    update();
  }, []);

  const updateUserPassword = async () => {
    try {
      const url = window.location.href;
      const token = extractTokenFromUrl(url);

      const response = await updatePassword(token);
      if (response.status === 200) {
        auth.logout();
      }
    } catch (err) {
      console.log("err : ", err);
      setError(err.response.data.errorCode);
    } finally {
    }
  };

  const extractTokenFromUrl = (url) => {
    try {
      const basePath = "changePassword/";
      const indexOfBasePath = url.indexOf(basePath);

      if (indexOfBasePath === -1) {
        return "Url is not valid or Token not found.";
      }
      const token = url.substring(indexOfBasePath + basePath.length);

      return token || "Token not found.";
    } catch (error) {
      console.error("Extracting token error:", error);
    }
  };

  return (
    <>
      <div className="navbar-container">
        <div className="navbar-logo-container" style={{ cursor: "default" }}>
          <img src={logo} alt="logo" />
        </div>
      </div>
      <div className="changePasswordConfirmation-container">
        {!error && (
          <div className="changePasswordConfirmation-inner-container">
            <div className="changePasswordConfirmation-icon-container">
              <FaCheckCircle />
            </div>
            <div className="changePasswordConfirmation-content">
              Your password associated with your account has been changed
            </div>
            <div className="changePasswordConfirmation-login">
              <div className="changePasswordConfirmation-login-text">
                Please log in with your new password.
              </div>
              <div
                className="changePasswordConfirmation-login-signIn"
                onClick={() => navigate("/login")}
              >
                Sign In
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="changePasswordConfirmation-inner-container">
            <div className="changePasswordConfirmation-error-icon-container">
              <IoMdWarning />
            </div>
            <div className="changePasswordConfirmation-error-content">
              Not Valid Link
            </div>
            <div className="changePasswordConfirmation-login">
              <div className="changePasswordConfirmation-login-text">
                This link is not valid for this user!
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default ChangePasswordConfirmation;
