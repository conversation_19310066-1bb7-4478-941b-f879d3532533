import "./Account.css";
import Navbar from "../../components/UI/Navbar/Navbar";
import { useEffect, useState } from "react";
import { useAuth } from "../../context/AuthContext";
import {
  getUserByEmail,
  sendChangeEmailConfirmationCode,
  sendChangePasswordConfirmationCode,
  sendConfirmConfirmationCode,
  updateEmail,
  updateName,
} from "../../services/http";
import AccountChangeNameModal from "../../components/UI/Modals/AccountChangeNameModal";
import { message } from "antd";
import AccountChangeEmailModal from "../../components/UI/Modals/AccountChangeEmailModal";
import googleIcon from "../../assets/images/icons/google.png";
import AccountChangePasswordModal from "../../components/UI/Modals/AccountChangePasswordModal";

const Account = () => {
  const [loading, setLoading] = useState(false);
  const [accountUser, setAccountUser] = useState();
  const [changeNameModal, setChangeNameModal] = useState(false);
  const [changeEmailModal, setChangeEmailModal] = useState(false);
  const [changeEmailStep, setChangeEmailStep] = useState(0);
  const [changeEmailError, setChangeEmailError] = useState();
  const [changeEmailTimerStopped, setChangeEmailTimerStopped] = useState(false);
  const [changeEmailTimerRestart, setChangeEmailTimerRestart] = useState(false);
  const [changePasswordModal, setChangePasswordModal] = useState(false);
  const [changePasswordStep, setChangePasswordStep] = useState(0);
  const [changePasswordError, setChangePasswordError] = useState();
  const [changePasswordTimerStopped, setChangePasswordTimerStopped] =
    useState(false);
  const [changePasswordTimerRestart, setChangePasswordTimerRestart] =
    useState(false);
  const [selectedLeftMenuItem, setSelectedLeftMenuItem] =
    useState("yourSettings");

  const { authUser, setAuthUser } = useAuth();

  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    async function getAccountUser() {
      await getUser();
    }
    getAccountUser();
  }, []);

  const getUser = async () => {
    try {
      setLoading(true);
      const response = await getUserByEmail(authUser.email);
      if (response.status === 200) {
        setAccountUser(response.data);
      }
    } catch (err) {
      console.log("Gettings user ");
    } finally {
      setLoading(false);
    }
  };

  const changeName = async (user) => {
    try {
      setLoading(true);
      const response = await updateName(user);
      if (response.status === 200) {
        const firstName = response.data.firstName;
        const lastName = response.data.lastName;
        const tempAccountUser = accountUser;
        tempAccountUser.firstName = firstName;
        tempAccountUser.lastName = lastName;
        setAccountUser(tempAccountUser);
        setAuthUser(response.data);
        setTimeout(() => {
          messageApi.open({
            type: "success",
            content: "Name is updated",
          });
        }, 1100);
      }
    } catch (err) {
      console.log("Updating name error : ", err);
    } finally {
      setTimeout(() => {
        setLoading(false);
        setChangeNameModal(false);
      }, 1000);
    }
  };

  const sendCodeForChangeEmail = async (email) => {
    try {
      setLoading(true);
      const response = await sendChangeEmailConfirmationCode(email);
      if (response.status === 200) {
        setChangeEmailStep(1);
      }
    } catch (err) {
      console.log("Sending change email confirmation code error : ", err);
      if (err.response.data.errorCode === 409) {
        setChangeEmailError("This email is already registered in the system.");
      }
    } finally {
      setLoading(false);
    }
  };

  const sendCodeForChangePassword = async (currentPassword, newPassword) => {
    try {
      setLoading(true);
      const request = {
        currentPassword: currentPassword,
        newPassword: newPassword,
      };
      const response = await sendChangePasswordConfirmationCode(request);
      if (response.status === 200) {
        setChangePasswordStep(1);
      }
    } catch (err) {
      console.log("Sending change password confirmation code error : ", err);
      if (err.response.data.errorCode === 406) {
        setChangePasswordError("Current password is not correct");
      }
    } finally {
      setLoading(false);
    }
  };

  const sendConfirmMail = async (code, newEmail, type) => {
    try {
      setLoading(true);
      const response = await sendConfirmConfirmationCode(code, newEmail);
      if (response.status === 200) {
        if (type === "changeEmail") {
          setChangeEmailStep(0);
          setChangeEmailModal(false);
        } else {
          setChangePasswordStep(0);
          setChangePasswordModal(false);
        }

        setTimeout(() => {
          messageApi.open({
            type: "success",
            content: "Sent email",
          });
        }, 1100);
      }
    } catch (err) {
      console.log("Sending confirm confirmation email error : ", err);
      if (err.response.data.errorCode === 406) {
        if (type === "changeEmail") {
          setChangeEmailError("Invalid code, please try again");
        } else {
          setChangePasswordError("Invalid code, please try again");
        }
      }
      if (err.response.data.errorCode === 410) {
        if (type === "changeEmail") {
          setChangeEmailError("Confirmation code is expired");
        } else {
          setChangePasswordError("Confirmation code is expired");
        }

        setChangeEmailTimerStopped(true);
        setChangePasswordTimerStopped(true);
        setChangeEmailTimerRestart(false);
        setChangePasswordTimerRestart(false);
      }
    } finally {
      setLoading(false);
    }
  };

  const changeEmail = async (newEmail, code) => {
    try {
      setLoading(true);
      const response = await updateEmail(newEmail, code);
      if (response.status === 200) {
        const tempAccountUser = accountUser;
        tempAccountUser.email = newEmail;
        setAccountUser(tempAccountUser);
        setAuthUser(response.data);

        setTimeout(() => {
          messageApi.open({
            type: "success",
            content: "Email is updated",
          });
        }, 1100);
      }
    } catch (err) {
      console.log("Changing email error : ", err);
      if (err.response.data.errorCode === 406) {
        setChangeEmailError("Invalid code, please try again");
      }
      if (err.response.data.errorCode === 410) {
        setChangeEmailError("Confirmation code is expired");
      }
    } finally {
      setLoading(false);
    }
  };

  /*
  if (loading) {
    return <SmallLoading />;
  }
    */

  return (
    <>
      {contextHolder}
      <Navbar />
      <div className="account-container">
        <div className="account-left-menu">
          <div className="account-left-menu-title">Account</div>
          <div
            className={
              selectedLeftMenuItem === "yourSettings"
                ? "account-left-menu-item-selected"
                : "account-left-menu-item"
            }
          >
            Your settings
          </div>
        </div>
        {selectedLeftMenuItem === "yourSettings" && (
          <div className="account-content-container">
            <div className="account-content-innerContainer">
              <div className="account-content-header">Your Settings</div>
              <div className="account-content-header-description">
                Edit account login and user information
              </div>
              <div className="account-content-form">
                <div
                  className="account-content-item"
                  style={{ justifyContent: "space-between" }}
                >
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <div className="account-content-item-avatar">
                      {accountUser?.firstName?.charAt(0).toUpperCase()}
                      {accountUser?.lastName?.charAt(0).toUpperCase()}
                    </div>
                    <div className="account-content-item-user-container">
                      <div className="account-content-item-user-name">
                        {accountUser?.firstName} {accountUser?.lastName}
                      </div>
                      <div className="account-content-item-user-createdDate">
                        Joined Formiqo on {accountUser?.createdDate}
                      </div>
                    </div>
                  </div>
                  <div className="account-content-item-button-container">
                    <div
                      className="account-content-item-button"
                      onClick={() => setChangeNameModal(true)}
                    >
                      Change name
                    </div>
                  </div>
                </div>

                <div
                  className="account-content-item"
                  style={{ justifyContent: "space-between" }}
                >
                  <div style={{ width: "100%" }}>
                    <div
                      className="account-content-item-header"
                      style={{ display: "flex", alignItems: "center" }}
                    >
                      {" "}
                      LOGIN DETAILS
                    </div>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        marginBottom: "30px",
                      }}
                    >
                      <div>
                        <div className="account-content-item-label">
                          Your email address
                        </div>
                        <div
                          className="account-content-item-value"
                          style={{ display: "flex", alignItems: "center" }}
                        >
                          {accountUser?.email}
                          {accountUser?.social === "google" && (
                            <img
                              src={googleIcon}
                              style={{
                                width: "16px",
                                height: "16px",
                                marginLeft: "10px",
                              }}
                              alt="googleIcon"
                            />
                          )}
                        </div>
                        {accountUser?.social === "google" && (
                          <div className="account-content-item-warning">
                            Your account is connected to a Google account.
                            <br />
                            Changing the email address here will disconnect{" "}
                            <br />
                            your account from the Google account.
                          </div>
                        )}
                      </div>
                      <div
                        className="account-content-item-button-container"
                        style={{
                          justifyContent: "flex-end",
                        }}
                      >
                        <div
                          className="account-content-item-button"
                          onClick={() => setChangeEmailModal(true)}
                        >
                          Change email address
                        </div>
                      </div>
                    </div>

                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <div>
                        <div className="account-content-item-label">
                          Your password
                        </div>
                        <div className="account-content-item-value">
                          ******************
                        </div>
                      </div>
                      <div
                        className="account-content-item-button-container"
                        style={{
                          justifyContent: "flex-end",
                        }}
                      >
                        <div
                          className="account-content-item-button"
                          onClick={() => setChangePasswordModal(true)}
                        >
                          Change password
                        </div>
                      </div>
                    </div>
                    <div className="account-content-item-description">
                      <div className="account-content-item-description-header">
                        Email address and password are the information used to
                        log in to the system.
                      </div>
                      <div className="account-content-item-description-header-desc">
                        When you change it, your session information will also
                        change.
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  className="account-content-item"
                  style={{ justifyContent: "space-between", borderWidth: "0" }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      marginRight: "20px",
                    }}
                  >
                    <div className="account-content-item-description-header-desc">
                      All your forms and the data they collected get removed
                      from our system
                    </div>
                  </div>
                  <div className="account-content-item-button-container">
                    <div className="account-content-item-delete-button">
                      Delete account
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <AccountChangeNameModal
        open={changeNameModal}
        handleClose={() => setChangeNameModal(false)}
        title="Chance your name"
        handleOk={changeName}
        loading={loading}
        accountUser={accountUser}
        type="changeName"
      />

      <AccountChangeEmailModal
        open={changeEmailModal}
        handleClose={() => {
          setChangeEmailStep(0);
          setChangeEmailModal(false);
        }}
        title="Chance your email address"
        sendCode={sendCodeForChangeEmail}
        step={changeEmailStep}
        sendConfirmMail={sendConfirmMail}
        loading={loading}
        accountUser={accountUser}
        serviceError={changeEmailError}
        setServiceError={setChangeEmailError}
        type="changeName"
        timerStopped={changeEmailTimerStopped}
        setTimerStopped={setChangeEmailTimerStopped}
        timerRestart={changeEmailTimerRestart}
        setTimerRestart={setChangeEmailTimerRestart}
      />

      <AccountChangePasswordModal
        open={changePasswordModal}
        handleClose={() => {
          setChangePasswordStep(0);
          setChangePasswordModal(false);
        }}
        title="Chance your password"
        sendCode={sendCodeForChangePassword}
        step={changePasswordStep}
        sendConfirmMail={sendConfirmMail}
        loading={loading}
        accountUser={accountUser}
        serviceError={changePasswordError}
        setServiceError={setChangePasswordError}
        type="changeName"
        timerStopped={changePasswordTimerStopped}
        setTimerStopped={setChangePasswordTimerStopped}
        timerRestart={changePasswordTimerRestart}
        setTimerRestart={setChangePasswordTimerRestart}
      />
    </>
  );
};

export default Account;
