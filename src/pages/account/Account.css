.account-container {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}

.account-left-menu {
  position: absolute;
  height: 100%;
  left: 0;
  bottom: 0;
  background-color: var(--white);
  width: 280px;
  border-right: 1px solid var(--grey300);
}

.account-left-menu-title {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 20px;
  margin-bottom: 20px;
  margin-top: 100px;
  padding: 0 20px;
}

.account-left-menu-item {
  padding: 8px 20px;
  font-family: "Exo 2", sans-serif;
  font-weight: 400;
  font-size: 15px;
  color: var(--black);
  cursor: pointer;
}

.account-left-menu-item:hover {
  background-color: #f7f7f7;
}

.account-left-menu-item-selected {
  background-color: var(--grey300);
  padding: 8px 20px;
  font-family: "Exo 2", sans-serif;
  font-weight: 400;
  font-size: 15px;
  color: var(--black);
  cursor: pointer;
}

.account-content-container {
  display: flex;
  justify-content: center;
  margin-top: 100px;
  width: calc(100% - 280px);
  height: calc(100% - 100px);
  margin-left: 280px;
}

.account-content-innerContainer {
  width: 700px;
}

.account-content-header {
  font-family: "Exo 2", sans-serif;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.account-content-header-description {
  font-family: "Exo 2", sans-serif;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 20px;
  opacity: 0.8;
}

.account-content-form {
  width: 700px;
  background-color: var(--white);
  padding: 10px 30px;
  border: 1px solid var(--grey300);
  border-radius: 5px;
}

.account-content-item {
  display: flex;
  border-bottom: 1px solid var(--grey300);
  padding: 30px 0;
}

.account-content-item-avatar {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  background-color: var(--profile-bg);
  border: 1px solid var(--black);
  border-radius: 100px;
  color: var(--black);
  font-family: "Exo 2", sans-serif;
  font-weight: 600;
  margin-right: 20px;
}

.account-content-item-user-container {
}

.account-content-item-user-name {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  margin-bottom: 3px;
}

.account-content-item-user-createdDate {
  font-family: "Exo 2", sans-serif;
  font-size: 14px;
  font-weight: 400;
  opacity: 0.8;
}

.account-content-item-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.account-content-item-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 15px;
  background-color: var(--grey300);
  border: 1px solid var(--grey200);
  border-radius: 5px;
  cursor: pointer;
  font-family: "Exo 2", sans-serif;
  font-size: 15px;
}

.account-content-item-delete-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 15px;
  color: var(--white);
  background-color: var(--red100);
  border: 1px solid var(--red100);
  border-radius: 5px;
  cursor: pointer;
  font-family: "Exo 2", sans-serif;
  font-size: 15px;
}

.account-content-item-button:hover {
  opacity: 0.8;
  transition: 0.3s all;
}

.account-content-item-header {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  margin-bottom: 10px;
}

.account-content-item-label {
  font-family: "Exo 2", sans-serif;
  font-weight: 400;
  opacity: 0.8;
}

.account-content-item-value {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
}

.account-content-item-description {
  margin-top: 20px;
  padding: 20px;
  background-color: var(--green600);
  border: 1px solid var(--green400);
  border-radius: 5px;
  text-align: center;
}

.account-content-item-description-header {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: var(--black);
  margin-bottom: 5px;
}

.account-content-item-description-header-desc {
  font-family: "Exo 2", sans-serif;
  font-weight: 400;
  font-size: 14px;
  opacity: 0.9;
  color: var(--black);
}

.account-content-item-delete-description {
  margin-top: 20px;
  padding: 20px;
  background-color: var(--red300);
  border: 1px solid var(--red100);
  border-radius: 5px;
  text-align: center;
  width: 400px;
  color: var(--white);
}

.account-items {
  width: 750px;
  background-color: var(--white);
  padding: 30px;
  border: 1px solid var(--grey300);
  border-radius: 5px;
}

.account-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--grey300);
}

.account-item-label {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 17px;
  width: 320px;
  color: var(--black);
  width: 300px;
}

.account-content-item-warning {
  background-color: var(--green600);
  border: 1px solid var(--green400);
  border-radius: 3px;
  font-family: "Exo 2", sans-serif;
  font-size: 12px;
  padding: 5px 10px;
  margin-top: 10px;
}
