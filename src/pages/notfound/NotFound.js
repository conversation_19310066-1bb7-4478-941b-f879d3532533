import { useNavigate } from "react-router-dom";
import logo from "../../assets/images/logo/logo-white-small-50.png";
import "./NotFound.css";

const NotFound = () => {
  const navigate = useNavigate();

  const goToHomePage = () => {
    navigate("/");
  };

  return (
    <div className="notFound-container">
      <div className="notFound-logo-container">
        <img src={logo} alt="logo" />
      </div>
      <div className="notFound-title">Oops!</div>
      <div className="notFound-description">
        <div>Sorry, the page not found.</div>
        <div
          style={{
            borderBottom: "2px solid var(--black)",
            marginLeft: "8px",
            cursor: "pointer",
          }}
          onClick={goToHomePage}
        >
          Go to home page
        </div>
      </div>
    </div>
  );
};

export default NotFound;
