import Navbar from "../../components/UI/Navbar/Navbar";
import "./Members.css";
import { MdDeleteOutline, MdOutlineAdd } from "react-icons/md";
import { CiSearch } from "react-icons/ci";
import { BsSortDown } from "react-icons/bs";
import AddMemberModal from "../../components/UI/Modals/AddMemberModal";
import { useCallback, useEffect, useState } from "react";
import {
  deleteMemberById,
  getAllWorkspaces,
  getMembersByOrganization,
  saveMemberByUser,
} from "../../services/http";
import { useMainContext } from "../../context/MainContext";
import { Col, Pagination, Row } from "antd";
import { AiOutlineAppstoreAdd } from "react-icons/ai";
import DeleteModal from "../../components/UI/Modals/DeleteFormModal";
import MemberWorkspaceModal from "../../components/UI/Modals/MemberWorkspaceModal";

const Members = () => {
  const [loading, setLoading] = useState(false);
  const [selectedMember, setSelectedMember] = useState();
  const [openedAddMemberModal, setOpenedAddMemberModal] = useState(false);
  const [addMemberError, setAddMemberError] = useState("");
  const [openedDeleteMemberModal, setOpenedDeleteMemberModal] = useState(false);
  const [deleteMemberError, setDeleteMemberError] = useState("");
  const [openedWorkspaceModal, setOpenedWorkspaceModal] = useState(false);
  const [addWorkspaceError, setAddWorkspaceError] = useState();

  const [organizationMembers, setOrganizationMembers] = useState();
  const [tempOrganizationMembers, setTempOrganizationMembers] = useState();
  const [workspaces, setWorkspaces] = useState([]);

  const [searchTerm, setSearchTerm] = useState("");

  const mainContext = useMainContext();

  const getOrganizationMembers = useCallback(async () => {
    try {
      setLoading(true);
      const organizationId = mainContext.selectedOrganization.id;
      const response = await getMembersByOrganization(organizationId);
      if (response.status === 200) {
        setOrganizationMembers(response.data);
        setTempOrganizationMembers(response.data);
      }
    } catch (e) {
      console.log("Getting organization members error : ", e);
    } finally {
      setLoading(false);
    }
  }, [mainContext.selectedOrganization.id]);

  useEffect(() => {
    async function getMembers() {
      await getOrganizationMembers();
    }

    if (!organizationMembers) {
      getMembers();
    }
  }, [organizationMembers, getOrganizationMembers]);

  const getWorkspaceList = async () => {
    try {
      setLoading(true);
      const response = await getAllWorkspaces();
      if (response.status === 200) {
        setWorkspaces(response.data);
        sortMembers("createdDate");
      }
    } catch (err) {
      console.log("Getting workspaces error : ", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    async function getWorkspaces() {
      await getWorkspaceList();
    }
    if (workspaces.length === 0) {
      getWorkspaces();
    }
  }, [workspaces, getWorkspaceList]);

  useEffect(() => {
    if (searchTerm === "") {
      setOrganizationMembers(tempOrganizationMembers);
    } else {
      const filteredMembers = organizationMembers.filter(
        (member) =>
          member.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          member.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          member.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setOrganizationMembers(filteredMembers);
    }
  }, [searchTerm]);

  const saveMember = async (member) => {
    try {
      setLoading(true);
      member.organizationId = mainContext.selectedOrganization.id;
      const response = await saveMemberByUser(member);
      if (response.status === 200) {
        await getOrganizationMembers();
        setOpenedAddMemberModal(false);
        return response;
      }
    } catch (err) {
      console.log("Saving member error : ", err);
      if (err.response.status && err.response.status === 409) {
        setAddMemberError("The user already saved!");
      } else {
        setAddMemberError("");
        setOpenedAddMemberModal(false);
      }
    } finally {
      setLoading(false);
    }
  };

  const deleteMember = async () => {
    try {
      setLoading(true);
      const response = await deleteMemberById(selectedMember.id);
      if (response.status === 200) {
        const membersTemp = [...organizationMembers];
        const deletedMemberIndex = membersTemp.findIndex(
          (member) => member.id === selectedMember.id
        );

        membersTemp.splice(deletedMemberIndex, 1);
        setOrganizationMembers(membersTemp);
        setOpenedDeleteMemberModal(false);
      }
    } catch (err) {
      console.log("Deleting member error : ", err);
      if (err.response.status && err.response.status === 403) {
        setDeleteMemberError("You do not have the authority to delete Member");
      } else {
        setDeleteMemberError("");
        setOpenedDeleteMemberModal(false);
      }
    } finally {
      setLoading(false);
    }
  };

  const saveWorkspaces = async (workspaces) => {
    const memberTemp = selectedMember;
    memberTemp.workspaces = workspaces;
    const response = await saveMember(memberTemp);
    if (response.status === 200) {
      const tempOrganizationMembers = [...organizationMembers];
      const updatedMemberIndex = tempOrganizationMembers.findIndex(
        (member) => member.id === memberTemp.id
      );
      tempOrganizationMembers.splice(updatedMemberIndex, 1, memberTemp);
      setOrganizationMembers(tempOrganizationMembers);
    }
  };

  const searchMember = (e) => {
    setSearchTerm(e.target.value);
  };

  const sortMembers = (option) => {
    const tempMembers = Array.isArray(organizationMembers)
      ? [...organizationMembers]
      : [];

    tempMembers.sort((a, b) => {
      if (a.role === "ADMIN" && b.role !== "ADMIN") {
        return -1;
      }
      if (a.role !== "ADMIN" && b.role === "ADMIN") {
        return 1;
      }

      if (option === "createdDate") {
        const dateA = parseDate(a.createdDate, false);
        const dateB = parseDate(b.createdDate, false);
        return dateA.getTime() - dateB.getTime();
      } else if (option === "firstNameAZ") {
        return a.firstName.localeCompare(b.firstName);
      } else if (option === "firstNameZA") {
        return b.firstName.localeCompare(a.firstName);
      }
      return 0;
    });

    setOrganizationMembers(tempMembers);
  };

  const parseDate = (dateString, formattedDate) => {
    const months = {
      Oca: "Jan",
      Şub: "Feb",
      Mar: "Mar",
      Nis: "Apr",
      May: "May",
      Haz: "Jun",
      Tem: "Jul",
      Ağu: "Aug",
      Eyl: "Sep",
      Eki: "Oct",
      Kas: "Nov",
      Ara: "Dec",
    };

    const [day, month, year, time] = dateString.split(" ");
    const monthEng = months[month];

    if (formattedDate) {
      return `${year} ${monthEng} ${day} ${time}`;
    }
    return new Date(`${year} ${monthEng} ${day} ${time}`);
  };

  return (
    <>
      <Navbar />
      <div className="members-container">
        <div className="members-top-menu-container">
          <div className="members-top-menu-filters">
            <div className="members-top-menu-filter-search">
              <div className="members-top-menu-filter-search-icon">
                <CiSearch />
              </div>
              <input
                onChange={searchMember}
                placeholder="Searh my members"
                className="members-top-menu-filter-search-input"
              />
            </div>
            <div className="members-top-menu-filter-order">
              <div className="workspace-top-filter-item-icon">
                <BsSortDown />
              </div>
              <select
                className="workspace-top-filter-select"
                onChange={(e) => sortMembers(e.target.value)}
              >
                <option value="createdDate" key="createdDate">
                  Creation Date
                </option>
                <option value="firstNameAZ" key="firstNameAZ">
                  First Name [a-z]
                </option>
                <option value="firstNameZA" key="firstNameZA">
                  First Name [z-a]
                </option>
              </select>
            </div>
          </div>
          <div
            className="members-addMember-button"
            onClick={() => setOpenedAddMemberModal(true)}
          >
            <div className="members-addMember-button-icon">
              <MdOutlineAdd />
            </div>
            <div className="members-addMember-button-title">Add Member</div>
          </div>
        </div>

        <div className="members-table-container">
          {organizationMembers && organizationMembers.length > 0 && (
            <Row className="members-table-headers">
              <Col span={4}>First Name</Col>
              <Col span={6}>Last Name</Col>
              <Col span={6}>Email</Col>
              <Col span={4}>Created Date</Col>
              <Col span={4}></Col>
            </Row>
          )}
          {organizationMembers &&
            organizationMembers.length > 0 &&
            organizationMembers.map((member, index) => (
              <Row
                key={index}
                className={
                  member.role === "ADMIN"
                    ? "members-table-item-admin"
                    : "members-table-item"
                }
              >
                <Col span={4}>{member.firstName}</Col>
                <Col span={6}>{member.lastName}</Col>
                <Col span={6}>{member.email}</Col>
                <Col span={4}>{parseDate(member.createdDate, true)}</Col>
                <Col
                  span={4}
                  style={{ display: "flex", justifyContent: "center" }}
                >
                  {member.role !== "ADMIN" ? (
                    <div className="members-table-item-actions">
                      <div
                        className="members-table-item-action-item"
                        onClick={() => {
                          setOpenedDeleteMemberModal(true);
                          setSelectedMember(member);
                        }}
                      >
                        <MdDeleteOutline />
                      </div>
                      <div
                        className="members-table-item-action-item"
                        onClick={() => {
                          setSelectedMember(member);
                          setOpenedWorkspaceModal(true);
                        }}
                      >
                        <AiOutlineAppstoreAdd />
                      </div>
                    </div>
                  ) : (
                    <div className="members-table-item-status-admin">admin</div>
                  )}
                </Col>
              </Row>
            ))}
          {organizationMembers && organizationMembers.length > 0 && (
            <div className="members-table-pagination-container">
              <Pagination
                align="end"
                defaultCurrent={1}
                total={organizationMembers.length}
              />
            </div>
          )}
        </div>
      </div>
      <AddMemberModal
        open={openedAddMemberModal}
        handleClose={() => setOpenedAddMemberModal(false)}
        title="Add Member"
        buttonAction={saveMember}
        loading={loading}
        error={addMemberError}
        setError={setAddMemberError}
      />

      <DeleteModal
        open={openedDeleteMemberModal}
        handleOk={() => deleteMember()}
        handleClose={() => setOpenedDeleteMemberModal(false)}
        title="Delete this member"
        message="Are you sure you want to delete this member?"
        buttonTitle="Delete Member"
        loading={loading}
        error={deleteMemberError}
        setError={setDeleteMemberError}
      />

      <MemberWorkspaceModal
        open={openedWorkspaceModal}
        handleOk={saveWorkspaces}
        handleClose={() => setOpenedWorkspaceModal(false)}
        selectedMember={selectedMember}
        setSelectedMember={setSelectedMember}
        workspaces={workspaces}
        title="Member workspaces"
        message="Define which workspaces you will authorize the member in."
        buttonTitle="Saving Changes"
        error={addWorkspaceError}
        setError={setAddWorkspaceError}
      />
    </>
  );
};

export default Members;
