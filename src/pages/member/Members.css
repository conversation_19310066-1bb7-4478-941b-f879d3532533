.members-container {
  margin-top: 60px;
}

.members-top-menu-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 10px 35px;
  border-bottom: 1px solid var(--grey200);
}

.members-top-menu-filters {
  display: flex;
  align-items: center;
}

.members-top-menu-filter-search {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
}

.members-top-menu-filter-search-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: -35px;
  right: 220px;
  z-index: 1;
}

.members-top-menu-filter-search-input {
  position: relative;
  padding: 8px 0px 8px 30px;
  border: 1px solid var(--grey200);
  color: var(--text-dark);
  margin-left: 10px;
  border-radius: 5px;
  width: 280px;
  height: 35px;
  background-color: var(--white);
  position: relative;
  padding: 8px 0px 8px 30px;
  border: 1px solid var(--grey200);
  color: var(--text-dark);
  margin-left: 10px;
  border-radius: 5px;
  width: 280px;
  height: 35px;
  background-color: var(--white);
}

.members-top-menu-filter-order {
  display: flex;
  justify-content: center;
  align-items: center;
}

.members-addMember-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 15px;
  margin-right: 10px;
  border: 1px solid var(--green400);
  border-radius: 3px;
  font-family: "Exo 2", sans-serif;
  background-color: var(--green400);
  width: fit-content;
  cursor: pointer;
}

.members-addMember-button:hover {
  opacity: 0.8;
  transition: 0.3s all;
}

.members-addMember-button-icon {
  font-size: 18px;
  color: var(--white);
  margin-right: 5px;
}

.members-addMember-button-title {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 15px;
  color: var(--white);
}

.members-table-container {
  margin: 30px;
}

.members-table-headers {
  padding: 15px;
  font-family: "Exo 2", sans-serif;
}

.members-table-item {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  background-color: var(--white);
  border: 1px solid var(--grey200);
  border-radius: 10px;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
}

.members-table-item-admin {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 10px;
  background-color: var(--grey400);
  border: 1px solid var(--grey200);
  border-radius: 10px;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
}

.members-table-item-actions {
  display: flex;
  justify-content: center;
}

.members-table-item-action-item {
  display: flex;
  justify-content: center;
  align-items: center;
  width: fit-content;
  padding: 3px;
  font-size: 20px;
  background-color: var(--white);
  border: 1px solid var(--grey200);
  border-radius: 5px;
  margin-right: 10px;
  cursor: pointer;
}

.members-table-item-action-item:hover {
  background-color: var(--grey400);
  transition: 0.3s all;
}

.members-table-item-status-admin {
  padding: 1px 15px 2px 15px;
  background-color: var(--green600);
  width: fit-content;
  text-align: center;
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  color: var(--green400);
  border: 1px solid var(--green400);
  border-radius: 30px;
}

.members-table-item-workspace {
  display: flex;
  justify-content: center;
  align-items: center;
  width: fit-content;
  padding: 3px;
  font-size: 20px;
  background-color: var(--white);
  border: 1px solid var(--grey200);
  border-radius: 5px;
  cursor: pointer;
}

.members-table-item-workspace:hover {
  background-color: var(--grey400);
  transition: 0.3s all;
}

.members-table-pagination-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-top: 20px;
}
