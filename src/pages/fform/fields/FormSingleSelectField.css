.formSingleSelectField-container {
    margin-bottom: 30px;
}

.formSingleSelectField-label {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 10px;
}

.formSingleSelectField-description {
    font-size: 12px;
    opacity: .9;
    margin-top: -10px;
}

.formSingleSelectField-formItem {
    font-family: 'Inter', sans-serif !important;    
    width: 330px !important;
}

.formSingleSelectField-formItem-input-cover {
    padding: 0 8px;
}

.ant-form-item-label >label {
    font-family: "Inter", sans-serif;
}

.ant-input {
    border-radius: 3px;
    height: 40px !important;
}

.ant-input::before {
    background-color: var(--white) !important;
}

.ant-input::after {
    background-color: var(--white) !important;
}

.ant-input-outlined {
    height: 40px !important;
    border-width: 1px;
    border-style: solid;
    border-color: var(--grey300) !important;
    font-family: "Inter", sans-serif;
}

.ant-input-outlined:hover {
    /*border-color: var(--bg-dark) !important;*/
}

.ant-input-outlined:focus {
    /*border-color: var(--bg-dark) !important;    */
}

.ant-input-outlined:focus-within {
    box-shadow: 0 0 0 0px #acabab;
}

.ant-input-affix-wrapper {
    height: 45px !important;
    border-radius: 3px;
    border-width: 1px;
}

.ant-input-affix-wrapper >input.ant-input {
    height: 25px !important;
}

.ant-input-affix-wrapper:focus-within {
    border: 1px solid var(--grey300);
}


.ant-select-focused {
    border-width: 0 !important;
    border-color: red !important;
}

.ant-select-selector:hover {
    border-color: var(--grey100) !important;
    border-width: 0 !important;
    box-shadow: 0 0 0 0px rgba(0, 145, 255, 0) !important;
}

.ant-select:hover {
    border-width: 0 !important;
    border-color: var(--grey100) !important;
}

.ant-select-single {
    height: 25px !important;
}

.ant-select-selector:hover, .ant-select-selector:focus-within {
    border: 1px solid var(--grey200) !important;
    border-width: 0 !important;
    box-shadow: 0 0 0 0px rgba(0, 145, 255, 0) !important;
}

  .ant-select-item-option {
    box-shadow: 0 0 0 0px rgba(0, 145, 255, 0) !important;
    background-color: var(--white) !important;
    &:hover {
        background-color: var(--grey400) !important;
    }
}

.ant-select-customize-input:focus {
    border-width: 0;
    box-shadow: 0 0 0 0px rgba(0, 145, 255, 0) !important;
}

.ant-select-customize-input:hover {
    border-width: 0;
    box-shadow: 0 0 0 0px rgba(0, 145, 255, 0) !important;
}

.ant-select-selection-placeholder {
    color: var(--grey100) !important;
    font-weight: 300 !important;
    font-size: 13px !important;
    font-family: 'Inter', sans-serif;
}

::-ms-input-placeholder {
    color: var(--grey100) !important;
    font-weight: 400 !important;
    font-size: 13px !important;
    font-family: 'Inter', sans-serif;
}

::-ms-input-placeholder {
    color: var(--grey100) !important;
    font-weight: 400 !important;
    font-size: 13px !important;
    font-family: 'Inter', sans-serif;
}
