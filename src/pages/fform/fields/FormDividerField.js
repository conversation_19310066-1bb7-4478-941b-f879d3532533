import "./FormDividerField.css";

const FormDividerField = ({ field }) => {
  return (
    <>
      <div className="formDividerField-container">
        <div style={{ width: "100%" }}>
          <div
            style={{
              borderWidth: field.dividerHeight
                ? field.dividerHeight + "px"
                : "1px",
              borderStyle: field.dividerStyle ? field.dividerStyle : "Solid",
              marginBottom: field.spaceBelow ? field.spaceBelow + "px" : "5px",
              marginTop: field.spaceAbove ? field.spaceAbove + "px" : "5px",
              borderColor: field.lineColor ? field.lineColor : "var(--grey200)",
            }}
          />
        </div>
      </div>
    </>
  );
};

export default FormDividerField;
