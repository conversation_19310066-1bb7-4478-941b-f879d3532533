import { Form, Radio, Space } from "antd";
import "./FormSingleSelectField.css";
import { useState } from "react";
import styled, { css } from "styled-components";

const CustomRadio = styled(Radio)`
  ${(props) =>
    props.backgroundColor &&
    css`
      & .ant-radio-checked .ant-radio-inner {
        background-color: ${props.backgroundColor} !important;
        border-color: ${props.backgroundColor} !important;

        &&:hover {
          opacity: 0.8 !important;
        }
      }
    `}
`;

const FormSingleSelectField = ({
  field,
  theme,
  formValues,
  setFormValues,
  antForm,
}) => {
  const [itemValue, setItemValue] = useState(
    formValues
      ? Object.values(formValues).find(
          (formValue) => formValue.fieldId === field.id
        )?.value
      : null
  );

  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  const handleOnChange = (e) => {
    const value = e.target.value;
    setItemValue(value);
    setFormValues({
      ...formValues,
      [field.id]: {
        fieldId: field.id,
        type: field.type,
        title: field.title,
        value: value,
      },
    });
  };

  return (
    <>
      <div
        className="formSingleSelectField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formSingleSelectField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          value={antForm.getFieldsValue([field.id])}
          rules={[
            {
              required: field.required,
              message: field.title + " is required",
            },
          ]}
          wrapClassName="formSingleSelectField-formItem"
        >
          <div
            className="formSingleSelectField-formItem-input-cover"
            style={{
              /*borderColor: theme ? theme.questionColor : "",*/
              borderRadius: theme ? getInputBorderRadius() : "3px",
            }}
          >
            <Radio.Group
              backgroundColor={theme ? theme.questionColor : "#000"}
              style={{
                marginBottom: "5px",
                color: theme ? theme.questionColor : "",
              }}
              onChange={handleOnChange}
              value={formValues && itemValue}
            >
              <Space direction="vertical">
                {field.options?.map((option) => (
                  <CustomRadio
                    key={option.id}
                    value={option.label}
                    backgroundColor={theme ? theme.questionColor : "#000"}
                    style={{
                      marginBottom: "5px",
                      color: theme ? theme.questionColor : "",
                    }}
                  >
                    {option.label}
                  </CustomRadio>
                ))}
              </Space>
            </Radio.Group>
          </div>
        </Form.Item>
        {field.description && field.description.length > 0 && (
          <div className="formSingleSelectField-description">
            {field.description}
          </div>
        )}
      </div>
    </>
  );
};

export default FormSingleSelectField;
