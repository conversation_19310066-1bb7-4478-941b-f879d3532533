import { Form, Select } from "antd";
import "./FormSelectMenuField.css";
import { useState } from "react";

const FormSelectMenuField = ({
  field,
  theme,
  formValues,
  setFormValues,
  antForm,
}) => {
  const [itemValue, setItemValue] = useState(
    formValues
      ? Object.values(formValues).find(
          (formValue) => formValue.fieldId === field.id
        )?.value
      : null
  );
  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  const handleOnChange = (e) => {
    const value = e;
    setItemValue(value);
    setFormValues({
      ...formValues,
      [field.id]: {
        fieldId: field.id,
        type: field.type,
        title: field.title,
        value: value,
      },
    });
  };

  return (
    <>
      <div
        className="formSelectMenuField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formSelectMenuField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          value={antForm.getFieldsValue([field.id])}
          rules={[
            {
              required: field.required,
              message: field.title + " is required",
            },
          ]}
          wrapclassname="formSelectMenuField-formItem"
        >
          <div
            className="formSelectMenuField-formItem-input-cover"
            style={{
              /*borderColor: theme ? theme.questionColor : "",*/
              borderRadius: theme ? getInputBorderRadius() : "3px",
            }}
          >
            <Select
              className="formSelectMenuField-formItem"
              style={{
                /* borderColor: theme ? theme.questionColor : "",*/
                borderRadius: theme ? getInputBorderRadius() : "",
              }}
              onChange={handleOnChange}
              options={[...field.options]}
              placeholder={field.placeholder}
              value={itemValue}
            ></Select>
          </div>
        </Form.Item>
        {field.description && field.description.length > 0 && (
          <div className="formSelectMenuField-description">
            {field.description}
          </div>
        )}
      </div>
    </>
  );
};

export default FormSelectMenuField;
