import { DatePicker, Form } from "antd";
import "./FormDatePickerField.css";

const FormDatePickerField = ({ field, theme, formValues, setFormValues }) => {
  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  const handleOnChange = (date, dateString) => {
    const value = dateString;
    setFormValues({
      ...formValues,
      [field.id]: {
        fieldId: field.id,
        type: field.type,
        title: field.title,
        value: value,
      },
    });
  };

  return (
    <>
      <div
        className="formDatePickerField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formDatePickerField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          rules={[
            {
              required: field.required,

              message: field.title + " is required",
            },
          ]}
          wrapClassName="formDatePickerField-formItem"
        >
          <DatePicker
            placeholder={field.placeholder}
            className="formDatePickerField-formItem"
            style={{
              borderRadius: theme ? getInputBorderRadius() : "",
            }}
            value={field.id}
            onChange={handleOnChange}
          />
        </Form.Item>
        {field.description && field.description.length > 0 && (
          <div className="formDatePickerField-description">
            {field.description}
          </div>
        )}
      </div>
    </>
  );
};

export default FormDatePickerField;
