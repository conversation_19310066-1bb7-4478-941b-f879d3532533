import "./FormHeading1Field.css";

const FormHeading1Field = ({ field, theme }) => {
  return (
    <div
      className="formHeading1Field-container"
      style={{
        fontFamily: theme ? theme.font : "Inter",
        color: theme ? theme.questionColor : "",
        textAlign: field.textAlignment,
      }}
    >
      <div className="formHeading1Field-title">{field.title}</div>
      <div className="formHeading1Field-description">{field.description}</div>
    </div>
  );
};

export default FormHeading1Field;
