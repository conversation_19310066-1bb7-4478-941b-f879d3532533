import { Form, InputNumber } from "antd";
import "./FormNumberField.css";

const FormNumberField = ({ field, theme, formValues, setFormValues }) => {
  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  const handleOnChange = (e) => {
    const value = e;
    setFormValues({
      ...formValues,
      [field.id]: {
        fieldId: field.id,
        type: field.type,
        title: field.title,
        value: value,
      },
    });
  };

  return (
    <>
      <div
        className="formNumberField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formNumberField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          rules={[
            {
              type: "number",
              message: "The input is not a number",
              transform(value) {
                if (value) {
                  return Number(value);
                }
              },
            },
            {
              required: field.required,
              message: field.title + " is required",
            },
          ]}
          wrapClassName="formNumberField-formItem"
        >
          <div style={{ width: "150px" }}>
            <InputNumber
              placeholder={field.placeholder}
              className="formNumberField-formItem"
              style={{
                borderRadius: theme ? getInputBorderRadius() : "",
              }}
              onChange={handleOnChange}
            />
          </div>
        </Form.Item>
        {field.description && field.description.length > 0 && (
          <div className="formNumberField-description">{field.description}</div>
        )}
      </div>
    </>
  );
};

export default FormNumberField;
