.formImageUploadField-container {
    margin-bottom: 30px;
}

.formImageUploadField-label {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 5px;
}

.formImageUploadField-description {
    font-size: 12px;
    opacity: .9;
    margin-top: -10px;
}

.formImageUploadField-formItem {
    font-family: 'Inter', sans-serif !important;
}

.formImageUploadField-rendered-item-icon {
    font-size: 24px;
    margin-top: 10px;
}

.formImageUploadField-browse-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    padding: 15px;
    border: 1px dashed var(--grey200);
}

.formImageUploadField-browse-icon {
    font-size: 42px;
}

.formImageUploadField-browse-placeholder {
    font-size: 17px;
    font-weight: 500;
}

.formImageUploadField-browse-description {
    font-size: 14px;
}

.formImageUploadField-fileList-container {
    width: 100%;
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.formImageUploadField-fileItem {
    display: flex;
    justify-content: space-between;
    border: 1px solid var(--grey200);
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.formImageUploadField-fileItem-icon {
    font-size: 24px;
    margin-right: 20px;
}

.formImageUploadField-fileItem-delete {
    font-size: 16px;
    padding: 3px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 3px;
    color: var(--red100);
    cursor: pointer;
}

.formImageUploadField-fileItem-delete:hover {
    background-color: var(--red100);
    color: var(--white);
    transition: .3s all;
}

.formImageUploadField-errorContainer {
    color: var(--red100);
    font-size: 14px;
}

.ant-upload-wrapper .ant-upload-list.ant-upload-list-picture .ant-upload-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    border: 0px solid #f9f9f9 !important;
    height: 45px !important;
}

.ant-upload:hover {
    border-color: var(--grey300) !important;
}

.ant-form-item-label>label {
    font-family: "Inter", sans-serif;
}

.ant-input {
    border-radius: 3px;
    height: 40px !important;
}

.ant-input::before {
    background-color: var(--white) !important;
}

.ant-input::after {
    background-color: var(--white) !important;
}

.ant-input-outlined {
    height: 40px !important;
    border-width: 1px;
    border-style: solid;
    border-color: var(--grey300) !important;
    font-family: "Inter", sans-serif;
}

.ant-input-outlined:hover {
    /*border-color: var(--bg-dark) !important;*/
}

.ant-input-outlined:focus {
    /*border-color: var(--bg-dark) !important;    */
}

.ant-input-outlined:focus-within {
    box-shadow: 0 0 0 0px #acabab;
}

.ant-input-affix-wrapper {
    height: 45px !important;
    border-radius: 3px;
    border-width: 1px;
}

.ant-input-affix-wrapper>input.ant-input {
    height: 25px !important;
}

.ant-input-affix-wrapper:focus-within {
    border: 1px solid var(--grey300);
}