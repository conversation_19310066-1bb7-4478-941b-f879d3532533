.formEmailField-container {
    margin-bottom: 30px;
}

.formEmailField-label {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 5px;
}

.formEmailField-description {
    font-size: 12px;
    opacity: .9;
    margin-top: -10px;
}

.formSelectMenuField-field-error{
    display: flex;
    align-items: center;
    background-color: red;
    padding: 2px 10px;
    font-size: 12px;
    font-weight: 400;
    width: auto;
    color: var(--white);
    font-family: 'Exo 2', sans-serif;
    border-radius: 3px;
    margin-top: 10px;
    width: fit-content;
}

.formEmailField-formItem {
    font-family: 'Inter', sans-serif !important;    
}

.ant-form-item-label >label {
    font-family: "Inter", sans-serif;
}

.ant-input {
    border-radius: 3px;
    height: 40px !important;
}

.ant-input::before {
    background-color: var(--white) !important;
}

.ant-input::after {
    background-color: var(--white) !important;
}

.ant-input-outlined {
    height: 40px !important;
    border-width: 1px;
    border-style: solid;
    border-color: var(--grey300) !important;
    font-family: "Inter", sans-serif;
}

.ant-input-outlined:hover {
    /*border-color: var(--bg-dark) !important;*/
}

.ant-input-outlined:focus {
    /*border-color: var(--bg-dark) !important;    */
}

.ant-input-outlined:focus-within {
    box-shadow: 0 0 0 0px #acabab;
}

.ant-input-affix-wrapper {
    height: 45px !important;
    border-radius: 3px;
    border-width: 1px;
}

.ant-input-affix-wrapper >input.ant-input {
    height: 25px !important;
}

.ant-input-affix-wrapper:focus-within {
    border: 1px solid var(--grey300);
}