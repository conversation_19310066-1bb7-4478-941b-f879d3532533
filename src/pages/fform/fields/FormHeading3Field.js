import "./FormHeading3Field.css";

const FormHeading3Field = ({ field, theme }) => {
  return (
    <div
      className="formHeading3Field-container"
      style={{
        fontFamily: theme ? theme.font : "Inter",
        color: theme ? theme.questionColor : "",
        textAlign: field.textAlignment,
      }}
    >
      <div className="formHeading3Field-title">{field.title}</div>
      <div className="formHeading3Field-description">{field.description}</div>
    </div>
  );
};

export default FormHeading3Field;
