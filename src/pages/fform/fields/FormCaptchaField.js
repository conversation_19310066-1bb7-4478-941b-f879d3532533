import { Form } from "antd";
import "./FormCaptchaField.css";
import ReCAPTC<PERSON> from "react-google-recaptcha";

const FormCaptchaField = ({ field, theme, formValues, setFormValues }) => {
  const handleOnChange = (e) => {
    const value = e.target.value;
    setFormValues({
      ...formValues,
      [field.id]: {
        fieldId: field.id,
        type: field.type,
        title: field.title,
        value: value,
      },
    });
  };

  return (
    <>
      <div
        className="formCaptchaField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formCaptchaField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          rules={[
            {
              required: true,
              message: field.title + " is required",
            },
          ]}
          wrapClassName="formFileUploadField-formItem"
        >
          <ReCAPTCHA
            sitekey="6Ldehk4qAAAAAL7ubOn6v-IJFWL4xzLKh0MhtSnP"
            onChange={handleOnChange}
          />
        </Form.Item>
        {field.description && field.description.length > 0 && (
          <div className="formCaptchaField-description">
            {field.description}
          </div>
        )}
      </div>
    </>
  );
};

export default FormCaptchaField;
