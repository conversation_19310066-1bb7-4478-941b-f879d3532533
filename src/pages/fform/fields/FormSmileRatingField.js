import { Form, Rate } from "antd";
import "./FormSmileRatingField.css";
import { useEffect, useState } from "react";
import {
  CgSmile,
  CgSmileMouthOpen,
  CgSmileNone,
  CgSmileSad,
} from "react-icons/cg";
import { BiWinkSmile } from "react-icons/bi";

const FormSmileRatingField = ({ field, theme, formValues, setFormValues }) => {
  const [value, setValue] = useState(3);
  const desc = ["terrible", "bad", "normal", "good", "wonderful"];

  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  useEffect(() => {
    setFormValues({ ...formValues, [field.id]: value });
  }, [value]);

  const handleOnChange = (e) => {
    const val = e;
    setValue(val);
    setFormValues({
      ...formValues,
      [field.id]: {
        fieldId: field.id,
        type: field.type,
        title: field.title,
        value: val,
      },
    });
  };

  const customIcons = {
    1: <CgSmileSad />,
    2: <CgSmileNone />,
    3: <BiWinkSmile />,
    4: <CgSmile />,
    5: <CgSmileMouthOpen />,
  };

  return (
    <>
      <div
        className="formSmileRatingField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formSmileRatingField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          rules={[
            {
              required: field.required,

              message: field.title + " is required",
            },
          ]}
          wrapClassName="formSmileRatingField-formItem"
        >
          <Rate
            character={({ index = 0 }) => customIcons[index + 1]}
            value={value}
            defaultValue={value}
            tooltips={desc}
            placeholder={field.placeholder}
            className="formSmileRatingField-formItem"
            style={{
              color: theme ? theme.questionColor : "var(--black)",
              borderRadius: theme ? getInputBorderRadius() : "",
              fontSize: "36px",
            }}
            onChange={handleOnChange}
          />
        </Form.Item>
        {field.description && field.description.length > 0 && (
          <div className="formSmileRatingField-description">
            {field.description}
          </div>
        )}
      </div>
    </>
  );
};

export default FormSmileRatingField;
