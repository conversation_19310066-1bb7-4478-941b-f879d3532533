.formShortInputField-container {
    margin-bottom: 30px;
}

.formShortInputField-label {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 5px;
}

.formShortInputField-description {
    font-size: 12px;
    opacity: .9;
    margin-top: -10px;
}

.formShortInputField-formItem {
    font-family: 'Inter', sans-serif !important;    
}

.ant-form-item-label >label {
    font-family: "Inter", sans-serif;
}

.ant-input {
    border-radius: 3px;
    height: 40px !important;
}

.ant-input::before {
    background-color: var(--white) !important;
}

.ant-input::after {
    background-color: var(--white) !important;
}

.ant-input-outlined {
    height: 40px !important;
    border-width: 1px;
    border-style: solid;
    border-color: var(--grey300) !important;
    font-family: "Inter", sans-serif;
}

.ant-input-outlined:hover {
    /*border-color: var(--bg-dark) !important;*/
}

.ant-input-outlined:focus {
    /*border-color: var(--bg-dark) !important;    */
}

.ant-input-outlined:focus-within {
    box-shadow: 0 0 0 0px #acabab;
}

.ant-input-affix-wrapper {
    height: 45px !important;
    border-radius: 3px;
    border-width: 1px;
}

.ant-input-affix-wrapper >input.ant-input {
    height: 25px !important;
}

.ant-input-affix-wrapper:focus-within {
    border: 1px solid var(--grey300);
}