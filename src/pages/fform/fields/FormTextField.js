import "./FormTextField.css";

const FormTextField = ({ field, theme }) => {
  const getFontSize = () => {
    if (field.textSize === "SMALL") {
      return "13px";
    } else if (field.textSize === "LARGE") {
      return "17px";
    } else {
      return "15px";
    }
  };

  return (
    <div
      className="formTextField-container"
      style={{
        fontFamily: theme ? theme.font : "Inter",
        color: theme ? theme.questionColor : "",
        textAlign: field.textAlignment,
      }}
    >
      <div
        className="formTextField-title"
        style={{
          fontSize: getFontSize(),
        }}
      >
        {field.title}
      </div>
    </div>
  );
};

export default FormTextField;
