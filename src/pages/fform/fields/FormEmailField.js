import { Form, Input } from "antd";
import "./FormEmailField.css";

const FormEmailField = ({ field, theme, formValues, setFormValues }) => {
  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  const handleOnChange = (e) => {
    const value = e.target.value;
    if (
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
        value
      )
    ) {
    } else {
    }

    setFormValues({
      ...formValues,
      [field.id]: {
        fieldId: field.id,
        type: field.type,
        title: field.title,
        value: value,
      },
    });
  };
  return (
    <>
      <div
        className="formEmailField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formEmailField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          rules={[
            {
              required: field.required,
              type: "email",
              message: "Please enter your email address",
            },
            {
              required: field.required,
              message: field.title + " is required",
            },
          ]}
          wrapClassName="formEmailField-formItem"
        >
          <Input
            placeholder={field.placeholder}
            className="formEmailField-formItem"
            style={{
              borderColor: theme ? theme.questionColor : "",
              borderRadius: theme ? getInputBorderRadius() : "",
            }}
            onChange={handleOnChange}
          />
        </Form.Item>
        {field.description && field.description.length > 0 && (
          <div className="formSelectMenuField-description">
            {field.description}
          </div>
        )}
        {/*
        {error && (
          <div className="formSelectMenuField-field-error">
            <MdOutlineError style={{ marginRight: "5px" }} />
            {error}
          </div>
        )}
          */}
      </div>
    </>
  );
};

export default FormEmailField;
