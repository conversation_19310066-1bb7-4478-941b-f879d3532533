import { Form } from "antd";
import "./FormLongInputField.css";
import TextArea from "antd/es/input/TextArea";

const FormLongInputField = ({ field, theme, formValues, setFormValues }) => {
  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  const handleOnChange = (e) => {
    const value = e.target.value;
    setFormValues({
      ...formValues,
      [field.id]: {
        fieldId: field.id,
        type: field.type,
        title: field.title,
        value: value,
      },
    });
  };

  return (
    <>
      <div
        className="formLongInputField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formLongInputField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          rules={[
            {
              required: field.required,

              message: field.title + " is required",
            },
          ]}
          wrapClassName="formLongInputField-formItem"
        >
          <TextArea
            placeholder={field.placeholder}
            className="formLongInputField-formItem"
            style={{
              borderColor: theme ? theme.questionColor : "",
              borderRadius: theme ? getInputBorderRadius() : "",
            }}
            onChange={handleOnChange}
            autoSize={{ minRows: 6, maxRows: 6 }}
          />
        </Form.Item>
        {field.description && field.description.length > 0 && (
          <div className="formLongInputField-description">
            {field.description}
          </div>
        )}
      </div>
    </>
  );
};

export default FormLongInputField;
