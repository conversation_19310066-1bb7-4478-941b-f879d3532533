import "./FormImageField.css";

const FormImageField = ({ field, theme, formValues, setFormValues }) => {
  return (
    <>
      <div
        style={{
          width: "100%",
          display: "flex",
          justifyContent: field.textAlignment,
        }}
      >
        {field.fileUrl && (
          <img
            // src={field.file.thumbUrl}
            src={`${process.env.REACT_APP_BACKEND_URL}/api/v1/response/files/image/${field.fileUrl}`}
            style={{
              width: field.width,
              height: field.height,
              margin: "20px 0",
            }}
            alt="fileImage"
          />
        )}
      </div>
    </>
  );
};

export default FormImageField;
