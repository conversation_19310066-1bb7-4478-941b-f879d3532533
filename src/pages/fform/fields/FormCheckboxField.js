import { Checkbox, Form, Space } from "antd";
import "./FormCheckboxField.css";
import styled, { css } from "styled-components";
import { useState } from "react";

const CustomCheckbox = styled(Checkbox)`
  ${(props) =>
    props.backgroundColor &&
    css`
      & .ant-checkbox-checked .ant-checkbox-inner {
        background-color: ${props.backgroundColor} !important;
        border-color: ${props.backgroundColor};

        &&:hover {
          opacity: 0.8 !important;
        }
      }
    `}
`;

const FormCheckboxField = ({
  field,
  theme,
  formValues,
  setFormValues,
  antForm,
}) => {
  const [itemValue, setItemValue] = useState(
    formValues
      ? Object.values(formValues).find(
          (formValue) => formValue.fieldId === field.id
        )?.value
      : null
  );
  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  const handleOnChange = (e) => {
    const value = e;
    setItemValue(value);
    setFormValues({
      ...formValues,
      [field.id]: {
        fieldId: field.id,
        type: field.type,
        title: field.title,
        value: value,
      },
    });
  };

  return (
    <>
      <div
        className="formCheckboxField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formCheckboxField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          value={antForm.getFieldsValue([field.id])}
          rules={[
            {
              required: field.required,
              message: field.title + " is required",
            },
          ]}
          wrapClassName="formCheckboxField-formItem"
        >
          <div
            className="formCheckboxField-formItem-input-cover"
            style={{
              /*borderColor: theme ? theme.questionColor : "",*/
              borderRadius: theme ? getInputBorderRadius() : "3px",
            }}
          >
            <Checkbox.Group
              backgroundColor={theme ? theme.questionColor : "#000"}
              style={{
                marginBottom: "5px",
                color: theme ? theme.questionColor : "",
              }}
              onChange={handleOnChange}
              value={itemValue}
            >
              <Space direction="vertical">
                {field.options?.map((option) => (
                  <CustomCheckbox
                    key={option.id}
                    value={option.label}
                    backgroundColor={theme ? theme.questionColor : "#000"}
                    style={{
                      marginBottom: "5px",
                      color: theme ? theme.questionColor : "",
                    }}
                  >
                    {option.label}
                  </CustomCheckbox>
                ))}
              </Space>
            </Checkbox.Group>
          </div>
        </Form.Item>
        {field.description && field.description.length > 0 && (
          <div className="formCheckboxField-description">
            {field.description}
          </div>
        )}
      </div>
    </>
  );
};

export default FormCheckboxField;
