import { Form, Input, TimePicker } from "antd";
import "./FormTimePickerField.css";

const FormTimePickerField = ({ field, theme, formValues, setFormValues }) => {
  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  const handleOnChange = (time, timeString) => {
    const value = timeString;
    setFormValues({
      ...formValues,
      [field.id]: {
        fieldId: field.id,
        type: field.type,
        title: field.title,
        value: value,
      },
    });
  };

  return (
    <>
      <div
        className="formTimePickerField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formTimePickerField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          rules={[
            {
              required: field.required,
              message: field.title + " is required",
            },
          ]}
          wrapClassName="formTimePickerField-formItem"
        >
          <TimePicker
            placeholder={field.placeholder}
            className="formTimePickerField-formItem"
            style={{
              borderRadius: theme ? getInputBorderRadius() : "",
            }}
            onChange={handleOnChange}
          />
        </Form.Item>
        {field.description && field.description.length > 0 && (
          <div className="formTimePickerField-description">
            {field.description}
          </div>
        )}
      </div>
    </>
  );
};

export default FormTimePickerField;
