import { Form, Upload } from "antd";
import "./FormVideoUploadField.css";
import { MdDeleteOutline } from "react-icons/md";
import { useState } from "react";
import SmallLoading from "../../../components/UI/Loading/SmallLoading";
import { IoVideocamOutline } from "react-icons/io5";
const { Dragger } = Upload;

const FormVideoUploadField = ({
  field,
  theme,
  formValues,
  setFormValues,
  isPreview,
}) => {
  const [error, setError] = useState();

  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [loading, setLoading] = useState(false);

  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  const handleChange = (info) => {
    if (info.file.status === "done") {
    } else if (info.file.status === "error") {
    }
  };

  const customRequest = async ({ file, onSuccess, onError }) => {
    setLoading(true);
    setError("");
    if (file.size > 10000000) {
      setError("You cannot upload files larger than 10 MB.");
      setLoading(false);
      return;
    }

    if (field.filesLimit <= uploadedFiles.length) {
      setError("You can upload " + field.filesLimit + " file/files");
      setLoading(false);
      return;
    }
    const formData = new FormData();
    formData.append("file", file);

    const tempFiles = uploadedFiles;

    try {
      const response = await fetch(isPreview ? "/" : "/api/upload", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        onSuccess(file);

        setFormValues({ ...formValues, [field.id]: file });
      } else {
        tempFiles.push(file);
        setUploadedFiles(tempFiles);
        setFormValues({ ...formValues, [field.id]: uploadedFiles });
        if (isPreview) {
          onSuccess(file);
        } else {
          onError(new Error("File upload error"));
        }
      }
    } catch (error) {
      if (isPreview) {
        tempFiles.push(file);
        setUploadedFiles(tempFiles);
        setFormValues({ ...formValues, [field.id]: uploadedFiles });
      } else {
        onError(error);
      }
    } finally {
      setLoading(false);
    }
  };

  const deleteFile = (file) => {
    setError("");
    setLoading(true);
    const tempFiles = uploadedFiles;
    const deletingFileIndex = tempFiles.indexOf(file);
    uploadedFiles.splice(deletingFileIndex, 1);
    setTimeout(() => {
      setUploadedFiles(uploadedFiles);
      setLoading(false);
    }, 50);
  };

  return (
    <>
      <div
        className="formVideoUploadField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formVideoUploadField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          rules={[
            {
              required: field.required,
              message: field.title + " is required",
            },
          ]}
          wrapClassName="formVideoUploadField-formItem"
        >
          <Dragger
            name="file"
            maxCount={field.filesLimit}
            customRequest={customRequest}
            onChange={handleChange}
            showUploadList={false}
            accept=".mp3, .mp4, .wma, .avi, .mpg, .flv"
            listType="picture"

            //beforeUpload={() => false}
            //onChange={(e) => onChange(e)}
            //onRemove={(e) => fileRemoved(e)}
            //defaultFileList={defaultFileList}
          >
            <div
              className="formVideoUploadField-browse-container"
              style={{
                borderColor: theme ? theme.questionColor : "",
                borderRadius: theme ? getInputBorderRadius() : "",
              }}
            >
              <div
                className="formVideoUploadField-browse-icon"
                style={{
                  color: theme ? theme.questionColor : "",
                }}
              >
                <IoVideocamOutline
                  style={{
                    color: theme ? theme.questionColor : "",
                  }}
                />
              </div>
              <div
                className="formVideoUploadField-browse-placeholder"
                style={{
                  color: theme ? theme.questionColor : "",
                  fontFamily: theme ? theme.font : "Inter",
                }}
              >
                {field.placeholder}
              </div>
              <div
                className="formVideoUploadField-browse-description"
                style={{
                  color: theme ? theme.questionColor : "",
                  fontFamily: theme ? theme.font : "Inter",
                }}
              >
                Drag and drop files here
              </div>
              <div
                style={{
                  fontSize: "13px",
                  marginTop: "10px",
                  color: theme ? theme.questionColor : "",
                  fontFamily: theme ? theme.font : "Inter",
                  opacity: 0.8,
                }}
              ></div>
            </div>
          </Dragger>
          {loading && <SmallLoading />}
          {!loading && uploadedFiles && uploadedFiles.length > 0 && (
            <div className="formVideoUploadField-fileList-container">
              {uploadedFiles.map((file, index) => (
                <div className="formVideoUploadField-fileItem" key={index}>
                  <div style={{ display: "flex" }}>
                    <div
                      className="formVideoUploadField-fileItem-icon"
                      style={{ color: theme ? theme.questionColor : "" }}
                    >
                      <IoVideocamOutline />
                    </div>
                    <div
                      className="formVideoUploadField-fileItem-name"
                      style={{ color: theme ? theme.questionColor : "" }}
                    >
                      {file.name}
                    </div>
                  </div>
                  <div
                    className="formVideoUploadField-fileItem-delete"
                    onClick={() => deleteFile(file)}
                  >
                    <MdDeleteOutline />
                  </div>
                </div>
              ))}
            </div>
          )}
          <div className="formVideoUploadField-errorContainer">{error}</div>
        </Form.Item>

        {field.description && field.description.length > 0 && (
          <div className="formVideoUploadField-description">
            {field.description}
          </div>
        )}
      </div>
    </>
  );
};

export default FormVideoUploadField;
