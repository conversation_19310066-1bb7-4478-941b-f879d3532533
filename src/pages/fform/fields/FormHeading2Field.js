import "./FormHeading2Field.css";

const FormHeading2Field = ({ field, theme }) => {
  return (
    <div
      className="formHeading2Field-container"
      style={{
        fontFamily: theme ? theme.font : "Inter",
        color: theme ? theme.questionColor : "",
        textAlign: field.textAlignment,
      }}
    >
      <div className="formHeading2Field-title">{field.title}</div>
      <div className="formHeading2Field-description">{field.description}</div>
    </div>
  );
};

export default FormHeading2Field;
