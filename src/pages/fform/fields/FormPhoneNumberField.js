import { Form, Input } from "antd";
import "./FormPhoneNumberField.css";

const FormPhoneNumberField = ({ field, theme, formValues, setFormValues }) => {
  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  const handleOnChange = (e) => {
    const value = e.target.value;
    setFormValues({
      ...formValues,
      [field.id]: {
        fieldId: field.id,
        type: field.type,
        title: field.title,
        value: value,
      },
    });
  };

  return (
    <>
      <div
        className="formPhoneField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formPhoneField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          rules={[
            {
              type: "number",
              min: 1000000000,
              max: 999999999999,
              message: "Please enter valid phone number!",
              transform(value) {
                if (value) {
                  return Number(value);
                }
              },
            },
            {
              required: field.required,
              message: field.title + " is required",
            },
          ]}
          wrapClassName="formPhoneField-formItem"
        >
          <Input
            placeholder={field.placeholder}
            className="formPhoneField-formItem"
            style={{
              borderColor: theme ? theme.questionColor : "",
              borderRadius: theme ? getInputBorderRadius() : "",
            }}
            value={field.id}
            onChange={handleOnChange}
          />
        </Form.Item>
        {field.description && field.description.length > 0 && (
          <div className="formPhoneField-description">{field.description}</div>
        )}
      </div>
    </>
  );
};

export default FormPhoneNumberField;
