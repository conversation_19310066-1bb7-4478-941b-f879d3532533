import { Form, Input } from "antd";
import "./FormShortInputField.css";

const FormShortInputField = ({ field, theme, formValues, setFormValues }) => {
  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  const handleOnChange = (e) => {
    const value = e.target.value;
    setFormValues({
      ...formValues,
      [field.id]: {
        fieldId: field.id,
        type: field.type,
        title: field.title,
        value: value,
      },
    });
  };

  return (
    <>
      <div
        className="formShortInputField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formShortInputField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          rules={[
            {
              required: field.required,

              message: field.title + " is required",
            },
          ]}
          wrapClassName="formShortInputField-formItem"
        >
          <Input
            placeholder={field.placeholder}
            className="formShortInputField-formItem"
            style={{
              borderColor: theme ? theme.questionColor : "",
              borderRadius: theme ? getInputBorderRadius() : "",
            }}
            value={field.id}
            onChange={handleOnChange}
          />
        </Form.Item>
        {field.description && field.description.length > 0 && (
          <div className="formShortInputField-description">
            {field.description}
          </div>
        )}
      </div>
    </>
  );
};

export default FormShortInputField;
