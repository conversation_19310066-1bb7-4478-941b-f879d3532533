.formNumberField-container {
    margin-bottom: 30px;
}

.formNumberField-label {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 5px;
}

.formNumberField-description {
    font-size: 12px;
    opacity: .9;
    margin-top: -10px;
}

.formNumberField-formItem {
    display: flex;
    align-items: center;
    font-family: 'Inter', sans-serif !important;
    font-weight: 500;
}

.ant-input-number-outlined {
    width: 150px !important;
    height: 35px;
}

.ant-form-item-label>label {
    font-family: "Inter", sans-serif;
}

.ant-input {
    border-radius: 3px;
    height: 40px !important;
}

.ant-input::before {
    background-color: var(--white) !important;
}

.ant-input::after {
    background-color: var(--white) !important;
}

.ant-input-outlined {
    height: 40px !important;
    border-width: 1px;
    border-style: solid;
    border-color: var(--grey300) !important;
    font-family: "Inter", sans-serif;
}

.ant-input-outlined:hover {
    /*border-color: var(--bg-dark) !important;*/
}

.ant-input-outlined:focus {
    /*border-color: var(--bg-dark) !important;    */
}


.formNumberField-formItem:hover {
    border-color: var(--grey200);
}

.formNumberField-formItem:focus {
    border-color: var(--grey200);
    border-width: 1px;
}

.ant-input-number-outlined:focus-within {
    box-shadow: 0 0 0 0 rgba(5, 145, 255, 0);
}

.ant-input-number-outlined:focus {
    border-color: black;
}

.ant-input-outlined:focus-within {
    box-shadow: 0 0 0 0px #acabab;
}

.ant-input-affix-wrapper {
    height: 45px !important;
    border-radius: 3px;
    border-width: 1px;
}

.ant-input-affix-wrapper>input.ant-input {
    height: 25px !important;
}

.ant-input-affix-wrapper:focus-within {
    border: 1px solid var(--grey300);
}


.ant-input-number-outlined:focus-within {
    box-shadow: 0 0 0 0 rgba(5, 145, 255, 0);
}

::placeholder {
    color: var(--grey100) !important;
    font-weight: 300 !important;
    font-size: 13px !important;
    font-family: 'Inter', sans-serif;
}

::-ms-input-placeholder {
    color: var(--grey100) !important;
    font-weight: 300 !important;
    font-size: 13px !important;
    font-family: 'Inter', sans-serif;
}

::-ms-input-placeholder {
    color: var(--grey100) !important;
    font-weight: 300 !important;
    font-size: 13px !important;
    font-family: 'Inter', sans-serif;
}