import { Form, Rate } from "antd";
import "./FormStarRatingField.css";
import { useEffect, useState } from "react";

const FormStarRatingField = ({ field, theme, formValues, setFormValues }) => {
  const [value, setValue] = useState(3);
  const desc = ["terrible", "bad", "normal", "good", "wonderful"];

  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  useEffect(() => {
    setFormValues((prevValues) => ({
      ...prevValues,
      [field.id]: value,
    }));
  }, [value, field.id, setFormValues]);

  const handleOnChange = (e) => {
    const val = e;
    setValue(val);
    setFormValues({
      ...formValues,
      [field.id]: {
        fieldId: field.id,
        type: field.type,
        title: field.title,
        value: val,
      },
    });
  };

  return (
    <>
      <div
        className="formStarRatingField-container"
        style={{
          fontFamily: theme ? theme.font : "Inter",
          color: theme ? theme.answerColor : "",
        }}
      >
        <div className="formStarRatingField-label">
          {field.title}{" "}
          {field.required && <span style={{ color: "var(--red100)" }}>*</span>}
        </div>
        <Form.Item
          name={field.id}
          rules={[
            {
              required: field.required,

              message: field.title + " is required",
            },
          ]}
          wrapClassName="formStarRatingField-formItem"
        >
          <Rate
            value={value}
            defaultValue={value}
            tooltips={desc}
            placeholder={field.placeholder}
            className="formStarRatingField-formItem"
            style={{
              color: theme ? theme.questionColor : "var(--black)",
              borderRadius: theme ? getInputBorderRadius() : "",
              fontSize: "36px",
            }}
            onChange={handleOnChange}
          />
        </Form.Item>
        {field.description && field.description.length > 0 && (
          <div className="formStarRatingField-description">
            {field.description}
          </div>
        )}
      </div>
    </>
  );
};

export default FormStarRatingField;
