import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ield from "../fields/FormCheckboxField";
import FormDatePickerField from "../fields/FormDatePickerField";
import FormEmailField from "../fields/FormEmailField";
import FormHeading1Field from "../fields/FormHeading1Field";
import FormHeading2Field from "../fields/FormHeading2Field";
import FormHeading3Field from "../fields/FormHeading3Field";
import FormLongInputField from "../fields/FormLongInputField";
import FormMultiSelectMenuField from "../fields/FormMultiSelectMenuField";
import FormNumberField from "../fields/FormNumberField";
import FormPhoneNumberField from "../fields/FormPhoneNumberField";
import FormSelectMenuField from "../fields/FormSelectMenuField";
import FormShortInputField from "../fields/FormShortInputField";
import FormSingleSelectField from "../fields/FormSingleSelectField";
import FormTextField from "../fields/FormTextField";
import FormTimePickerField from "../fields/FormTimePickerField";
import FormFileUploadField from "../fields/FormFileUploadField";
import FormStarRatingField from "../fields/FormStartRatingField";
import FormScaleRatingField from "../fields/FormScaleRatingField";
import FormSmileRatingField from "../fields/FormSmileRatingField";
import FormDividerField from "../fields/FormDividerField";
import FormImageField from "../fields/FormImageField";
import FormCaptchaField from "../fields/FormCaptchaField";
import FormImageUploadField from "../fields/FormImageUploadField";
import FormVideoUploadField from "../fields/FormVideoUploadField";

function FormFieldRender(
  item,
  theme,
  formValues,
  setFormValues,
  antForm,
  isOpen,
  isPreview
) {
  if (item.type === "Heading1Field") {
    return <FormHeading1Field field={item} theme={theme} />;
  }

  if (item.type === "Heading2Field") {
    return <FormHeading2Field field={item} theme={theme} />;
  }

  if (item.type === "Heading3Field") {
    return <FormHeading3Field field={item} theme={theme} />;
  }

  if (item.type === "TextField") {
    return <FormTextField field={item} theme={theme} />;
  }

  if (item.type === "ShortInputField") {
    return (
      <FormShortInputField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
      />
    );
  }

  if (item.type === "LongInputField") {
    return (
      <FormLongInputField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
      />
    );
  }

  if (item.type === "SelectMenuField") {
    return (
      <FormSelectMenuField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
        isOpen={isOpen}
      />
    );
  }

  if (item.type === "MultiSelectMenuField") {
    return (
      <FormMultiSelectMenuField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
      />
    );
  }

  if (item.type === "SingleSelectField") {
    return (
      <FormSingleSelectField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
      />
    );
  }

  if (item.type === "CheckboxField") {
    return (
      <FormCheckboxField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
      />
    );
  }

  if (item.type === "EmailField") {
    return (
      <FormEmailField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
      />
    );
  }

  if (item.type === "NumberField") {
    return (
      <FormNumberField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
      />
    );
  }

  if (item.type === "PhoneNumberField") {
    return (
      <FormPhoneNumberField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
      />
    );
  }

  if (item.type === "TimePickerField") {
    return (
      <FormTimePickerField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
      />
    );
  }

  if (item.type === "DatePickerField") {
    return (
      <FormDatePickerField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
      />
    );
  }

  if (item.type === "FileUploadField") {
    return (
      <FormFileUploadField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
        isPreview
      />
    );
  }

  if (item.type === "ImageUploadField") {
    return (
      <FormImageUploadField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
        isPreview
      />
    );
  }

  if (item.type === "VideoUploadField") {
    return (
      <FormVideoUploadField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
        isPreview
      />
    );
  }

  if (item.type === "StarRatingField") {
    return (
      <FormStarRatingField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
        isPreview
      />
    );
  }

  if (item.type === "ScaleRatingField") {
    return (
      <FormScaleRatingField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
        isPreview
      />
    );
  }

  if (item.type === "SmileRatingField") {
    return (
      <FormSmileRatingField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
        isPreview
      />
    );
  }

  if (item.type === "DividerField") {
    return (
      <FormDividerField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
        isPreview
      />
    );
  }

  if (item.type === "ImageField") {
    return (
      <FormImageField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
        isPreview
      />
    );
  }

  if (item.type === "CaptchaField") {
    return (
      <FormCaptchaField
        field={item}
        theme={theme}
        setFormValues={setFormValues}
        formValues={formValues}
        antForm={antForm}
        isPreview
      />
    );
  }
}

export default FormFieldRender;
