import FormFieldRender from "./FormFieldRender";
import "./FormPage.css";

const FormPage = ({
  page,
  theme,
  prevStep,
  nextStep,
  formValues,
  setFormValues,
  antForm,
  isOpen,
  isPreview,
}) => {
  const sortedItemsByOrder = () => {
    return page.items?.sort((a, b) => a.order - b.order);
  };

  return (
    <>
      {sortedItemsByOrder() ? (
        <div className="formPage-container">
          {sortedItemsByOrder()
            .filter((i) => i.hide === false)
            .map((item) =>
              FormFieldRender(
                item,
                theme,
                formValues,
                setFormValues,
                antForm,
                isOpen,
                isPreview
              )
            )}
        </div>
      ) : (
        <div></div>
      )}
    </>
  );
};

export default FormPage;
