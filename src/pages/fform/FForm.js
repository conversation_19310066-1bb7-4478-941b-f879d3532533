import { useEffect, useRef, useState } from "react";
import "./FForm.css";
import GoogleAuthenticate from "./GoogleAuthenticate/GoogleAuthenticate";
import { useGoogleLogin } from "@react-oauth/google";
import axios from "axios";
import FormWelcomePage from "./FormWelcomePage/WelcomePageForm";
import FormPage from "./FormPage/FormPage";
import { Form } from "antd";
import logo from "../../assets/images/logo/logo-white-small-50.png";
import FormThankyouPage from "./FormThankyouPage/FormThankyouPage";
import FormFinishPage from "./FormFinishPage/FormFinishPage";

const FForm = ({ form, theme, isPreview, isOpen, perspective }) => {
  const [steps, setSteps] = useState([]);
  const [currentStep, setCurrentStep] = useState();
  const [authUser, setAuthUser] = useState();
  const [pageChangeType, setPageChangeType] = useState();

  const welcomePageRef = useRef();
  const thankyouPageRef = useRef();
  const finishPageRef = useRef();

  const [antForm] = Form.useForm();
  const [formValues, setFormValues] = useState();

  let stepList = [];

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(null);

      const stepList = [];

      if (form.googleAuthenticate) {
        stepList.push("googleAuthenticate");
      }

      if (form.web3) {
        stepList.push("web3");
      }

      if (form.welcomePage) {
        stepList.push("welcomePage");
      }

      form.pages?.forEach((page, index) => {
        stepList.push("page-" + index);
      });

      if (form.thankyouPage) {
        stepList.push("thankyouPage");
      }

      if (steps.length !== stepList.length) {
        setSteps(stepList);
      }

      setCurrentStep(stepList[0]);
    }
  }, [form, isOpen, steps]);

  const findCurrentPage = () => {
    const pageIndex = currentStep.substring(
      currentStep.indexOf("-") + 1,
      currentStep.length
    );
    const sortedPages = form.pages.sort((a, b) => a.order - b.order);
    return sortedPages[pageIndex];
  };

  const loginWithGoogle = useGoogleLogin({
    onSuccess: async (response) => {
      const res = await axios.get(
        "https://www.googleapis.com/oauth2/v3/userinfo",
        {
          headers: {
            Authorization: `Bearer ${response.access_token}`,
          },
        }
      );
      const loggedUser = {
        name: res.data.name,
        email: res.data.email,
        picture: res.data.picture,
      };
      setAuthUser(loggedUser);

      if (loggedUser && loggedUser.name) {
        nextStep();
      }
    },
  });

  const onCheckForm = async () => {
    try {
      await antForm.validateFields();
      nextStep();
    } catch (error) {
      console.log("onCheckForm error : ", error);
    }
  };

  const onSubmit = () => {
    // if (isPreview) return;
    /*
    console.log("emre values : ", Object.values(formValues));
    console.log("VaLues : ", formValues);
    */
    setCurrentStep("finish");
  };

  const prevStep = () => {
    setPageChangeType("prev");
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
    }
    setTimeout(() => {
      setPageChangeType(null);
    }, 300);
  };

  const nextStep = () => {
    setPageChangeType("next");
    const currentIndex = steps.indexOf(currentStep);
    setCurrentStep(steps[currentIndex + 1]);
    const nextPage = form.pages[currentIndex + 1];
    setTimeout(() => {
      setPageChangeType(null);
    }, 300);
  };

  const onFinish = (values) => {
    console.log("values : ", values);
  };

  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };

  const visibleBackButton = () => {
    if (currentStep === "page-0") {
      return false;
    } else {
      return true;
    }
  };

  const visibleNextButton = () => {
    if (currentStep === steps[steps.length - 1]) {
      return false;
    } else {
      return true;
    }
  };

  const getScreenSize = () => {
    if (perspective === "desktop") {
      return "800px";
    } else if (perspective === "tablet") {
      return "650px";
    } else if (perspective === "mobile") {
      return "420px";
    } else {
      return "100%";
    }
  };

  const setBackgroundSize = () => {
    if (perspective === "desktop") {
      return "100%";
    } else if (perspective === "tablet") {
      return "750px";
    } else if (perspective === "mobile") {
      return "auto";
    } else {
      return "100%";
    }
  };

  return (
    <>
      <div
        className="fform-container"
        style={{
          backgroundImage:
            theme && theme.pageBackgroundImage
              ? "url(" + theme.pageBackgroundImage + ")"
              : "",
          backgroundAttachment: "fixed",
          backgroundColor:
            theme && !theme.pageBackgroundImage && theme.pageBackgroundColor
              ? theme.pageBackgroundColor
              : "#FFFFFF",
          width: setBackgroundSize(),
        }}
      >
        <div
          className={
            pageChangeType === "prev"
              ? "fform-page-container-prev"
              : pageChangeType === "next"
              ? "fform-page-container-next"
              : "fform-page-container"
          }
          style={{
            backgroundColor:
              theme && !theme.backgroundImage ? theme.backgroundColor : "",
            backgroundImage: theme ? "url(" + theme.backgroundImage + ")" : "",
            backgroundRepeat: "round",
            maxWidth: getScreenSize(),
          }}
        >
          {currentStep === "googleAuthenticate" && (
            <GoogleAuthenticate handleClick={loginWithGoogle} />
          )}

          {/*
          {currentStep === "web3" && <appkit-button />}
        */}
          {currentStep === "welcomePage" && (
            <FormWelcomePage
              ref={welcomePageRef}
              welcomePageItem={form.welcomePageItem}
              theme={theme}
              nextStep={nextStep}
            />
          )}

          {currentStep?.includes("page") && (
            <div className="fform-form-container">
              <Form form={antForm} layout="vertical" onFinish={onFinish}>
                <FormPage
                  page={findCurrentPage()}
                  theme={theme}
                  prevStep={prevStep}
                  nextStep={nextStep}
                  formValues={formValues}
                  setFormValues={setFormValues}
                  antForm={antForm}
                  isOpen={isOpen}
                  isPreview={isPreview}
                />
              </Form>

              <div className="ffrom-form-button-container">
                {visibleBackButton() ? (
                  <div
                    className="fform-form-button"
                    style={{
                      fontFamily: theme && theme.font ? theme.font : "Inter",
                      backgroundColor:
                        theme && theme.buttonColor
                          ? theme.buttonColor
                          : "var(--grey300)",
                      color:
                        theme && theme.buttonTextColor
                          ? theme.buttonTextColor
                          : "var(--black)",
                      borderRadius:
                        theme && theme.rounded ? getInputBorderRadius() : "",
                    }}
                    onClick={prevStep}
                  >
                    Back
                  </div>
                ) : (
                  <div />
                )}

                {visibleNextButton() ? (
                  <div
                    className="fform-form-button"
                    style={{
                      fontFamily: theme && theme.font ? theme.font : "Inter",
                      backgroundColor:
                        theme && theme.buttonColor ? theme.buttonColor : "",
                      color:
                        theme && theme.buttonTextColor
                          ? theme.buttonTextColor
                          : "",
                      borderRadius:
                        theme && theme.rounded ? getInputBorderRadius() : "",
                    }}
                    onClick={onCheckForm}
                  >
                    Next
                  </div>
                ) : (
                  <div
                    className="fform-form-button"
                    style={{
                      fontFamily: theme && theme.font ? theme.font : "Inter",
                      backgroundColor:
                        theme && theme.buttonColor ? theme.buttonColor : "",
                      color:
                        theme && theme.buttonTextColor
                          ? theme.buttonTextColor
                          : "",
                      borderRadius:
                        theme && theme.rounded ? getInputBorderRadius() : "",
                    }}
                    onClick={onSubmit}
                  >
                    Submit{" "}
                  </div>
                )}
              </div>
            </div>
          )}
          {currentStep === "thankyouPage" && (
            <FormThankyouPage
              ref={thankyouPageRef}
              thankyouPageItem={form.thankyouPageItem}
              theme={theme}
            />
          )}

          {currentStep === "finish" && (
            <FormFinishPage ref={finishPageRef} theme={theme} />
          )}
        </div>

        <div className="fform-bottom-formiqo">
          <img src={logo} className="fform-bottom-formiqo-logo" alt="logo" />
          <div className="fform-bottom-formiqo-text"> Made with Formiqo</div>
        </div>
      </div>
    </>
  );
};

export default FForm;
