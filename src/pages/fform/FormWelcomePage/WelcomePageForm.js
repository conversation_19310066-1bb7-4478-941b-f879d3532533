import "./FormWelcomePage.css";

const FormWelcomePage = ({ welcomePageItem, theme, nextStep }) => {
  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };
  return (
    <div
      className="welcomePageForm-container"
      style={{
        fontFamily: theme && theme.font ? theme.font : "Inter",
        color: theme && theme.questionColor ? theme.questionColor : "",
      }}
    >
      <div className="welcomePageForm-title">{welcomePageItem.title}</div>
      <div className="welcomePageForm-description">
        {welcomePageItem.description}
      </div>
      <div
        className="welcomePageForm-button"
        style={{
          borderRadius: theme && theme.rounded ? getInputBorderRadius() : "",
          backgroundColor:
            theme && theme.buttonColor ? theme.buttonColor : "#000",
        }}
        onClick={nextStep}
      >
        {welcomePageItem.buttonText}
      </div>
    </div>
  );
};

export default FormWelcomePage;
