.fform-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: fit-content;
  background-repeat: round;
  background-size: cover;
  padding: 50px 30px;
  align-items: center;
  background-repeat: round;
  background-size: cover;
  flex-direction: column;
  justify-content: center;
  padding: 50px 30px;
  position: relative;
  height: 100%;
  /*min-height: 100vh;*/
}

.fform-page-container {
  /*width: 768px;*/
  width: 100%;
  /*box-shadow: rgba(0, 0, 0, 0.24) 0px 1px 4px;*/
  margin-top: 0px;
  margin-bottom: 30px;
  /*min-height: 500px;*/
  /* max-height: calc(100vh - 150px);*/
  /* overflow-y: auto;*/
  padding: 20px 10px;
  transition-property: transform;
  transition-duration: 0.3s;
  transition-timing-function: ease-out;
  transition-delay: 0s;
}

.fform-page-container-prev {
  transform-origin: 0 0;
  transform: translateX(-300px);
  opacity: 0;
  transition-property: transform;
  transition-duration: 0.3s;
  transition-timing-function: ease-out;
  transition-delay: 0s;
}

.fform-page-container-next {
  transform-origin: 0 0;
  transform: translateX(300px);
  opacity: 0;
  transition-property: transform;
  transition-duration: 0.3s;
  transition-timing-function: ease-out;
  transition-delay: 0s;
}

.fform-form-container {
  display: table;
  width: 100%;
  flex-direction: column;
  justify-content: space-between;
  /* min-height: 450px; */
  height: 100%;
  padding: 0 20px;
}

.ffrom-form-button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 10px;
}

.fform-form-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 30px;
  font-weight: 500;
  font-family: "Inter", sans-serif;
  background-color: var(--black);
  color: var(--white);
  border-radius: 5px;
  cursor: pointer;
}

.fform-form-button:hover {
  opacity: 0.8;
}

.fform-perspective-outer-container {
  position: fixed;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  top: 0;
  background-color: var(--white);
  z-index: 999;
}

.fform-perspective-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 10px;
  padding: 5px 10px;
  background-color: var(--white);
  border-radius: 5px;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 1px 4px;
  border: 1px solid var(--grey200);
}

.fform-option-preview-icon {
  padding: 3px;
  border: 1px solid var(--black);
  border-radius: 5px;
  /*border-bottom: 4px solid var(--black);*/
  color: var(--black);
  cursor: pointer;
  background-color: var(--white);
  font-size: 14px;
}

.fform-option-preview-icon:hover {
  border: 1px solid var(--grey100);
  background-color: var(--grey100);
  color: var(--white);
  transition: 0.15s all;
}

.fform-option-preview-icon-selected {
  padding: 3px;
  border: 1px solid var(--black);
  border-radius: 5px;
  border: 1px solid var(--black);
  background-color: var(--black);

  font-size: 14px;
  color: var(--white);
}

.fform-bottom-formiqo {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 10px;
  right: 20px;
  padding: 3px 15px;
  background-color: var(--white);
  border-radius: 30px;
  cursor: pointer;
}

.fform-bottom-formiqo-logo {
  width: 26px;
  height: 26px;
  margin-right: 10px;
}

.fform-bottom-formiqo-text {
  font-family: "Exo 2", sans-serif;
  font-weight: 600;
  font-size: 15px;
}
