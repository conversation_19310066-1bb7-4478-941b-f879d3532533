import { MdOutlineMarkEmailRead } from "react-icons/md";
import "./FormFinishPage.css";

const FormFinishPage = ({ theme }) => {
  return (
    <div
      className="finishPageForm-container"
      style={{
        fontFamily: theme && theme.font ? theme.font : "Inter",
        color: theme && theme.questionColor ? theme.questionColor : "",
      }}
    >
      <div className="finishPageForm-icon">
        <MdOutlineMarkEmailRead />{" "}
      </div>
      <div className="finishPageForm-title">Thank You !</div>
      <div className="finishPageForm-description">
        Your submission has been received.
      </div>
      <div className="finishPage-formiqo-container">
        <div className="finishPage-formiqo-description">
          Now create your own Formiqo form - It’s free!
        </div>
        <a
          target="_blank"
          href="https://www.formiqo.com"
          className="finishPage-formiqo-button"
          rel="noreferrer"
        >
          Create your own Formiqo
        </a>
      </div>
    </div>
  );
};

export default FormFinishPage;
