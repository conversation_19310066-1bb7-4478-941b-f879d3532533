.finishPageForm-container {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 500px;
    width: 100%;
    height: 100%;
}

.finishPageForm-icon {
    margin-bottom: 30px;
    font-size: 72px;
    color: var(--black);
}

.finishPageForm-title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 15px;
}

.finishPageForm-description {
    font-size: 15px;
    opacity: .9;
    margin-bottom: 30px;
}

.finishPageForm-button {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--black);
    color: var(--white);
    padding: 5px 20px;
    border-radius: 5px;
    cursor: pointer;
}

.finishPageForm-button:hover {
    opacity: .8;
}

a:hover {
    opacity: .8;
    color: var(--white);
}

a:focus {
    color: var(--white);
}

a:visited {
    color: var(--white);
}

.finishPage-formiqo-container {
    position: absolute;
    display: flex;
    justify-content: space-between;
    align-items: center;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    border-top: 1px solid var(--grey200);
}

.finishPage-formiqo-description {
    font-family: "Exo 2",sans-serif;
    font-size: 14px;
}

.finishPage-formiqo-button {
    padding: 8px 20px;
    background-color: var(--black);
    border-radius: 5px;
    font-family: "Exo 2", sans-serif;
    font-size: 15px;
    font-weight: 600;
    color: var(--white);
    letter-spacing: .3px;
    cursor: pointer;
}

.finishPage-formiqo-button:hover {
    opacity: .8;
    transition: .3s all;
}