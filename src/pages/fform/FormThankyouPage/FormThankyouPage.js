import { MdOutlineMarkEmailRead } from "react-icons/md";
import "./FormThankyouPage.css";

const FormThankyouPage = ({ thankyouPageItem, theme }) => {
  const getInputBorderRadius = () => {
    if (theme.rounded === 1) {
      return "0px";
    } else if (theme.rounded === 2) {
      return "5px";
    } else {
      return "30px";
    }
  };
  return (
    <div
      className="thankyouPageForm-container"
      style={{
        fontFamily: theme && theme.font ? theme.font : "Inter",
        color: theme && theme.questionColor ? theme.questionColor : "",
      }}
    >
      <div className="thankyouPageForm-icon">
        <MdOutlineMarkEmailRead />{" "}
      </div>
      <div className="thankyouPageForm-title">{thankyouPageItem.title}</div>
      <div className="thankyouPageForm-description">
        {thankyouPageItem.description}
      </div>
      {thankyouPageItem.redirectUrl && (
        <a
          href={thankyouPageItem.redirectUrl}
          className="thankyouPageForm-button"
          style={{
            borderRadius: theme && theme.rounded ? getInputBorderRadius() : "",
            backgroundColor:
              theme && theme.buttonColor ? theme.buttonColor : "#000",
          }}
        >
          {thankyouPageItem.redirectUrl}
        </a>
      )}
    </div>
  );
};

export default FormThankyouPage;
