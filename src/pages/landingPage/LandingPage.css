.landingPage-container {
  margin-top: 85px;
  height: 100%;
  background-color: var(--white);
}

.landingPage-section1 {
  padding-top: 40px !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.landingPage-segtion1-image {
  min-width: 450px;
  min-height: 510px;
  height: 100%;
}

.landingPage-segtion1-title {
  text-align: center;
  font-family: "Exo 2", sans-serif;
  font-size: 2.6rem;
  font-weight: 600;
}

.landingPage-segtion1-description {
  margin-top: 20px;
  margin-bottom: 30px;
  text-align: center;
  font-family: "Exo 2", sans-serif;
  font-weight: 400;
  font-size: 1.9rem;
  opacity: 0.9;
  padding: 0 10px;
}

.landingPage-segtion1-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
}

.landingPage-segtion1-button {
  background-color: var(--black);
  color: var(--white);
  padding: 15px 30px;
  border-radius: 5px;
  letter-spacing: 0.7px;
  font-family: "Exo 2", sans-serif;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
}

.landingPage-segtion1-button:hover {
  opacity: 0.8;
}

.landingPage-section2 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  padding-top: 20px !important;
}

.landingPage-segtion2-title {
  text-align: center;
  font-family: "Exo 2", sans-serif;
  font-size: 2rem;
  font-weight: 600;
}

.landingPage-video {
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "Exo 2", sans-serif;
  font-size: 36px;
  width: 1200px;
  height: 768px;
  border: 1px solid var(--grey400);
  border-radius: 5px;
  margin-top: 50px;
  background-color: #e6edf7;
}

.landingPage-section2-card {
  margin: 15px;
  padding: 15px;
  height: 360px;
  border-radius: 10px;
}

.landingPage-section2-card-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.landingPage-section2-card-icon {
  font-size: 56px;
}

.landingPage-section2-card-title {
  font-family: "Exo 2", sans-serif;
  font-weight: 600;
  font-size: 24px;
  margin-bottom: 15px;
}

.landingPage-section2-card-description {
  font-family: "Exo 2", sans-serif;
  font-weight: 400;
  font-size: 16px;
}

.green-border {
  border: 1px solid var(--green300);
  border-left: 8px solid var(--green300);
}

.orange-border {
  border: 1px solid var(--orange300);
  border-left: 8px solid var(--orange300);
}

.purple-border {
  border: 1px solid #c685c6;
  border-left: 8px solid #c685c6;
}

.landingPage-section3 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  padding-top: 20px !important;
}

.landingPage-section3-card {
  margin: 15px;
  padding: 15px;
  border-radius: 10px;
  border: 1px solid var(--grey300);
  background-color: var(--white);
  min-height: 180px;
}

.landingPage-section3-card-title {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 20px;
  margin-bottom: 15px;
}

.landingPage-section3-card-description {
  font-family: "Exo 2", sans-serif;
  font-weight: 400;
  font-size: 16px;
}

.landingPage-footer {
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--white);
  height: 80px;
  border-top: 1px solid var(--grey300);
  margin-top: 40px;
}
