import "./LPNavbar.css";
import logo from "../../assets/images/logo/logo-white-title-50.png";
import { useMediaQuery } from "react-responsive";
import { AiOutlineMenu } from "react-icons/ai";
import { useState } from "react";
import { IoMdClose } from "react-icons/io";
import { useNavigate } from "react-router-dom";

const LPNavbar = () => {
  const [menuOpened, setMenuOpened] = useState(false);
  const navigate = useNavigate();
  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 920px)" });

  return (
    <>
      <div className="lpNavbar-container">
        <div className="lpNavbar-logo-container">
          <img src={logo} alt="logo" />
        </div>
        {isTabletOrMobile ? (
          <div className="lpNavbar-menu-mobile">
            {!menuOpened ? (
              <AiOutlineMenu
                style={{ fontSize: "24px" }}
                onClick={() => {
                  setMenuOpened(!menuOpened);
                }}
              />
            ) : (
              <IoMdClose
                style={{ fontSize: "24px" }}
                onClick={() => {
                  setMenuOpened(!menuOpened);
                }}
              />
            )}
            {menuOpened && (
              <div className="lpNavbar-menu-mobile-expanded">
                {/*
                <div className="lpNavbar-menu-item-mobile">Resources</div>
                <div className="lpNavbar-menu-item-mobile">Pricing</div>
                */}
                <div
                  className="lpNavbar-menu-item-mobile"
                  onClick={() =>
                    navigate("/login", { state: { activeTab: "signIn" } })
                  }
                >
                  Sign in
                </div>
                <div
                  className="lpNavbar-menu-item-mobile signUp"
                  onClick={() =>
                    navigate("/login", { state: { activeTab: "signUp" } })
                  }
                >
                  Sign up
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="lpNavbar-menu">
            {/*
            <div className="lpNavbar-menu-item">Resources</div>
            <div className="lpNavbar-menu-item">Pricing</div>
            */}
            <div
              className="lpNavbar-menu-item"
              onClick={() =>
                navigate("/login", { state: { activeTab: "signIn" } })
              }
            >
              Sign in
            </div>
            <div
              className="lpNavbar-menu-item signUp"
              onClick={() =>
                navigate("/login", {
                  state: { activeTab: "signUp" },
                })
              }
            >
              Sign up
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default LPNavbar;
