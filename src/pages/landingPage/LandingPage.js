import "./LandingPage.css";
import LPNavbar from "./LPNavbar";
import { Col, Row } from "antd";
import { useMediaQuery } from "react-responsive";
import { FaPlus, FaShareAlt } from "react-icons/fa"; // Örnek ikon
import { IoIosAnalytics } from "react-icons/io";
import { Slide } from "react-slideshow-image";
import "react-slideshow-image/dist/styles.css";

const LandingPage = () => {
  const isTabletOrMobile = useMediaQuery({ query: "(max-width:1560px)" });

  return (
    <>
      <LPNavbar />
      <div className="landingPage-container">
        {/* Section 1 */}

        <Row
          className="landingPage-section1"
          style={{ padding: isTabletOrMobile ? "15px" : "15px 250px" }}
        >
          <Col>
            <Row className="landingPage-section1">
              <Col className="landingPage-segtion1-title">
                <PERSON>ı<PERSON>la ve Kolayca Profesyonel Formlar Oluşturun
              </Col>
            </Row>
            <Row>
              <Col className="landingPage-segtion1-description">
                <PERSON><PERSON><PERSON> ile her türlü formu sadece birkaç tıklama ile oluşturun.
                Kişiselleştirilmiş temalar, çok sayfalı formlar ve sürükle-bırak
                arayüzü ile form oluşturma deneyiminizi mükemmelleştirin.
              </Col>
            </Row>
            <Row className="landingPage-segtion1-button-container">
              <Col className="landingPage-segtion1-button">
                Create an account completely free
              </Col>
            </Row>
          </Col>
        </Row>

        {/* Section 2 - Özellikler */}
        <Row
          className="landingPage-section2"
          style={{ padding: isTabletOrMobile ? "15px" : "15px 250px" }}
        >
          <Col
            xs={{ flex: "100%" }}
            sm={{ flex: "100%" }}
            md={{ flex: "100%" }}
            lg={{ flex: "30%" }}
            xl={{ flex: "30%" }}
            className="landingPage-section2-card green-border"
          >
            <div className="landingPage-section2-card-icon-container">
              <FaPlus
                className="landingPage-section2-card-icon"
                style={{ color: "var(--green300)" }}
              />
            </div>
            <div className="landingPage-section2-card-title">
              İstediğiniz formu hemen oluşturun
            </div>
            <div className="landingPage-section2-card-description">
              {" "}
              Sadece birkaç adımda istediğiniz türde formu oluşturun.
              İhtiyacınıza göre anket, başvuru formu ya da geri bildirim formu
              tasarlayın. Kullanıcı dostu arayüzü ile hızlı ve etkili bir
              şekilde form hazırlayın.
            </div>
          </Col>
          <Col
            xs={{ flex: "100%" }}
            sm={{ flex: "100%" }}
            md={{ flex: "100%" }}
            lg={{ flex: "30%" }}
            xl={{ flex: "30%" }}
            className="landingPage-section2-card orange-border"
          >
            <div className="landingPage-section2-card-icon-container">
              <FaShareAlt
                className="landingPage-section2-card-icon"
                style={{ color: "var(--orange300)" }}
              />
            </div>
            <div className="landingPage-section2-card-title">
              Formunuzu sosyal medyada veya sitenizde paylaşın
            </div>
            <div className="landingPage-section2-card-description">
              {" "}
              Oluşturduğunuz formu kolayca sosyal medya platformlarında
              (Facebook, Twitter, LinkedIn, WhatsApp, Telegram) paylaşın.
              Ayrıca, formu web sitenizde yerleştirmek için embed kodunu da
              edinin.
            </div>
          </Col>
          <Col
            xs={{ flex: "100%" }}
            sm={{ flex: "100%" }}
            md={{ flex: "100%" }}
            lg={{ flex: "30%" }}
            xl={{ flex: "30%" }}
            className="landingPage-section2-card purple-border"
          >
            <div className="landingPage-section2-card-icon-container">
              <IoIosAnalytics
                className="landingPage-section2-card-icon"
                style={{ color: "#c685c6" }}
              />
            </div>
            <div className="landingPage-section2-card-title">
              Yanıtları görüntüleme ve analiz etme
            </div>
            <div className="landingPage-section2-card-description">
              {" "}
              Formunuza gelen yanıtları hızlıca görüntüleyin ve detaylı analiz
              yapın. Grafiksel raporlar ve istatistikler ile verilerinizi
              kolayca inceleyin, anlamlı sonuçlar elde edin.
            </div>
          </Col>
        </Row>

        <div style={{ background: "#deeaf6", marginTop: "20px" }}>
          <div
            style={{
              textAlign: "center",
              paddingTop: "10px",
              marginBottom: "-20px",
              fontSize: "24px",
              fontFamily: "Exo 2",
            }}
          >
            User Reviews
          </div>
          <Slide duration={10000}>
            <div
              className="landingPage-section3 each-slide-effect"
              style={{ padding: isTabletOrMobile ? "15px" : "15px 250px" }}
            >
              <div className="landingPage-section3-card">
                <div className="landingPage-section3-card-title">John M.</div>
                <div className="landingPage-section3-card-description">
                  Creating forms is incredibly easy and fast! I was able to
                  design the form I wanted in just a few minutes and started
                  sharing it on social media right away. Very practical, highly
                  recommend!
                </div>
              </div>
              <div className="landingPage-section3-card">
                <div className="landingPage-section3-card-title">Sophia T.</div>
                <div className="landingPage-section3-card-description">
                  Sharing forms on social media is incredibly easy! Plus, the
                  reports for analyzing responses are very detailed. This really
                  simplified my work.
                </div>
              </div>
              <div className="landingPage-section3-card">
                <div className="landingPage-section3-card-title">Hakan E.</div>
                <div className="landingPage-section3-card-description">
                  I’ve tried many form tools, but this one is by far the most
                  user-friendly. The sharing and response analysis features are
                  excellent.
                </div>
              </div>
            </div>

            <div
              className="landingPage-section3 each-slide-effect"
              style={{
                padding: isTabletOrMobile ? "15px" : "15px 250px",
              }}
            >
              <div className="landingPage-section3-card">
                <div className="landingPage-section3-card-title">Olivia C.</div>
                <div className="landingPage-section3-card-description">
                  This is a platform where you can achieve truly professional
                  results. It’s both aesthetically pleasing and functional. The
                  tools for analyzing the data are really powerful.
                </div>
              </div>
              <div className="landingPage-section3-card">
                <div className="landingPage-section3-card-title">
                  Michael S.
                </div>
                <div className="landingPage-section3-card-description">
                  This was my first time creating a form, and the process was so
                  easy! I was able to create stylish and professional forms in
                  no time. I'll definitely use it again.
                </div>
              </div>
              <div className="landingPage-section3-card">
                <div className="landingPage-section3-card-title">David B.</div>
                <div className="landingPage-section3-card-description">
                  I started using the form embed code on my website. The
                  user-friendly interface made it super easy to integrate. I'm
                  really happy with the results!
                </div>
              </div>
            </div>

            <div
              className="landingPage-section3 each-slide-effect"
              style={{
                padding: isTabletOrMobile ? "15px" : "15px 250px",
              }}
            >
              <div className="landingPage-section3-card">
                <div className="landingPage-section3-card-title">Olivia C.</div>
                <div className="landingPage-section3-card-description">
                  This is a platform where you can achieve truly professional
                  results. It’s both aesthetically pleasing and functional. The
                  tools for analyzing the data are really powerful.
                </div>
              </div>
              <div className="landingPage-section3-card">
                <div className="landingPage-section3-card-title">
                  Michael S.
                </div>
                <div className="landingPage-section3-card-description">
                  This was my first time creating a form, and the process was so
                  easy! I was able to create stylish and professional forms in
                  no time. I'll definitely use it again.
                </div>
              </div>
              <div className="landingPage-section3-card">
                <div className="landingPage-section3-card-title">David B.</div>
                <div className="landingPage-section3-card-description">
                  I started using the form embed code on my website. The
                  user-friendly interface made it super easy to integrate. I'm
                  really happy with the results!
                </div>
              </div>
            </div>
          </Slide>
        </div>
        <div className="landingPage-footer"></div>
      </div>
    </>
  );
};

export default LandingPage;
