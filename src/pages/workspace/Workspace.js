import { SlOptions } from "react-icons/sl";
import Navbar from "../../components/UI/Navbar/Navbar";
import Sidebar from "../../components/UI/Sidebar/Sidebar";
import "./Workspace.css";
import { BsSortDown } from "react-icons/bs";
import { CiEdit, CiSearch } from "react-icons/ci";
import { IoIosLink } from "react-icons/io";
import { useCallback, useEffect, useRef, useState } from "react";
import { AiOutlineUsergroupAdd } from "react-icons/ai";
import {
  MdDeleteOutline,
  MdMoveDown,
  MdOutlineDriveFileMove,
  MdOutlineEditNote,
  MdOutlineLegendToggle,
  MdOutlineRemoveRedEye,
} from "react-icons/md";
import { useMainContext } from "../../context/MainContext";
import {
  copyToWorkspaceByIdAndFormId,
  deleteFormById,
  deleteWorkspaceById,
  duplicateFormById,
  getAllWorkspaces,
  listByWorkspace,
  moveToWorkspaceByIdAndFormId,
  renameFormById,
  renameWorkspaceById,
  saveWorkspace,
} from "../../services/http";
import { Col, Pagination, Row, message } from "antd";
import { IoDuplicateOutline } from "react-icons/io5";
import { GoShareAndroid } from "react-icons/go";
import { useNavigate } from "react-router-dom";
import RenameFormModal from "../../components/UI/Modals/RenameFormModal";
import { SiGoogleforms } from "react-icons/si";
import DeleteModal from "../../components/UI/Modals/DeleteFormModal";
import RenameWorkspaceModal from "../../components/UI/Modals/RenameWorkspaceModal";
import MoveToFormModal from "../../components/UI/Modals/MoveToFormModal";
import CopyToFormModal from "../../components/UI/Modals/CopyToFormModal";
import { useAuth } from "../../context/AuthContext";

const Workspace = () => {
  const [processed, setProcessed] = useState(false);
  const [forms, setForms] = useState([]);
  const [tempForms, setTempForms] = useState([]);
  const [workspaces, setWorkspaces] = useState([]);
  const [isExpandedWorkspaceOptions, setExpandedWorkspaceOptions] =
    useState(false);
  const [expandedForm, setExpandedForm] = useState();
  const [showExpandedFormActions, setShowExpandedFormActions] = useState(false);
  const [openedRenameWorkspaceModal, setOpenedRenameWorkspaceModal] =
    useState(false);
  const [openedDeleteWorkspaceModal, setOpenedDeleteWorkspaceModal] =
    useState();
  const [loadingDeleteWorkspace, setLoadingDeleteWorkspace] = useState(false);
  const [openedRenameModal, setOpenedRenameModal] = useState(false);
  const [loadingRename, setLoadingRename] = useState(false);
  const [renameError, setRenameError] = useState();
  const [openedDeleteModal, setOpenedDeleteModal] = useState(false);
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [openedCopyToFormModal, setOpenedCopyToFormModal] = useState(false);

  const [openedMoveToFormModal, setOpenedMoveToFormModel] = useState(false);
  const [loadingMoveTo, setLoadingMoveTo] = useState(false);
  const [deleteError, setDeleteError] = useState();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentDataPage, setCurrentDataPage] = useState(1);
  const recordsPerPage = 8;
  // const totalPages = Math.ceil(forms.length / recordsPerPage);
  const indexOfLastRecord = currentDataPage * recordsPerPage;
  const indexOfFirstRecord = indexOfLastRecord - recordsPerPage;
  const currentForms = forms.slice(indexOfFirstRecord, indexOfLastRecord);
  // const paginate = (pageNumber) => setCurrentDataPage(pageNumber);

  const workspaceOptionsRef = useRef();
  const formOptionRef = useRef();
  const mainContext = useMainContext();

  const navigate = useNavigate();

  const [messageApi, contextHolder] = message.useMessage();

  const auth = useAuth();

  const listForms = useCallback(async () => {
    try {
      const workspaceId = mainContext.selectedWorkspace.id;
      const response = await listByWorkspace(workspaceId);
      if (response.status === 200) {
        setForms(response.data);
        setTempForms(response.data);
      }
    } catch (err) {
      console.log("Getting forms by workspace error : ", err);
    } finally {
    }
  }, [mainContext.selectedWorkspace.id]);

  const sortFormItems = (option) => {
    const tempForms = [...forms];

    if (option === "createdDate") {
      tempForms.sort(
        (a, b) =>
          parseDate(a.createdDate, false) - parseDate(b.createdDate, false)
      );
    } else if (option === "updatedDate") {
      tempForms.sort(
        (a, b) =>
          parseDate(a.updatedDate, false) - parseDate(b.updatedDate, false)
      );
    } else if (option === "titleAZ") {
      tempForms.sort((a, b) => a.name.localeCompare(b.name));
    } else if (option === "titleZA") {
      tempForms.sort((a, b) => b.name.localeCompare(a.name));
    }

    setForms(tempForms);
  };

  useEffect(() => {
    async function listFormsByWorkspace() {
      await listForms();
    }

    listFormsByWorkspace();
  }, [setForms, processed, mainContext.selectedWorkspace, listForms]);

  useEffect(() => {
    async function listWorkspacesByUser() {
      await listWorkspaces();
    }
    listWorkspacesByUser();
  }, [setWorkspaces, processed]);

  useEffect(() => {
    sortFormItems();
  }, [setForms, sortFormItems]);

  useEffect(() => {
    if (searchTerm === "") {
      setForms(tempForms);
    } else {
      const filteredForms = forms.filter((form) =>
        form.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setForms(filteredForms);
    }
  }, [searchTerm]);

  useEffect(() => {
    async function listFormsByWorkspace() {
      await listForms();
    }

    listFormsByWorkspace();
  }, [setForms, processed, mainContext.selectedWorkspace, listForms]);

  const listWorkspaces = async () => {
    try {
      const response = await getAllWorkspaces();
      if (response.status === 200) {
        setWorkspaces(response.data);
      }
    } catch (err) {
      console.log("Getting workspaces error : ", err);
    } finally {
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleOutsideClicks);
    return () => {
      document.removeEventListener("mousedown", handleOutsideClicks);
    };
  }, [isExpandedWorkspaceOptions]);

  const handleOutsideClicks = (event) => {
    if (
      event.target.className !== "workspace-top-title-action-icon" &&
      event.target.className.baseVal !==
        "workspace-top-title-action-icon-svg" &&
      event.target.className.baseVal !== "" &&
      workspaceOptionsRef &&
      workspaceOptionsRef.current &&
      !workspaceOptionsRef.current.contains(event.target)
    ) {
      setExpandedWorkspaceOptions(false);
    }
  };

  const handleWorkspaceAction = () => {
    setExpandedWorkspaceOptions(!isExpandedWorkspaceOptions);
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleOutsideFormActionClicks);
    return () => {
      document.removeEventListener("mousedown", handleOutsideFormActionClicks);
    };
  }, [expandedForm]);

  const handleOutsideFormActionClicks = (event) => {
    if (
      event.target.className !== "workspace-form-menu-action" &&
      event.target.className.baseVal !== "workspace-form-menu-action-item" &&
      event.target.className.baseVal !== "workspace-form-menu-action-item" &&
      event.target.className.baseVal !==
        "workspace-form-menu-action-item-icon" &&
      event.target.className !== "gtx-trans-icon" &&
      event.target.className !== "jfk-bubble gtx-bubble" &&
      event.target.className !== "ant-modal-content" &&
      event.target.className !== "ant-modal-title" &&
      event.target.className !== "ant-modal-content" &&
      event.target.className !== "ant-modal-wrap ant-modal-centered" &&
      event.target.className !== "renameFormModal-container" &&
      event.target.className !== "modalOkButton-container" &&
      event.target.className !== "modalOkButton-container-title" &&
      event.target.className !== "renameFormModal-input" &&
      event.target.className !== "renameFormModal-button-container" &&
      formOptionRef &&
      formOptionRef.current &&
      !formOptionRef.current.contains(event.target)
    ) {
      setTimeout(() => {
        setExpandedForm(null);
      }, 100);
    }
  };

  const createWorkspace = async (workspaceName) => {
    try {
      const response = await saveWorkspace({
        name: workspaceName,
        isDefault: false,
        organization: mainContext.selectedOrganization,
      });
      if (response.status === 200) {
        await listWorkspaces();
        mainContext.saveWorkspace(response.data);
      } else {
        setTimeout(() => {
          messageApi.open({
            type: "error",
            content: "An error occurred while creating the workspace.",
          });
        }, 200);
      }
    } catch (err) {
      console.log("err : ", err);
      if (err.response.status === 409) {
        /*
        setCreateModalError(
          "You have already defined a workspace with this name."
        );
        */
      }
    } finally {
    }
  };

  const renameWorkspace = async (name) => {
    try {
      setProcessed(true);
      const response = await renameWorkspaceById(
        mainContext.selectedWorkspace.id,
        name
      );
      if (response.status === 200) {
        mainContext.saveWorkspace(response.data);
        setTimeout(() => {
          messageApi.open({
            type: "success",
            content: "Renamed workspace.",
          });
        }, 100);
      } else {
        setTimeout(() => {
          messageApi.open({
            type: "error",
            content: "An error occurred while renaming the workspace.",
          });
        }, 200);
      }
    } catch (err) {
      console.log("Renaming workspace error : ", err);
    } finally {
      setOpenedRenameWorkspaceModal(false);
      setProcessed(false);
    }
  };

  const deleteWorkspace = async (id) => {
    try {
      setLoadingDeleteWorkspace(true);
      setProcessed(true);
      const response = await deleteWorkspaceById(
        mainContext.selectedWorkspace.id
      );
      if (response.status === 200) {
        const tempWorkspaces = workspaces;
        const deletedWorkspaceIndex = workspaces.indexOf(response.data);
        tempWorkspaces.splice(deletedWorkspaceIndex, 1);
        setWorkspaces(tempWorkspaces);
        setTimeout(() => {
          messageApi.open({
            type: "success",
            content: "Deleted workspace.",
          });
        }, 100);
        mainContext.saveWorkspace(workspaces[0]);
      } else {
        setTimeout(() => {
          messageApi.open({
            type: "error",
            content: "An error occurred while deleting the workspace.",
          });
        }, 200);
      }
    } catch (err) {
      console.log("Deleting workspace error : ", err);
    } finally {
      setLoadingDeleteWorkspace(false);
      setOpenedDeleteWorkspaceModal(false);
      setProcessed(false);
    }
  };

  const viewForm = () => {
    window.open(`${process.env.REACT_APP_FORM_APP_URL}/${expandedForm.id}`);
    // navigate(`/${expandedForm.id}`);
  };

  const editForm = () => {
    console.log("expandedForm : ", expandedForm);
    navigate(
      `/workspace/${mainContext.selectedWorkspace.uniqueId}/form/${expandedForm.id}`,
      { state: { formId: expandedForm.id } }
    );
  };

  const copyLink = () => {
    navigator.clipboard.writeText(
      `${process.env.REACT_APP_FORM_APP_URL}/${expandedForm.id}`
    );
    messageApi.open({
      type: "success",
      content: "Link copied",
    });
  };

  const share = () => {
    navigate(
      `/workspace/${mainContext.selectedWorkspace.uniqueId}/form/${expandedForm.id}/share`,
      {
        state: { formId: expandedForm.id },
      }
    );
  };

  const rename = async (name) => {
    try {
      setProcessed(true);
      setLoadingRename(true);
      const response = await renameFormById(expandedForm.id, name);
      if (response.status === 200) {
        setTimeout(() => {
          messageApi.open({
            type: "success",
            content: "Updated form name.",
          });
        }, 100);
      } else {
        messageApi.open({
          type: "error",
          content: "An error occurred while updating the name of the form.",
        });
      }
    } catch (err) {
      console.log("renaming form error : ", err);
    } finally {
      setProcessed(false);
      setOpenedRenameModal(false);
      setLoadingRename(false);
      setExpandedForm(null);
    }
  };

  const duplicate = async () => {
    try {
      setProcessed(true);
      const response = await duplicateFormById(expandedForm.id);
      if (response.status === 200) {
        setTimeout(() => {
          messageApi.open({
            type: "success",
            content: "Duplicated form.",
          });
        }, 100);
      } else {
        setTimeout(() => {
          messageApi.open({
            type: "error",
            content: "An error occurred while duplicating the form.",
          });
        }, 200);
      }
    } catch (err) {
      console.log("Duplicating form error : ", err);
    } finally {
      setProcessed(false);
      setExpandedForm(null);
    }
  };

  const copyTo = async (workspaceId) => {
    try {
      setProcessed(true);
      const formId = expandedForm.id;
      const response = await copyToWorkspaceByIdAndFormId(workspaceId, formId);
      if (response.status === 200) {
        setTimeout(() => {
          messageApi.open({
            type: "success",
            content: "Copied form.",
          });
        }, 200);
      } else {
        setTimeout(() => {
          messageApi.open({
            type: "error",
            content: "An error occurred while copyinhg the form.",
          });
        }, 200);
      }
    } catch (err) {
      console.log("copying to workspace error : ", err);
    } finally {
      setProcessed(false);
      setOpenedCopyToFormModal(false);
    }
  };

  const moveTo = async (workspaceId) => {
    try {
      setLoadingMoveTo(true);
      setProcessed(true);
      const formId = expandedForm.id;
      const response = await moveToWorkspaceByIdAndFormId(workspaceId, formId);
      if (response.status === 200 && response.data === true) {
        messageApi.open({
          type: "success",
          content: "Moved form.",
        });
      } else {
        setTimeout(() => {
          messageApi.open({
            type: "error",
            content: "An error occurred while moving the form.",
          });
        }, 200);
      }
    } catch (err) {
      console.log("moving to workspace error : ", err);
    } finally {
      setLoadingMoveTo(false);
      setProcessed(false);
      setOpenedMoveToFormModel(false);
    }
  };

  const deleteForm = async () => {
    try {
      setLoadingDelete(true);
      setProcessed(true);
      const response = await deleteFormById(expandedForm.id);
      if (response.status === 200) {
        const updatedWorkspaces = workspaces.map((workspace) => {
          if (workspace.id === mainContext.selectedWorkspace.id) {
            return {
              ...workspace,
              formsCount:
                workspace.formsCount > 0 ? workspace.formsCount - 1 : 0,
            };
          }
          return workspace;
        });

        setWorkspaces(updatedWorkspaces);

        setTimeout(() => {
          messageApi.open({
            type: "success",
            content: "Deleted form.",
          });
        }, 100);
      } else {
        setTimeout(() => {
          messageApi.open({
            type: "error",
            content: "An error occurred while deleting the form.",
          });
        }, 200);
      }
    } catch (err) {
      console.log("Deleting form error : ", err);
    } finally {
      setLoadingDelete(false);
      setOpenedDeleteModal(false);
      setExpandedForm(null);
      setProcessed(false);
    }
  };

  const goToResultPage = () => {
    navigate(
      `/workspace/${mainContext.selectedWorkspace.uniqueId}/form/${expandedForm.id}/results`,
      { state: { formId: expandedForm.id } }
    );
  };

  /*
  if (loading) {
    return <SmallLoading />;
  }
    */

  const formActionMenu = () => {
    return (
      <>
        <div ref={formOptionRef} className="workspace-form-menu-action">
          <div className="workspace-form-menu-action-item" onClick={viewForm}>
            <div className="workspace-form-menu-action-item-icon">
              <MdOutlineRemoveRedEye />
            </div>
            <div className="workspace-form-menu-action-item-title">View</div>
          </div>
          <div
            className="workspace-form-menu-action-item"
            onClick={() => editForm()}
          >
            <div className="workspace-form-menu-action-item-icon">
              <MdOutlineEditNote />
            </div>
            <div className="workspace-form-menu-action-item-title">Edit</div>
          </div>
          <div className="workspace-form-menu-action-item" onClick={copyLink}>
            <div className="workspace-form-menu-action-item-icon">
              <IoIosLink />
            </div>
            <div className="workspace-form-menu-action-item-title">
              Copy link
            </div>
          </div>
          <div className="workspace-form-menu-action-divider" />
          <div className="workspace-form-menu-action-item" onClick={share}>
            <div className="workspace-form-menu-action-item-icon">
              <GoShareAndroid />
            </div>
            <div className="workspace-form-menu-action-item-title">Share</div>
          </div>
          <div
            className="workspace-form-menu-action-item"
            onClick={goToResultPage}
          >
            <div className="workspace-form-menu-action-item-icon">
              <MdOutlineLegendToggle />
            </div>
            <div className="workspace-form-menu-action-item-title">Results</div>
          </div>
          <div className="workspace-form-menu-action-divider" />
          <div
            className="workspace-form-menu-action-item"
            onClick={() => {
              setOpenedRenameModal(true);
              setShowExpandedFormActions(false);
            }}
          >
            <div className="workspace-form-menu-action-item-icon">
              <CiEdit />
            </div>
            <div className="workspace-form-menu-action-item-title">Rename</div>
          </div>
          <div
            className="workspace-form-menu-action-item"
            onClick={() => {
              duplicate();
              setShowExpandedFormActions(false);
            }}
          >
            <div className="workspace-form-menu-action-item-icon">
              <IoDuplicateOutline />
            </div>
            <div className="workspace-form-menu-action-item-title">
              Duplicate
            </div>
          </div>
          <div
            className={
              workspaces && workspaces.length > 1
                ? "workspace-form-menu-action-item"
                : "workspace-form-menu-action-item-disabled"
            }
            onClick={
              workspaces && workspaces.length > 1
                ? () => {
                    setOpenedCopyToFormModal(true);
                    setShowExpandedFormActions(false);
                  }
                : () => {}
            }
          >
            <div className="workspace-form-menu-action-item-icon">
              <MdMoveDown />
            </div>
            <div className="workspace-form-menu-action-item-title">Copy to</div>
          </div>
          <div
            className={
              workspaces && workspaces.length > 1
                ? "workspace-form-menu-action-item"
                : "workspace-form-menu-action-item-disabled"
            }
            onClick={
              workspaces && workspaces.length > 1
                ? () => {
                    setOpenedMoveToFormModel(true);
                    setShowExpandedFormActions(false);
                  }
                : () => {}
            }
          >
            <div className="workspace-form-menu-action-item-icon">
              <MdOutlineDriveFileMove />
            </div>
            <div className="workspace-form-menu-action-item-title">Move to</div>
          </div>
          <div className="workspace-form-menu-action-divider" />
          <div
            className="workspace-form-menu-action-item"
            style={{ color: "var(--red100)" }}
            onClick={() => {
              setOpenedDeleteModal(true);
              setShowExpandedFormActions(false);
            }}
          >
            <div className="workspace-form-menu-action-item-icon">
              <MdDeleteOutline />
            </div>
            <div className="workspace-form-menu-action-item-title">Delete</div>
          </div>
        </div>
      </>
    );
  };

  const parseDate = (dateString, formattedDate) => {
    const months = {
      Oca: "Jan",
      Şub: "Feb",
      Mar: "Mar",
      Nis: "Apr",
      May: "May",
      Haz: "Jun",
      Tem: "Jul",
      Ağu: "Aug",
      Eyl: "Sep",
      Eki: "Oct",
      Kas: "Nov",
      Ara: "Dec",
    };

    const [day, month, year, time] = dateString.split(" ");
    const monthEng = months[month]; // Türkçe ay ismini İngilizceye çevirü
    if (formattedDate) {
      return `${year} ${monthEng} ${day} ${time}`;
    }
    return new Date(`${year} ${monthEng} ${day} ${time}`);
  };

  const searchForm = (e) => {
    setSearchTerm(e.target.value);
  };

  const paginationControl = (page) => {
    setCurrentDataPage(page);
  };

  return (
    <>
      {contextHolder}
      <Sidebar
        workspaces={workspaces}
        processed={processed}
        setProcessed={setProcessed}
        createWorkspace={createWorkspace}
      />
      <Navbar />
      <div className="app-container">
        <div className="workspace-top-container">
          <div className="workspace-top-title-container">
            <div className="workspace-top-title-actions">
              <div className="workspace-top-title">
                {mainContext.selectedWorkspace.name}
              </div>
              {auth.authUser.role !== "USER" && (
                <div>
                  <div
                    className="workspace-top-title-action-icon"
                    onClick={handleWorkspaceAction}
                  >
                    <SlOptions className="workspace-top-title-action-icon-svg" />
                  </div>

                  {isExpandedWorkspaceOptions && (
                    <div
                      ref={workspaceOptionsRef}
                      className="workspace-action-options"
                    >
                      <div
                        className="workspace-action-option"
                        onClick={() => {
                          setOpenedRenameWorkspaceModal(true);
                          setExpandedWorkspaceOptions(false);
                        }}
                      >
                        <div className="workspace-action-option-icon">
                          <CiEdit />
                        </div>
                        <div className="workspace-action-option-title">
                          Rename
                        </div>
                      </div>
                      <div
                        className="workspace-action-option"
                        onClick={() => navigate("/members")}
                      >
                        <div className="workspace-action-option-icon">
                          <AiOutlineUsergroupAdd />
                        </div>
                        <div className="workspace-action-option-title">
                          Invite User
                        </div>
                      </div>

                      <div
                        className="workspace-action-option"
                        onClick={() => {
                          setOpenedDeleteWorkspaceModal(true);
                          setExpandedWorkspaceOptions(false);
                        }}
                      >
                        <div
                          className="workspace-action-option-icon"
                          style={{ color: "var(--red100" }}
                        >
                          <MdDeleteOutline />
                        </div>
                        <div
                          className="workspace-action-option-title"
                          style={{ color: "var(--red100" }}
                        >
                          Delete
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
          <div className="workspace-top-filter-container">
            <div className="workspace-top-filter-item-icon">
              <BsSortDown />
            </div>
            <select
              className="workspace-top-filter-select"
              onChange={(e) => sortFormItems(e.target.value)}
            >
              <option value="createdDate" key="createdDate">
                Creation Date
              </option>
              <option value="updatedDate" key="updatedDate">
                Last Edit
              </option>
              <option value="titleAZ" key="titleAZ">
                Title [a-z]
              </option>
              <option value="titleZA" key="titleZA">
                Title [z-a]
              </option>
            </select>
            {/*
              <div className="workspace-top-filter-item-title">
                Created date
              </div>
                    */}

            <div className="workspace-top-filter-search">
              <div className="workspace-top-filter-search-icon">
                <CiSearch />
              </div>
              <input
                value={searchTerm}
                placeholder="Search my forms"
                className="workspace-top-filter-search-input"
                onChange={searchForm}
              />
            </div>
          </div>
        </div>
        {forms && forms.length > 0 ? (
          <>
            <Row className="workspace-data-header">
              <Col span={1}></Col>
              <Col span={9}></Col>
              <Col span={3} className="workspace-data-header-item">
                Response
              </Col>
              <Col span={3} className="workspace-data-header-item">
                Creator
              </Col>
              <Col span={3} className="workspace-data-header-item">
                Created Date
              </Col>
              <Col span={4} className="workspace-data-header-item">
                UpdatedDate
              </Col>
              <Col span={1}></Col>
            </Row>

            {currentForms.map((record) => (
              <Row className="workspace-data-container">
                <Col span={1}>
                  <div className="workspace-data-item-icon">
                    <SiGoogleforms />
                  </div>
                </Col>
                <Col span={9}>
                  <div className="workspace-data-item-title">{record.name}</div>
                  <div className="workspace-data-item-title-description">
                    Last edited on {record.updatedDate}
                  </div>
                </Col>
                <Col span={3} className="workspace-data-item">
                  {record.responseCount}
                </Col>
                <Col span={3} className="workspace-data-item">
                  {record.creatorUser}
                </Col>
                <Col span={3} className="workspace-data-item">
                  {record.createdDate}
                </Col>
                <Col span={4} className="workspace-data-item">
                  {record.updatedDate}
                </Col>
                <Col span={1} className="workspace-data-item">
                  <div style={{ position: "relative" }}>
                    <SlOptions
                      className="workspace-form-actions-icon"
                      onClick={() => {
                        setExpandedForm(record);
                        setShowExpandedFormActions(true);
                      }}
                    />
                    {expandedForm?.id === record.id &&
                      showExpandedFormActions &&
                      formActionMenu()}
                  </div>
                </Col>
              </Row>
            ))}

            {forms && forms.length > 8 && (
              <div className="workspace-form-pagination-button-container">
                <Pagination
                  onChange={paginationControl}
                  align="end"
                  defaultCurrent={currentDataPage}
                  pageSize={8}
                  total={forms.length}
                />
              </div>
            )}
          </>
        ) : (
          <div className="workspace-noData-container">
            <div className="workspace-noData-title">
              YOU DON'T HAVE ANY FORMS YET
            </div>
            <div className="workspace-noData-description">
              Your forms will appear here
            </div>
            <div
              className="workspace-noData-createFormButton"
              onClick={() =>
                navigate(
                  `/workspace/${mainContext.selectedWorkspace.uniqueId}/form/select`
                )
              }
            >
              Create a new form
            </div>
          </div>
        )}
      </div>

      <RenameWorkspaceModal
        open={openedRenameWorkspaceModal}
        handleOk={renameWorkspace}
        handleClose={() => setOpenedRenameWorkspaceModal(false)}
        title="Rename this workspace"
        inputValue={
          mainContext.selectedWorkspace.name
            ? mainContext.selectedWorkspace.name
            : ""
        }
        buttonTitle="Save"
        loading={loadingRename}
        error={renameError}
        setError={setRenameError}
      />

      <DeleteModal
        open={openedDeleteWorkspaceModal}
        handleOk={deleteWorkspace}
        handleClose={() => setOpenedDeleteWorkspaceModal(false)}
        title="Delete this workspace"
        message="Deleting this workspace will permanently remove all associated data. "
        message2="This action cannot be undone. "
        message3="Are you sure you want to proceed?"
        buttonTitle="Delete"
        loading={loadingDeleteWorkspace}
        error={deleteError}
        setError={setDeleteError}
      />

      <RenameFormModal
        open={openedRenameModal}
        handleOk={rename}
        handleClose={() => setOpenedRenameModal(false)}
        title="Rename this form"
        inputValue={expandedForm ? expandedForm.name : ""}
        buttonTitle="Save"
        loading={loadingRename}
        error={renameError}
        setError={setRenameError}
      />

      <DeleteModal
        open={openedDeleteModal}
        handleOk={deleteForm}
        handleClose={() => setOpenedDeleteModal(false)}
        title="Delete this form"
        message="Deleting this form will permanently remove all associated data. "
        message2="This action cannot be undone. "
        message3="Are you sure you want to proceed?"
        buttonTitle="Delete"
        loading={loadingDelete}
        error={deleteError}
        setError={setDeleteError}
      />

      <CopyToFormModal
        open={openedCopyToFormModal}
        handleOk={copyTo}
        handleClose={() => setOpenedCopyToFormModal(false)}
        selectedWorkspace={mainContext.selectedWorkspace}
        title="Copy form"
        buttonTitle="Copy"
        loading={loadingMoveTo}
        error={renameError}
        setError={setRenameError}
        options={workspaces.filter(
          (workspace) => workspace.id !== mainContext.selectedWorkspace.id
        )}
      />

      <MoveToFormModal
        open={openedMoveToFormModal}
        handleOk={moveTo}
        handleClose={() => setOpenedMoveToFormModel(false)}
        title="Change workspace"
        inputValue={
          mainContext.selectedWorkspace
            ? {
                label: mainContext.selectedWorkspace.name,
                value: mainContext.selectedWorkspace.id,
              }
            : ""
        }
        buttonTitle="Move"
        loading={loadingMoveTo}
        error={renameError}
        setError={setRenameError}
        options={workspaces}
      />
    </>
  );
};

export default Workspace;
