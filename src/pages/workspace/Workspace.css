.workspace-top-container {
  display: flex;
  justify-content: space-between;
  padding: 0 20px 20px 20px;
  margin-bottom: 5px;
  border-bottom: 1px solid var(--grey200);
}

.workspace-top-title-container {
  font-size: 18px;
  color: var(--text-dark);
  font-weight: 500;
  font-family: "Exo 2", sans-serif;
}

.workspace-top-title-actions {
  display: flex;
  justify-content: center;
  align-items: center;
}

.workspace-top-title {
  display: flex;
  justify-content: center;
  align-items: center;
}

.workspace-top-title-action-icon {
  display: flex;
  justify-content: center;
  align-content: center;
  margin-left: 20px;
  font-size: 16px;
  width: 25px;
  height: 25px;
  padding: 4px;
  cursor: pointer;
}

.workspace-top-title-action-icon:hover {
  background-color: var(--grey300);
  border-radius: 3px;
}

.workspace-action-options {
  position: absolute;
  margin-left: 25px;
  background-color: var(--white);
  border-radius: 5px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 4px 0px;
  transition: all 0.2s ease-in;
  transform: scale(1);
  width: 150px;
  padding: 10px;
  border: 1px solid var(--grey100);
  z-index: 999;
}

.workspace-action-option {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 4px;
  padding: 6px 12px 6px 12px;
  cursor: pointer;
}

.workspace-action-option:hover {
  background-color: var(--grey300);
  border-radius: 5px;
}

.workspace-action-option-icon {
  font-size: 16px;
  margin-right: 8px;
  color: var(--text-dark);
}

.workspace-action-option-title {
  font-size: 14px;
  font-weight: 400;
  color: var(--text-dark);
  font-family: "Exo 2", sans-serif;
}

.workspace-top-filter-container {
  display: flex;
  align-items: center;
  margin-top: -4px;
}

.workspace-top-filter-select {
  padding: 5px 10px 5px 35px;
  background-color: var(--white);
  width: 180px;
  margin-left: -25px;
  margin-right: 20px;
  border-radius: 5px;
  border: 1px solid var(--grey300);
}

.workspace-top-filter-select::after {
  border-width: 0 !important;
}

.workspace-top-filter-select::selection {
  border-width: 0 !important;
}

.workspace-top-filter-select:active {
  border-width: 0 !important;
}

.workspace-top-filter-select:checked {
  border-width: 0 !important;
}

.workspace-top-filter-select:visited {
  border-width: 0 !important;
}

.workspace-top-filter-item-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0px 0px 0 10px;
  border-radius: 5px;
  cursor: pointer;
  color: var(--text-dark);
  min-width: 180px;
  height: 35px;
  position: relative;
  background-color: var(--white);
  border: 1px solid var(--grey200);
}

.workspace-top-filter-item-container:hover {
  transition: 0.15s all;
  /*background-color: var(--grey300);*/
}

.workspace-top-filter-item-icon {
  z-index: 999;
  font-size: 20px;
  color: var(--text-dark);
}

.workspace-top-filter-item-title {
  font-size: 15px;
  color: var(--text-dark);
}

.workspace-top-filter-search {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.workspace-top-filter-search-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: -35px;
  right: 220px;
  z-index: 1;
}

.workspace-top-filter-search-input {
  position: relative;
  padding: 8px 0px 8px 30px;
  border: 1px solid var(--grey200);
  color: var(--text-dark);
  margin-left: 10px;
  border-radius: 5px;
  width: 280px;
  height: 35px;
  background-color: var(--white);
}

.workspace-data-header {
  margin-top: 20px;
  padding: 10px;
}

.workspace-data-header-item {
  font-weight: 500;
}

.workspace-data-container {
  padding: 15px 10px;
  background-color: var(--white);
  border: 1px solid var(--grey300);
  border-radius: 5px;
  margin-bottom: 10px;
}

.workspace-data-container:hover {
  background-color: #f1f1f1;
  transition: 0.3s all;
}

.workspace-data-item-icon {
  font-size: 32px;
  color: #51566d;
  margin-right: 15px;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.workspace-data-item-title {
  font-size: 17px;
  font-weight: 600;
  color: var(--text-dark);
  font-family: "Exo 2", sans-serif;
  font-weight: 600;
  font-size: 16px;
}

.workspace-data-item-title-description {
  font-family: "Exo 2", sans-serif;
  opacity: 0.7;
  font-weight: 500;
}

.workspace-data-item {
  display: flex;
  align-items: center;
  font-weight: 400;
}

.workspace-data-responseCount {
  text-align: center;
  background-color: #ffebd7;
  padding: 2px 10px;
  border: 1px solid var(--orange200);
  font-weight: 600;
  border-radius: 3px;
}

.workspace-form-pagination-button-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 5px;
}

.workspace-form-pagination-button {
  padding: 10px;
  background-color: var(--black);
  color: var(--white);
  border-radius: 5px;
  margin-left: 20px;
  cursor: pointer;
}

.workspace-form-pagination-button:hover {
  opacity: 0.8;
}

.workspace-noData-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 70%;
}

.workspace-noData-title {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 24px;
  margin-bottom: 20px;
  color: var(--black);
}

.workspace-noData-description {
  font-family: "Exo 2", sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: var(--black);
  opacity: 0.8;
  margin-bottom: 40px;
}

.workspace-noData-createFormButton {
  background-color: var(--black);
  border: 1px solid var(--black);
  color: var(--white);
  padding: 8px 40px;
  font-family: "Exo 2", sans-serif;
  font-weight: 600;
  font-size: 16px;
  border-radius: 5px;
  cursor: pointer;
}

.workspace-noData-createFormButton:hover {
  opacity: 0.8;
  transition: 0.3s all;
}

.workspace-table-container {
  padding: 15px;
  border-radius: 10px;
  /*box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 4px 0px;*/
}

.workspace-table {
  width: 100%;
  padding: 15px;
}

.workspace-table thead th {
  font-size: 15px;
  font-weight: 400;
  font-family: "Exo 2", sans-serif;
  color: var(--text-dark);
  text-align: start;
  padding: 15px;
}

.workspace-table-item-container {
  border-radius: 10px;
  border-radius: 30px !important;
  background-color: var(--white);
}

.workspace-table-item-container:hover {
  background-color: var(--grey300);
  /*
    border: 1px solid var(--black);
    border-bottom: 3px solid var(--black);
    */
  cursor: pointer;
}

.workspace-table-item-container {
  margin-bottom: 10px;
}

.workspace-table-item-container td {
  text-align: start;
}

.workspace-table-item-container td:first-child {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.workspace-table-item-container td:last-child {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.workspace-table-item-container td {
  padding: 15px;
}

.workspace-form-table-item-icon {
  font-size: 32px;
  color: #51566d;
  margin-right: 15px;
  border-radius: 5px;
}

.workspace-table-item-title {
  font-size: 17px;
  font-weight: 600;
  color: var(--text-dark);
}

.workspace-table-item-explanation {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-dark);
  opacity: 0.8;
}

.workspace-table-item-firstName {
  font-family: "Exo 2", sans-serif;
  font-weight: 400;
  font-size: 15px;
  color: var(--text-dark);
  opacity: 0.9;
}

.workspace-table-item-lastName {
  font-family: "Exo 2", sans-serif;
  font-weight: 400;
  font-size: 15px;
  color: var(--text-dark);
  opacity: 0.9;
}

.workspace-table-item-updDate {
  font-family: "Exo 2", sans-serif;
  font-weight: 400;
  font-size: 15px;
  color: var(--text-dark);
  opacity: 0.9;
}

.workspace-table-item-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

table tbody tr {
  border-radius: 10px !important;
  border-top-left-radius: 10px;
}

.workspace-form-actions-icon {
  font-size: 22px;
  padding: 4px;
  cursor: pointer;
}

.workspace-form-actions-icon:hover {
  background-color: var(--grey300);
  border-radius: 3px;
}

.workspace-form-menu-action {
  min-width: 220px;
  height: 425px;
  position: absolute;
  background-color: var(--white);
  border: 1px solid var(--grey100);
  padding: 10px 15px;
  border-radius: 5px;
  z-index: 999;
  top: 0;
  right: 25px;
  bottom: calc(100vh - 425px);
}

.workspace-form-menu-action-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 6px 12px 6px 12px;
}

.workspace-form-menu-action-item:hover {
  background-color: var(--grey300);
  border-radius: 5px;
}

.workspace-form-menu-action-item-disabled {
  display: flex;
  align-items: center;
  cursor: default;
  padding: 6px 12px 6px 12px;
  color: #919191;
}

.workspace-form-menu-action-item-icon {
  margin-right: 10px;
  font-size: 16px;
}

.workspace-form-menu-action-item-title {
  font-size: 14px;
  font-family: "Exo 2", sans-serif;
}

.workspace-form-menu-action-divider {
  border-bottom: 1px solid var(--grey300);
  margin: 10px 0;
}

.ant-table-thead > tr > th {
  font-family: "Exo 2", sans-serif;
  font-weight: 500 !important;
}

.ant-table-wrapper .ant-table-tbody > tr > td {
  font-family: "Exo 2", sans-serif;
}
