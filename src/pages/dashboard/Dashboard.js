import { useNavigate } from "react-router-dom";
import "./Dashboard.css";
import { useAuth } from "../../context/AuthContext";
import { useEffect } from "react";

const Dashboard = () => {
  const { authUser } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    console.log("authUser : ", authUser);
    if (authUser) {
      navigate(`/workspace/${authUser.organization.defaultWorkspace.uniqueId}`);
    } else {
      navigate("/login");
    }
  }, [authUser, navigate]);
};

export default Dashboard;
