import "./ViewForm.css";
import { useEffect, useState } from "react";
import SmallLoading from "../../components/UI/Loading/SmallLoading";
import { useParams } from "react-router-dom";
import { getFormById, getThemeById } from "../../services/http";
import FForm from "../fform/FForm";

const ViewForm = () => {
  const { id } = useParams();

  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState();
  const [theme, setTheme] = useState();

  useEffect(() => {
    // Asenkron fonksiyon tanımlanması dışarıda
    const findForm = async () => {
      setLoading(true); // Yükleme başladığında true yapalım
      try {
        const response = await getFormById(id);
        if (response.status === 200) {
          setForm(response.data);
          if (response.data.themeId) {
            await findByThemeById(response.data.themeId);
          }
        }
      } catch (err) {
        console.log("Getting form by id error : ", err);
      } finally {
        setLoading(false); // Yükleme bittiğinde false yapalım
      }
    };
    findForm();
  }, [id]);

  const findByThemeById = async (themeId) => {
    try {
      setLoading(true);
      const response = await getThemeById(themeId);
      if (response.status === 200) {
        setTheme(response.data);
      }
    } catch (err) {
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <SmallLoading />;
  }

  return (
    <div className="viewForm-container">
      {form && (
        <FForm
          form={form}
          theme={theme}
          isPreview={false}
          isOpen={true}
          perspective="desktop"
        />
      )}
    </div>
  );
};

export default ViewForm;
