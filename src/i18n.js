/*import i18n from "i18next";
import { initReactI18next } from "react-i18next";

i18n.use(initReactI18next).init({
  resources: {
    en: {
      translations: {
        "Welcome back": "Welcome back",
      },
    },
    tr: {
      translations: {
        "Welcome back": "<PERSON>kra<PERSON>geldiniz",
      },
    },
  },
  fallbackLng: "tr",
  ns: ["translations"],
  defaultNS: "translations",
  keySeparator: false,
  interpolation: {
    escapeValue: false,
    formSeparator: ",",
  },
  react: {
    wait: true,
  },
});

export default i18n;
*/
