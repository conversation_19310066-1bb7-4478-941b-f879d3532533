import api from "./api";

const BASE_URL = "http://localhost:8080/api/v1";

const getHeaderConfig = () => {
  const auth = JSON.parse(localStorage.getItem("auth"));
  const authToken = auth?.token;
  if (!authToken) {
    localStorage.clear();
    window.location.href = "/login";
  }
  let headerConfig = {
    headers: {
      Authorization: `Bearer ${authToken}`,
      "Content-Type": "application/json",
    },
  };
  return headerConfig;
};

/* --- AUTHENTICATION --- */
export function registerUser(body) {
  return api.post(`${BASE_URL}/auth/register`, body);
}

export function authenticateUser(body) {
  return api.post(`${BASE_URL}/auth/login`, body);
}

export function authenticateGoogleUser(body) {
  return api.post(`${BASE_URL}/auth/googleLogin`, body);
}

/*
export function resetPassword(email) {
  return api.put(`${BASE_URL}/auth/resetPassword/${email}`);
}
  */
/* --- AUTHENTICATION END --- */

/* --- VERIFY --- */
export function resendVerify(body) {
  return api.post(`${BASE_URL}/verify/resend`, body);
}

export function verifyUser(verifyToken) {
  return api.put(`${BASE_URL}/verify/${verifyToken}`);
}
/* --- VERIFY END --- */

/* --- FORGOT PASSWORD --- */
export function forgotPasswordSendMail(email) {
  return api.post(`${BASE_URL}/forgotPassword/sendMail/${email}`);
}

export function checkForgotPasswordEmail(email) {
  return api.get(`${BASE_URL}/forgotPassword/check/${email}`);
}

export function resetPassword(email, password) {
  return api.post(
    `${BASE_URL}/forgotPassword/resetPassword/${email}/${password}`
  );
}
/* --- FORGOT PASSWORD END --- */

/* --- USER --- */
export function saveMemberByUser(body) {
  return api.post(`${BASE_URL}/user/member`, body, getHeaderConfig());
}

export function getMembersByOrganization(organizationId) {
  return api.get(
    `${BASE_URL}/user/member/organization/${organizationId}`,
    getHeaderConfig()
  );
}

export function getUserByEmail(email) {
  return api.get(`${BASE_URL}/user/${email}`, getHeaderConfig());
}

export function updateName(body) {
  return api.put(`${BASE_URL}/user/updateName`, body, getHeaderConfig());
}

export function updateEmail(token) {
  return api.put(`${BASE_URL}/user/updateEmail`, { token }, getHeaderConfig());
}

export function updatePassword(token) {
  return api.put(
    `${BASE_URL}/user/updatePassword`,
    { token },
    getHeaderConfig()
  );
}

export function deleteMemberById(id) {
  return api.delete(`${BASE_URL}/user/member/${id}`, getHeaderConfig());
}
/* --- USER END ---*/

/* --- CONFIRMATION CODE ---*/
export function sendChangeEmailConfirmationCode(email) {
  return api.post(
    `${BASE_URL}/confirmation/changeEmail/${email}`,
    {},
    getHeaderConfig()
  );
}

export function sendChangePasswordConfirmationCode(body) {
  return api.post(
    `${BASE_URL}/confirmation/changePassword`,
    body,
    getHeaderConfig()
  );
}

export function sendConfirmConfirmationCode(code, email) {
  return api.get(
    `${BASE_URL}/confirmation/sendConfirm/code/${code}/email/${email}`,
    getHeaderConfig()
  );
}

/* --- END CONFIRMATION CODE ---*/

/* --- WORKSPACE --- */
export function saveWorkspace(body) {
  return api.post(`${BASE_URL}/workspace`, body, getHeaderConfig());
}

export function getAllWorkspaces() {
  return api.get(`${BASE_URL}/workspace`, getHeaderConfig());
}

export function renameWorkspaceById(id, name) {
  return api.put(`${BASE_URL}/workspace/rename/${id}`, name, getHeaderConfig());
}

export function moveToWorkspaceByIdAndFormId(workspaceId, formId) {
  return api.get(
    `${BASE_URL}/workspace/moveTo/workspace/${workspaceId}/form/${formId}`,
    getHeaderConfig()
  );
}

export function deleteWorkspaceById(id) {
  return api.delete(`${BASE_URL}/workspace/${id}`, getHeaderConfig());
}
/* --- END WORKSPACE ---*/

/* --- THEME --- */
export function saveTheme(body) {
  return api.post(`${BASE_URL}/themes`, body, getHeaderConfig());
}

export function getThemesByOrganization(organizationId) {
  console.log("organizationId : ", organizationId);
  return api.get(
    `${BASE_URL}/themes/organization/${organizationId}`,
    getHeaderConfig()
  );
}

export function getThemeById(id) {
  return api.get(`${BASE_URL}/themes/${id}`, getHeaderConfig());
}

export function deleteThemeById(id) {
  return api.delete(`${BASE_URL}/themes/${id}`, getHeaderConfig());
}
/* --- END THEME --- */

/* --- FORM --- */
export function initializeForm(body) {
  return api.post(`${BASE_URL}/forms/initialize`, body, getHeaderConfig());
}

export function saveForm(body) {
  return api.post(`${BASE_URL}/forms`, body, getHeaderConfig());
}

export function updateForm(body) {
  return api.put(`${BASE_URL}/forms`, body, getHeaderConfig());
}

export function publishForm(id) {
  return api.put(`${BASE_URL}/forms/publish/${id}`, {}, getHeaderConfig());
}

export function updateField(body) {
  return api.put(`${BASE_URL}/forms/field`, body, getHeaderConfig());
}

export function updateOptions(body) {
  return api.put(`${BASE_URL}/forms/options`, body, getHeaderConfig());
}

export function listByWorkspace(workspaceId) {
  return api.get(
    `${BASE_URL}/forms/listByWorkspace/${workspaceId}`,
    getHeaderConfig()
  );
}

export function getFormById(formId) {
  return api.get(`${BASE_URL}/forms/${formId}`, getHeaderConfig());
}

export function sendMailForForm(body) {
  return api.post(`${BASE_URL}/forms/sendEmail`, body, getHeaderConfig());
}

export function renameFormById(id, name) {
  return api.put(`${BASE_URL}/forms/rename/${id}`, name, getHeaderConfig());
}

export function duplicateFormById(id) {
  return api.post(`${BASE_URL}/forms/duplicate/${id}`, {}, getHeaderConfig());
}

export function copyToWorkspaceByIdAndFormId(workspaceId, formId) {
  return api.get(
    `${BASE_URL}/forms/copyTo/workspace/${workspaceId}/form/${formId}`,
    getHeaderConfig()
  );
}

export function deleteFormById(id) {
  return api.delete(`${BASE_URL}/forms/${id}`, getHeaderConfig());
}

export function deletePageById(id) {
  return api.delete(`${BASE_URL}/forms/deletePage/${id}`, getHeaderConfig());
}

export function deleteFieldById(id) {
  return api.delete(`${BASE_URL}/forms/deleteField/${id}`, getHeaderConfig());
}

export function getHeadersById(formId) {
  return api.get(`${BASE_URL}/forms/headers/${formId}`, getHeaderConfig());
}

export function updateFormHeaders(formHeaders) {
  return api.put(
    `${BASE_URL}/forms/updateHeaders`,
    formHeaders,
    getHeaderConfig()
  );
}

export function deleteExternalPageById(formId, externalId) {
  return api.delete(
    `${BASE_URL}/forms/externalPage/form/${formId}/external/${externalId}`,
    getHeaderConfig()
  );
}

/* --- END FORM --- */

/* TRACKING */
export function getTrackingByFormId(formId) {
  return api.get(
    `${BASE_URL}/tracking/listByForm/${formId}`,
    getHeaderConfig()
  );
}
/* --- EBD TRACKING --- */

/* RESPONSE */
export function getResponsesByFormId(formId) {
  return api.get(
    `${BASE_URL}/response/listByForm/${formId}`,
    getHeaderConfig()
  );
}

export function getResponseById(id) {
  return api.get(`${BASE_URL}/response/${id}`, getHeaderConfig());
}

export function deleteResponseById(id) {
  return api.delete(`${BASE_URL}/response/${id}`, getHeaderConfig());
}

/* --- END RESPONSE --- */
