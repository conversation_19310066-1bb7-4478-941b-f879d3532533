@media print {
    body * {
      visibility: hidden;
    }

    #printArea, #printArea * {
      visibility: visible;
    }
  
    #printArea {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      background-color: red;
    }

    .reponse-selectedResponse-item {
        margin-bottom: 30px;
    }
    
    .response-selectedResponse-header-container {
        display: flex;
        justify-content: space-between;
        width: 500px;
    }
    
    .response-selectedResponse-header-date-container {
        display: flex;
        align-items: center;
        margin-left: -10px;
    }
    
    .response-selectedResponse-header-selections {
        display: flex;
        align-items: center;
        margin-right: 10px;
    }
    
    .response-selectedResponse-header-selection {
        padding: 5px;
        font-size: 16px;
        background-color: var(--white);
        border: 1px solid var(--grey100);
        border-radius: 3px;
        margin-right: 5px;
        cursor: pointer;
    }
    
    .response-selectedResponse-header-selection:hover {
        background-color: var(--grey200);
        transition: .3s all;
    }
    
    .response-selectedResponse-header-date {
        font-family: "Exo 2", sans-serif;
        font-size: 14px;
    }
    
    .response-selectedResponse-header-action-container {
        display: flex;
        align-items: center;
        margin-right: 20px;
    }
    
    .response-selectedResponse-print {
        display: flex;
        align-items: center;
        padding: 5px;
        background-color: var(--grey300);
        border: 1px solid var(--grey200);
        border-radius: 3px;
        margin-right: 8px;
        cursor: pointer;
    }
    
    .response-selectedResponse-print:hover {
        opacity: .7;
        transition: .3s all;
    }
    
    .response-selectedResponse-print-icon {
        font-size: 16px;
    }
    
    .response-selectedResponse-print-title {
        font-family: "Exo 2", sans-serif;
        font-size: 13px;
    }
    
    .response-selectedResponse-delete {
        display: flex;
        align-items: center;
        padding: 5px;
        background-color: var(--red200);
        border: 1px solid var(--red100);
        color: var(--white);
        border-radius: 3px;
        cursor: pointer;
    }
    
    .response-selectedResponse-delete:hover {
        opacity: .7;
        transition: .3s all;
    }
    
    .response-selectedResponse-delete-icon {
        font-size: 16px;
    }
    
    .response-selectedResponse-delete-title {
        font-family: "Exo 2", sans-serif;
        font-size: 13px;
    }
    
    .reponse-selectedResponse-item-innerContainer {
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }
    
    .reponse-selectedResponse-item-icon {
        font-size: 16px;
        padding: 3px;
        border: 1px solid var(--grey200);
        border-radius: 5px;
        margin-right: 15px;
    }
    
    .reponse-selectedResponse-item-title {
        font-family: "Exo 2", sans-serif;
        font-size: 13px;
        opacity: .7;
    }
    
    .response-selectedResponse-item-multiple-answer {
        padding: 0px 5px;
        margin: 5px 0 5px 40px;
        border: 1px solid var(--grey100);
        border-radius: 5px;
        width: fit-content;
    }
    
    .response-selectedResponse-item-answer {
        padding: 5px 0 0 40px;
    }
  }